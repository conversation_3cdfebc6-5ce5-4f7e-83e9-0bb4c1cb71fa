package skynet.platform.feign.exception;

import skynet.boot.exception.SkynetException;

/**
 * X<PERSON>ger api 接口 异常类
 *
 * <AUTHOR> by jianwu6 on 2020/7/31 11:39
 */
public class ApiRequestException extends SkynetException {


    private final ApiRequestErrorCode errorCode;

    public ApiRequestException(ApiRequestErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getValue());
        this.errorCode = errorCode;
    }


    public int getCode() {
        return errorCode == null ? 0 : errorCode.getCode();
    }

    @Override
    public String getMessage() {
        return errorCode == null ? "" : errorCode.getValue();
    }

    @Override
    public String toString() {
        return String.format("exception code:%d, message:%s", errorCode.getCode(), errorCode.getValue());
    }
}
