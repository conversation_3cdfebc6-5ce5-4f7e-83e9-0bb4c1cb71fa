package skynet.platform.feign.annotation;

import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import skynet.platform.feign.config.ApiAuthConfiguration;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({ApiAuthConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "skynet.platform.feign.service")
public @interface EnableSkynetPlatformFeign {
}
