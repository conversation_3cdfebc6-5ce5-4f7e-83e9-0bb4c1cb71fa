package skynet.platform.feign.service;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.ActionDefinitionDto;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.model.NodeDescriptionDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.io.IOException;
import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3ActionDefinition")
@Tag(name = "v3. 服务定义管理", description = "服务定义增删改查")//, hidden = true)
public interface V3ActionDefinition {

    String PREFIX = "/skynet/api/v3/actions/definition";

    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取服务定义列表")
        //@ApiResponse(code = 200, message = "成功")
    GetDefinitionsResponse getDefinitions(@Parameter(description = "过滤条件：系统编码")
                                          @RequestParam(required = false, name = "pluginCode") String pluginCode,
                                          @Parameter(description = "过滤条件：功能标签编码")
                                          @RequestParam(required = false, name = "actionLabelCode") String actionLabelCode) throws Exception;

    @GetMapping(value = PREFIX + "/{actionPoint}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取服务定义")
        //@ApiResponse(code = 200, message = "成功")
    GetDefinitionResponse getDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint) throws IOException;

    @PostMapping(value = PREFIX)
    @Operation(summary = "新建服务定义")
        //@ApiResponse(code = 200, message = "成功")
    NoDataResponse createDefinition(@RequestBody ActionDefinitionDto request);

    @DeleteMapping(value = PREFIX + "/{actionPoint}")
    @Operation(summary = "删除服务定义")
        //@ApiResponse(code = 200, message = "成功")
    NoDataResponse deleteDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint);


    @PutMapping(value = PREFIX + "/{actionPoint}/archive")
    @Operation(summary = "归档服务定义")
    NoDataResponse archiveDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint);


    @PutMapping(value = PREFIX + "/{actionPoint}/restore")
    @Operation(summary = "恢复服务定义")
    NoDataResponse restoreDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint);


    @PutMapping(value = PREFIX + "/{actionPoint}")
    @Operation(summary = "更新服务定义")
    NoDataResponse updateDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint,
                                    @RequestBody ActionDefinitionDto request);

    @PostMapping(value = PREFIX + "/import", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "服务定义导入")
    NoDataResponse importDefinition(@RequestPart("file") MultipartFile file);

    /**
     * 服务定义导出(ZkUI兼容的文本文件)
     *
     * @param codes 编码列表,支持系统编码和服务编码，后台智能判断
     * @return
     */
    @GetMapping(value = PREFIX + "/export", produces = {MediaType.APPLICATION_JSON_VALUE})
    ResponseEntity<StreamingResponseBody> exportDefs(@RequestParam String[] codes);

    @PostMapping(value = PREFIX + "/list")
    @Operation(summary = "获取Action列表描述")
        //@ApiResponse(code = 200, message = "成功")
    GetActionDescResponse fetchActionDesc(@RequestBody List<String> actionList);

    class GetDefinitionsResponse extends SkynetApiResponse<List<ActionDefinitionDto>> {

    }

    class GetDefinitionResponse extends SkynetApiResponse<ActionDefinitionDto> {

    }

    class GetActionDescResponse extends SkynetApiResponse<List<NodeDescriptionDto>> {

    }

}
