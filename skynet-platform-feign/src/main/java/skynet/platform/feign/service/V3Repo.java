package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.exception.PluginNotExistException;
import skynet.platform.feign.model.RepoFileDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.io.IOException;
import java.util.List;


/**
 * 资源仓库控制器
 *
 * <AUTHOR> 2018年9月26日 下午5:42:02
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Repo")
@Tag(name = "v3. 服务关联文件管理", description = "服务关联文件管理")//, hidden = true)
public interface V3Repo {

    String PREFIX = "/skynet/api/v3/repo";

    /**
     * 列出资源仓库目录下的文件
     *
     * @param plugin
     * @return
     */
    @GetMapping(value = PREFIX + "/files/{plugin}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取资源文件列表", description = "获取指定应用(plugin)的所有文件名满足正则表达式的文件列表" +
            "(正则表达式缺失则返回所有)")
    SkynetApiResponse<List<RepoFileDto>> list(@PathVariable String plugin,
                                              @RequestParam(required = false, defaultValue = "") String regex);

    /**
     * 下载指定plugin关联的资源文件
     *
     * @param plugin
     * @param fileName 相对plugin资源目录的相对路径
     * @return
     * @throws IOException
     */
    @GetMapping(value = PREFIX + "/files/{plugin}/{fileName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "下载文件", description = "下载指定应用系统（plugin）下的关联资源文件（fileName）")
    void download(@PathVariable String plugin, @PathVariable String fileName,
                  @RequestHeader(required = false, name = "Range") String rangeHeader,
                  @RequestParam(required = false, defaultValue = "false") boolean isArchived,
                  HttpServletResponse resp) throws IOException;

    /**
     * 上传资源文件
     *
     * @param plugin
     * @param files
     * @return
     */
    @PostMapping(value = PREFIX + "/files/{plugin}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "上传文件", description = "上传文件到指定应用系统下（plugin）")
    SkynetApiResponse<Void> upload(@PathVariable String plugin, @RequestPart("files") MultipartFile[] files);

    /**
     * 删除plugin关联资源仓库下的指定文件
     *
     * @param plugin
     * @param fileName 文件名称
     * @return
     */
    @DeleteMapping(value = PREFIX + "/files/{plugin}/{fileName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除文件", description = "删除指定应用系统下（plugin）的文件（fileName）")
    SkynetApiResponse<Void> delete(@PathVariable String plugin, @PathVariable String fileName, @RequestParam(required = false, defaultValue = "false") boolean isArchived);

    /**
     * 删除plugin关联资源仓库下的指定文件
     *
     * @param plugin
     * @param fileNames 文件名称列表
     * @return
     */
    @PostMapping(value = PREFIX + "/deletion", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "批量删除文件", description = "批量删除指定应用系统下（plugin）的文件")
    SkynetApiResponse<Void> batchDelete(@RequestParam() String plugin, @RequestBody List<String> fileNames);

    /**
     * 将plugin关联的资源仓库目录打包下载
     *
     * @param plugin
     * @return
     * @throws IOException
     */
    @GetMapping(value = PREFIX + "/compress/{plugin}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "下载资源包", description = "下载指定应用系统下（plugin）所有的文件(自动压缩成zip进行下载)")
    void downloadZip(@PathVariable String plugin, HttpServletResponse resp) throws IOException, PluginNotExistException;


    /**
     * 获取资源仓库路径
     *
     * @param plugin
     * @return
     */
    @GetMapping(value = PREFIX + "/path", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取资源文件目录", description = "获取指定应用系统(plugin)下的目录(若plugin为空，将是根目录)")
    SkynetApiResponse<String> getPath(@RequestParam(name = "plugin", required = false) String plugin);
}



