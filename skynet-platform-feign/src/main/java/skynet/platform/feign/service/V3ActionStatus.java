package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.*;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3ActionStatus")
@Tag(name = "v3. 服务状态管理", description = "服务启动、停止、重启、状态获取")//, hidden = true)
public interface V3ActionStatus {

    String PREFIX = "/skynet/api/v3/actions/status";

    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取所有服务实例状态")
    //@ApiResponse(code = 200, message = "成功")
    GetAllStatusResponse getAllStatus(@Parameter(description = "过滤条件：服务所在ip") @RequestParam(required = false, name = "ip") String ip);

    @GetMapping(value = PREFIX + "/{actionPoint}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取指定服务所有实例状态")
    //@ApiResponse(code = 200, message = "成功")
    GetAllStatusResponse getStatus(
            @Parameter(description = "服务坐标") @PathVariable String actionPoint,
            @Parameter(description = "过滤条件：服务所在ip") @RequestParam(required = false, name = "ip") String ip);

    @PutMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新指定服务实例状态")
    //@ApiResponse(code = 200, message = "成功")
    NoDataResponse updateInstanceStatus(@RequestBody List<ActionStatusUpdateDto> request);


    @PostMapping(value = PREFIX + "/reboot", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "重启指定服务")
    //@ApiResponse(code = 200, message = "成功")
    RebootResponse reboot(@RequestBody List<ActionRebootDto> request);

    class GetAllStatusResponse extends SkynetApiResponse<List<ActionStatusDto>> {

    }

    class RebootResponse extends SkynetApiResponse<List<String>> {

    }
}
