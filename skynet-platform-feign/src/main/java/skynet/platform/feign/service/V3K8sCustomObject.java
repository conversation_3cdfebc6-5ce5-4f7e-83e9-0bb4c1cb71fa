package skynet.platform.feign.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.alibaba.fastjson2.JSONObject;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.config.AuthTokenConfig;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sCustomObject")
@Tag(name = "v3. K8S CustomObject 管理", description = "K8S CustomObject 管理")//, hidden = true)
public interface V3K8sCustomObject {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getCustomObjects(
        @PathVariable String ip,
        @PathVariable String group,
        @PathVariable String version,
        @PathVariable String plural,
        @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/customobjects/{group}/{version}/{plural}/{customObjectName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getCustomObject(
        @PathVariable String ip, 
        @PathVariable String namespace, 
        @PathVariable String group,
        @PathVariable String version,
        @PathVariable String plural,
        @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}/{customObjectName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getCustomObject(
        @PathVariable String ip, 
        @PathVariable String group,
        @PathVariable String version,
        @PathVariable String plural,
        @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/customobjects/{group}/{version}/{plural}/{customObjectName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getCustomObjectYaml(
        @PathVariable String ip, 
        @PathVariable String namespace, 
        @PathVariable String group,
        @PathVariable String version,
        @PathVariable String plural,
        @PathVariable String customObjectName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/customobjects/{group}/{version}/{plural}/{customObjectName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 CustomObject Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getCustomObjectYaml(
        @PathVariable String ip, 
        @PathVariable String group,
        @PathVariable String version,
        @PathVariable String plural,
        @PathVariable String customObjectName) throws Exception;
}
