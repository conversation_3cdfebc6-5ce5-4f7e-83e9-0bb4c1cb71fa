package skynet.platform.feign.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.alibaba.fastjson2.JSONObject;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.config.AuthTokenConfig;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sPod")
@Tag(name = "v3. K8S Pod 管理", description = "K8S Pod 管理")//, hidden = true)
public interface V3K8sPod {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/pods", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Pod 列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getPods(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/pods/{podName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Pod 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getPod(@PathVariable String ip, @PathVariable String namespace, @PathVariable String podName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/pods/{podName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Pod Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getPodYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String podName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/pods/{podName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 Pod")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deletePod(@PathVariable String ip, @PathVariable String namespace, @PathVariable String podName) throws Exception;

    @GetMapping(value = PREFIX + "/logs/{ip}/namespace/{namespace}/pods/{podName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "下载pod48小时内的日志")
    //@ApiResponse(code = 200, message = "成功")
    ResponseEntity<StreamingResponseBody> downloadPodLogs(@PathVariable String ip, @PathVariable String namespace, @PathVariable String podName) throws Exception;


}
