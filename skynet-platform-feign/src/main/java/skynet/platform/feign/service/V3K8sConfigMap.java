package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sConfigMap")
@Tag(name = "v3. K8S ConfigMap 管理", description = "K8S ConfigMap 管理")//, hidden = true)
public interface V3K8sConfigMap {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/configmaps", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 ConfigMap 列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getConfigMaps(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/configmaps/{configMapName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 ConfigMap 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getConfigMap(@PathVariable String ip, @PathVariable String namespace, @PathVariable String configMapName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/configmaps/{configMapName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 ConfigMap Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getConfigMapYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String configMapName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/configmaps/{configMapName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 ConfigMap")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteConfigMap(@PathVariable String ip, @PathVariable String namespace, @PathVariable String configMapName) throws Exception;
}
