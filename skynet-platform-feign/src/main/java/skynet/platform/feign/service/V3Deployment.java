package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.ActionDeploymentUpdateDto;
import skynet.platform.feign.model.AgentDeploymentDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

/**
 * <AUTHOR>
 */

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Deployment")
@Tag(name = "v3. 服务部署管理", description = "获取部署拓朴状态，服务部署更新")
public interface V3Deployment {

    String PREFIX = "/skynet/api/v3/deployment";

    /**
     * 获取所有服务器和服务(包括在线状态）部署拓扑
     *
     * @return 返回状态
     */
    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取所有服务器和服务(包括在线状态）部署拓扑")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<AgentDeploymentDto>> getClusterDeployment() throws Exception;


    @GetMapping(value = PREFIX + "/{ip}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取指定服务器及其服务(包括在线状态）部署拓扑")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<AgentDeploymentDto> getDeploymentByIp(@PathVariable String ip);

    @GetMapping(value = PREFIX + "/{ip}/{actionPoint}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取指定服务器及其服务(包括在线状态）部署拓扑")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<AgentDeploymentDto> getDeploymentByActionPoint(@PathVariable String ip, @PathVariable String actionPoint);

    /**
     * 新增或删除部署的 action
     *
     * @param ip
     * @param request
     * @return
     * @throws Exception
     */
    @PutMapping(value = PREFIX + "/{ip}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新指定服务器上的服务部署拓扑")
    SkynetApiResponse<Void> editDeployment(@PathVariable String ip,
                                           @RequestBody List<ActionDeploymentUpdateDto> request)
            throws Exception;

    /**
     * 新增加部署 action
     *
     * @param ip
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping(value = PREFIX + "/{ip}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "在指定服务器上的新增服务部署")
    SkynetApiResponse<Void> addDeployment(@PathVariable String ip,
                                          @RequestBody List<ActionDeploymentUpdateDto> request) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/{actionPoint}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "在服务器上的移除指定服务部署")
    SkynetApiResponse<Void> deleteDeployment(@PathVariable String ip,
                                             @PathVariable String actionPoint) throws Exception;
}
