package skynet.platform.feign.model.xray;

import io.swagger.v3.oas.annotations.media.Schema;

public class NetworkStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-net";

    @Schema(title = "interface")
    private String devName;

    @Schema(title = "addr")
    private String address;

    /**
     * MAC地址
     */
    private String hwAddress;


    @Schema(title = "xray_net_rx_bytesps")
    private long rxSpeed;

    @Schema(title = "xray_net_tx_bytesps")
    private long txSpeed;
}
