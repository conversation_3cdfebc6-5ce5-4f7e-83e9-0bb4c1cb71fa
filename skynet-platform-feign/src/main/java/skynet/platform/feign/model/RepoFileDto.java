package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
@Schema(title =  "资源仓库文件")
public class RepoFileDto extends Jsonable {

    @Schema(title = "系统编码")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String plugin;

    @Schema(title = "文件名称")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 20)
    private String fileName;

    @Schema(title = "文件路径")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String filePath;

    @Schema(title = "是否是目录")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private boolean isDirectory;

    @Schema(title = "文件大小")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private long fileSize;

    @Schema(title = "更新时间")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String lastUpdateTime;

    @Schema(title = "创建时间")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String createTime;

    @Schema(title = "文件MD5签名")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String md5sum;

    @Schema(title = "是否归档")
    private boolean isArchived;

    @Override
    public String toString() {
        return super.toString();
    }
}
