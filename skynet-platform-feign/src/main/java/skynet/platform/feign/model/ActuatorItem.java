package skynet.platform.feign.model;

import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>
 * @date 2020/11/6 12:53
 */
public class ActuatorItem extends Jsonable {

    private final String rel;
    private final String href;

    private final String method;

    public ActuatorItem(String rel, String href, String method) {
        this.rel = rel;
        this.href = href;
        this.method = method;
    }

    /**
     * @return the rel
     */
    public String getRel() {
        return rel;
    }

    /**
     * @return the href
     */
    public String getHref() {
        return href;
    }

    /**
     * 接口方法
     *
     * @return
     */
    public String getMethod() {
        return method;
    }
}