package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Accessors(chain = true)
@Schema(title = "服务定义")
public class ActionDefinitionDto extends Jsonable {

    @Schema(title = "是否归档")//, position = 10)
    private boolean archived;

    @Schema(title = "是否已经分配部署")//, position = 10)
    private boolean deployed;

    @Schema(title = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 10)
    private String actionCode;

    @Schema(title = "系统编码", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 20)
    private String pluginCode;

    @Schema(title = "系统名称", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 30)
    private String pluginName;

    public String getActionPoint() {
        return String.format("%s@%s", actionCode, pluginCode);
    }

    @Schema(title = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 40)
    private String actionName;

    @Schema(title = "服务描述")//, position = 50)
    private String description;

    @Schema(title = "服务前台排序")//, position = 60)
    private int index;

    /**
     * SpringBoot|BaseBoot|DockerBoot|K8sBoot
     */
    @Schema(title = "服务类型", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 70)
    private String type;

    @Schema(title = "网络协议")//, position = 80)
    private String protocol;

    @Schema(title = "服务端口")//, position = 90)
    private int port = 0;

    @Schema(title = "扩展端口")//, position = 100)
    private String ports;

    @Schema(title = "主页URL")//, position = 110)
    private String homePageURL;

    @Schema(title = "服务标签")//, position = 120)
    private List<String> tags = new ArrayList<>(0);

    @Schema(title = "关联文件列表")//, position = 130)
    private List<ReferencedFileDto> referencedFiles = new ArrayList<>(0);

    @Schema(title = "扩展配置文件")//, position = 140)
    private List<ExtConfigItemDto> extConfigItems = new ArrayList<>(0);

    @Schema(title = "启动配置", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 150)
    private StartupConfigDto startupConfig;

    @Schema(title = "健康检查配置", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 160)
    private HealthCheckConfig healthCheckConfig;

    @Schema(title = "集成配置", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 170)
    private IntegrationConfigDto integrationConfig;

    @Schema(title = "功能开关项标签列表", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 180)
    private List<SwitchLabelValueDto> switchLabels;

    @Schema(title = "skynet-mesh配置，用换行符\\n隔开")//, position = 190)
    private String meshConfigText;

    @Schema(title = "属性配置", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 200)
    private String properties;

    @Schema(title = "logback日志级别配置，用换行符\\n隔开")//, position = 210)
    private String loggingLevels;

    /**
     * @since 3.4.10
     */
    @Schema(title = "依赖配置块(code)列表", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 220)
    private List<String> configBlockCodes = new ArrayList<>(0);

    /**
     * @since 3.4.11
     */
    @Schema(title = "依赖action列表", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 235)
    private List<DependActionDto> dependActions = new ArrayList<>(0);

    @Schema(title = "K8sBoot,自定义Yaml")//, position = 240)
    private String yaml;

    @Schema(title = "分配副本数, 默认1")//, position = 250)
    private Integer replicas = 1;

    @Schema(title = "分配实例数, 默认1")//, position = 255)
    private Integer instances = 1;

    @Getter
    @Setter
    @Schema(title = "启动配置")
    public static class StartupConfigDto extends Jsonable {

        @Schema(title = "工作目录", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 10)
        private String workingDir;

        @Schema(title = "可执行jar包名称")//, position = 20)
        private String runnableJar;

        @Schema(title = "skynetBoot运行参数", example = "{\"name\":\"skynet.ant.demo\", \"context\":{\"key\":\"value\"}}")
//, position = 30)
        private String skynetRunParam;

        @Schema(title = "Base命令行", example = "start.sh")//, position = 40)
        private String cmd;

        @Schema(title = "java命令行选项", example = "-Xms=512m -Xmx=1g")//, position = 50)
        private String javaCmdOptions;

        @Schema(title = "程序命令行参数", example = "--my-config=xxx")//, position = 60)
        private String programArguments;

        @Schema(title = "系统环境变量")//, position = 70)
        private Map<String, String> sysEnvironments;

        @Schema(title = "docker run 选项", example = "-p 8090:8080 -v /path1:/path2")//, position = 80)
        private String dockerRunOptions;

        @Schema(title = "docker run 执行命令及命令参数", example = "./start.sh foo1 foo2")//, position = 90)
        private String dockerRunCmdAndArgs;

        @Schema(title = "docker容器名称")//, position = 100)
        private String dockerContainerName;

        //由于老版本 与 runnableJar 复用
// @Schema(title = "docker镜像名称或者ID", required = false)//, position = 110) // private String dockerImage;
        @Schema(title = "程序退出信号值(kill命令参数)", requiredMode = Schema.RequiredMode.REQUIRED)//, position = 120)
        private int signalToStop;

        @Schema(title = "BaseBoot日志文件")//, position = 130)
        private String logFile;
    }

    @Getter
    @Setter
    @Schema(title = "集成配置")
    public static class IntegrationConfigDto extends Jsonable {
        @Schema(title = "是否采集logback日志到ELK")//, position = 10)
        private boolean logbackLogCollection;

        @Schema(title = "是否开启SkynetMesh")//, position = 30)
        private boolean meshEnabled;
    }

    @Getter
    @Setter
    @Schema(title = "健康检查配置")
    public static class HealthCheckConfig extends Jsonable {

        @Schema(title = "检查方式, pid 或者 protocol")//, position = 10)
        private String type;

        @Schema(title = "HTTP访问URL")//, position = 20)
        private String url;

        @Schema(title = "初次检查延迟时间（秒）")//, position = 30)
        private int delaySeconds = 10;

        @Schema(title = "检查间隔（秒）")//, position = 40)
        private int intervalSeconds = 30;

        @Schema(title = "超时时间（秒）")//, position = 50)
        private int timeoutSeconds = 20;

        @Schema(title = "重试次数")//, position = 60)
        private int retryTimes = 3;
    }


    @Getter
    @Setter
    @Schema(title = "引用文件")
    public static class ReferencedFileDto extends Jsonable {

        @Schema(title = "文件名")//, position = 10)
        private String fileName;

        @Schema(title = "下载目标目录")//, position = 20)
        private String targetDir;

        /**
         * 文件权限，如：755， 777
         */
        @Schema(title = "文件权限")//, position = 30)
        private String mode;
        /**
         * 归属用户
         */
        @Schema(title = "归属用户")//, position = 40)
        private String owner;
    }

    @Getter
    @Setter
    @Schema(title = "自定义扩展配置")
    @Accessors(chain = true)
    public static class ExtConfigItemDto extends Jsonable {

        @Schema(title = "目标文件（绝对路径）")//, position = 10)
        private String targetFile;

        @Schema(title = "文件内容编码，默认UTF8")//, position = 20)
        private String encoding;

        @Schema(title = "配置文件内容")//, position = 30)
        private String text;

        /**
         * 文件权限，如：755， 777
         */
        @Schema(title = "文件权限，如：755， 777")//, position = 40)
        private String mode;

        /**
         * 归属用户
         */
        @Schema(title = "文件归属用户")//, position = 50)
        private String owner;
    }

    @Getter
    @Setter
    @Schema(title = "依赖服务项")
    @Accessors(chain = true)
    public static class DependActionDto extends Jsonable {

        /**
         * 服务坐标编码  如：  mysql@turing-pass
         */
        @Schema(title = "服务坐标编码")//, position = 10)
        private String code;

        /**
         * 依赖类型
         */
        @Schema(title = "依赖类型，默认运行依赖")//, position = 20)
        private DependType type = DependType.runtime;

        /**
         * 依赖类型
         */
        @Schema(title = "依赖类型")
        public enum DependType {
            /**
             * 运行依赖
             */
            runtime,

            /**
             * 调用依赖
             */
            calling,
        }
    }
}
