package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
@Schema(title = "备份还原")
public class BackupRestoreDto extends Jsonable {

    @Schema(title = "还原整个备份")
    private String name;

    /**
     * name 和 code 二选一
     */
    @Schema(title = "还原指定系统的备份")
    private String code;

    @Schema(title = "要还原的备份内容")
    private String content;
}
