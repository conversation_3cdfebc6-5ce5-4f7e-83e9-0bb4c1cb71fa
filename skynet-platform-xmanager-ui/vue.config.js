'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'SKYNET管理控制台' // page title

const port = process.env.port || process.env.npm_config_port || 9527 // dev port

const devServerBefore = process.env.VUE_APP_USE_MOCK === 'true' ? require('./mock/mock-server.js') : null
const httpProxyTaget = process.env.VUE_APP_HTTP_PROXY_TARGET ? process.env.VUE_APP_HTTP_PROXY_TARGET : 'http://localhost:2230'
const wsProxyTaget = process.env.VUE_APP_WS_PROXY_TARGET ? process.env.VUE_APP_WS_PROXY_TARGET : 'ws://localhost:2230'
const outputDir = process.env.VUE_OUTPUT_DIR ? process.env.VUE_OUTPUT_DIR : 'dist'

module.exports = {
  publicPath: './',
  outputDir: outputDir,
  assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV !== 'production',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/dev-api': {
        target: httpProxyTaget,
        pathRewrite: { '^/dev-api': '' },
        changeOrigin: true // 必须要加
      },
      '/skynet': {
        target: httpProxyTaget,
        pathRewrite: { '^/skynet': '/skynet' },
        changeOrigin: true // 必须要加
      },
      '/skynet/proxy/ws': {
        target: wsProxyTaget, // 后端目标接口地址
        changeOrigin: true, // 是否允许跨域
        ws: true // 开启ws, 如果是http代理此处可以不用设置
      }
    },
    before: devServerBefore
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config.when(process.env.NODE_ENV === 'development', config => config.devtool('cheap-source-map'))

    config.when(process.env.NODE_ENV !== 'development', config => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            inline: /runtime\..*\.js$/
          }
        ])
        .end()
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })
      config.optimization.runtimeChunk('single')
    })
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [path.resolve(__dirname, './src/assets/css/*.scss')]
    }
  }
}
