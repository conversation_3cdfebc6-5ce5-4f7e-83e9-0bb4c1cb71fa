<template>
  <div>
    <el-tag
      :key="key"
      v-for="(value, key) in dynamicTags"
      closable
      :disable-transitions="false"
      @close="deleteLabel(key, value)"
      size="small"
      class="label-tag"
    >
      <span :title="`${key}=${value}`">{{ `${key}=${value}` }}</span>
    </el-tag>
    <span v-if="inputVisible" style="display: inline-block">
      <el-input class="input-new-tag" v-model="inputKey" ref="saveInputKey" @keyup.enter.native="keyBlur" @blur="keyBlur" :placeholder="$t('111')">
      </el-input>
      <span>=</span>
      <el-input class="input-new-tag" v-model="inputValue" ref="saveInputValue" @keyup.enter.native="addLable" :placeholder="$t('111')"> </el-input>
    </span>
    <el-button class="el-button el-button--primary el-button--mini" icon="el-icon-plus" v-else @click="showInput">{{ $t('112') }}</el-button>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  props: ['label'],
  data() {
    return {
      dynamicTags: {},
      inputVisible: false,
      inputKey: '',
      inputValue: ''
    }
  },
  watch: {
    label: {
      handler: function(val, oldVal) {
        if (val) {
          this.dynamicTags = val
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    deleteLabel(key, value) {
      this.$confirm(`${locale.t('113')}${key}=${value}${locale.t('114')}`, locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        const temp = JSON.parse(JSON.stringify(this.dynamicTags))
        delete temp[key]
        this.$emit('update', temp, () => {
          this.dynamicTags = temp
        })
      })
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveInputKey.$refs.input.focus()
      })
    },
    keyBlur() {
      if (this.inputKey) {
        if (!this.validateValue()) return
        this.$nextTick(_ => {
          this.$refs.saveInputValue.$refs.input.focus()
        })
      } else {
        this.closeInput()
      }
    },
    addLable() {
      if (this.inputValue) {
        if (!this.validateValue()) return
        const temp = JSON.parse(JSON.stringify(this.dynamicTags))
        temp[this.inputKey] = this.inputValue
        this.$emit('update', temp, () => {
          this.dynamicTags = temp
          this.closeInput()
        })
      } else {
        this.closeInput()
      }
    },
    // 校验
    validateValue() {
      if (this.dynamicTags[this.inputKey]) {
        this.$message.warning(`${this.inputKey}${locale.t('115')}`)
        return false
      }
      if (this.inputKey.match(/[\u4e00-\u9fa5]+/g) || this.inputValue.match(/[\u4e00-\u9fa5]+/g)) {
        this.$message.warning(`${locale.t('116')}`)
        return false
      }
      return true
    },
    closeInput() {
      this.inputVisible = false
      this.inputKey = ''
      this.inputValue = ''
    }
  }
}
</script>
<style scoped lang="scss">
.label-tag {
  margin: 0 10px 10px 0;
  color: #909399;
  display: inline-flex;
  align-items: end;
  max-width: 100%;

  span {
    width: calc(100% - 14px);
    overflow: hidden;
    flex-shrink: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: bold;
  }
}
.input-new-tag {
  width: 110px;
  vertical-align: bottom;

  &:first-child {
    margin-left: 10px;
  }
}
</style>
