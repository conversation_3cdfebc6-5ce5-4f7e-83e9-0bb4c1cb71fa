<template>
  <div class="logo-container">
    <transition name="sidebarLogoFade">
      <router-link to="/" class="logo">
        <img :src="logo" class="sidebar-logo" />
        <h1><i>|</i>{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import settings from '@/settings'
import { mapGetters } from 'vuex'
import logoPicPath from '@/assets/img/logo.png'
import logoEnPicPath from '@/assets/img/logo_en.png'
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      logoPicPath,
      logoEnPicPath
    }
  },
  computed: {
    ...mapGetters(['uiVars']),
    title() {
      return this.uiVars['app_title'] || settings.title
    },
    logo() {
      if (process.env.NODE_ENV === 'production') {
        let logoPicURL = this.uiVars['skynet.ui.logo.png']
        if (logoPicURL) {
          return logoPicURL
        }
      }
      if (localStorage.getItem('locale-lang') === 'en') {
        return logoEnPicPath
      } else {
        return logoPicPath
      }
    }
  }
}
</script>
<style scoped lang="scss">
.logo-container {
  .logo {
    height: $headtopHeight;
    float: left;
    text-align: center;
    padding-left: 20px;
    padding-top: 15px;
    overflow: hidden;
    img {
      display: block;
      height: 25px;
      float: left;
    }
    h1 {
      float: left;
      color: #fff;
      margin: 0;
      font-size: 17px;
      font-weight: normal;
      line-height: 26px;
      i {
        margin: 0 10px;
        font-style: normal;
      }
    }
  }
}
</style>
