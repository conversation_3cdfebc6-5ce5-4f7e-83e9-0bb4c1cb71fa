import locale from '@/i18n/index.js'
import tag from './tag'
import definition from './definition'
import agent from './agent'
import auth from './auth'
import cluster from './cluster'
import deployment from './deployment'
import plugin from './plugin'
import repo from './repo'
import runtime from './runtime'
import switchLabels from './switch-labels'
import status from './status'
import view from './view'
import proxy from './proxy'
import skynetconfig from './skynetconfig'
import ui from './ui'
import diagnosis from './diagnosis'
import terminal from './terminal'
import kubernetes from './kubernetes'
import backup from './backup'
import config from './config'
export default {
  tag,
  definition,
  agent,
  auth,
  cluster,
  deployment,
  plugin,
  repo,
  runtime,
  switchLabels,
  status,
  view,
  proxy,
  skynetconfig,
  ui,
  diagnosis,
  terminal,
  kubernetes,
  backup,
  config
}
