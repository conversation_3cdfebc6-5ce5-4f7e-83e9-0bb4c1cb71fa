import locale from '@/i18n/index.js'
import request from '../request'
import { readableBytes } from '@/common/util'
import { getUrlWithToken } from '@/utils/auth'
function getPath(pluginCode) {
  let url = `skynet/api/v3/repo/path?plugin=${pluginCode}`
  return new Promise(resolv => {
    request.get(url).then(data => {
      resolv(data)
    })
  })
}
function getFiles(pluginCode) {
  let url = `skynet/api/v3/repo/files/${pluginCode}`
  return new Promise(resolv => {
    request.get(url).then(data => {
      resolv(__dto2vo(pluginCode, data))
    })
  })
}
function upload(pluginCode, file, progressEventHandler) {
  // return new Promise((resolv) => {
  // })
  let formData = new FormData()
  formData.append('files', file)
  return request.post(`skynet/api/v3/repo/files/${pluginCode}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // onUploadProgress: progressEvent => {
    //   let complete = (progressEvent.loaded / progressEvent.total * 100 | 0) + '%'
    //   //console.log('complete: %s', complete)
    // }
    onUploadProgress: progressEventHandler,
    timeout: 0
  })
}
function remove(pluginCode, filename,archived) {
  return request.delete(`skynet/api/v3/repo/files/${pluginCode}/${filename}?isArchived=${archived}`)
}
function batchRemove(pluginCode, filenames) {
  return request.post(`skynet/api/v3/repo/deletion?plugin=${pluginCode}`, filenames, {
    __view_pop_success: locale.t('46'),
    __view_pop_error: locale.t('47')
  })
}
function download(pluginCode, fileName,archived) {
  let url = `skynet/api/v3/repo/files/${pluginCode}/${fileName}?isArchived=${archived}`
  url = getUrlWithToken(url)
  window.open(url, '_blank')
}
function downloadCompress(pluginCode) {
  let url = `skynet/api/v3/repo/compress/${pluginCode}`
  url = getUrlWithToken(url)
  window.open(url, '_blank')
}
function __dto2vo(pluginCode, data) {
  return data.map(item => {
    let readableSize = readableBytes(item.fileSize)
    item.__view_filesize = `${item.fileSize} [${readableSize}]`
    // item.__view_download_url = `skynet/api/v3/repo/files/${pluginCode}/${item.fileName}`
    return item
  })
}
export default {
  getPath,
  getFiles,
  upload,
  batchRemove,
  remove,
  download,
  downloadCompress
}
