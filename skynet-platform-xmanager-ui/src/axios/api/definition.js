import locale from '@/i18n/index.js'
import request from '../request'
import {deepCopy} from '@/common/util'
import {getUrlWithToken} from '@/utils/auth'
import emptyData from '../../views/ActionDefinition/empty-data'
import store from '@/store'

function getActionDefinitions(pluginCode) {
  let url = 'skynet/api/v3/actions/definition'
  if (pluginCode) {
    url = url + '?pluginCode=' + pluginCode
  }
  return new Promise((resolv, reject) => {
    request
      .get(url)
      .then(data => {
        let voList = data
          ? data.map(dto => {
            return __dto2vo(dto)
          })
          : []
        if (!pluginCode) {
          store.dispatch('skynet/changeActionDefs', voList)
        }
        resolv(voList)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function getActionDefinition(actionPoint) {
  return new Promise((resolv, reject) => {
    request
      .get(`skynet/api/v3/actions/definition/${actionPoint}`)
      .then(data => {
        resolv(__dto2vo(data))
      })
      .catch(err => {
        reject(err)
      })
  })
}

function createActionDefinition(vo) {
  let dto = __vo2dto(vo)
  let promise = request.post('skynet/api/v3/actions/definition', dto, {
    __view_pop_success: locale.t('27'),
    __view_pop_error: locale.t('28')
  })
  return __new_promise_clear_store(promise)
}

function updateActionDefinition(vo) {
  let dto = __vo2dto(vo)
  let url = `skynet/api/v3/actions/definition/${vo.actionCode}@${vo.pluginCode}`
  let promise = request.put(url, dto, {
    __view_pop_success: locale.t('29'),
    __view_pop_error: locale.t('30')
  })
  return __new_promise_clear_store(promise)
}

function deleteActionDefinition(actionPoint) {
  return __new_promise_clear_store(request.delete(`skynet/api/v3/actions/definition/${actionPoint}`))
}

function archiveActionDefinition(actionPoint) {
  return __new_promise_clear_store(request.put(`skynet/api/v3/actions/definition/${actionPoint}/archive`))
}

function restoreActionDefinition(actionPoint) {
  return __new_promise_clear_store(request.put(`skynet/api/v3/actions/definition/${actionPoint}/restore`))
}

function importActionDefinition(file) {
  let formData = new FormData()
  formData.append('file', file)
  let promise = request.post('skynet/api/v3/actions/definition/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000,
    __view_pop_success: locale.t('31'),
    __view_pop_error: locale.t('32')
  })
  return __new_promise_clear_store(promise)
}

function exportActionDefinition(codes) {
  let query = codes
    .map(code => {
      return `codes=${code}`
    })
    .join('&')
  let urlPrefix = process.env.NODE_ENV !== 'production' && process.env.VUE_APP_HTTP_PROXY_TARGET ? process.env.VUE_APP_HTTP_PROXY_TARGET : ''
  let url = `${urlPrefix}/skynet/api/v3/actions/definition/export?${query}`
  url = getUrlWithToken(url)
  window.open(url, '_blank')
}

function __new_promise_clear_store(promise) {
  return new Promise((resolv, reject) => {
    promise
      .then(d => {
        store.dispatch('skynet/clearActionDefs')
        resolv(d)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function __dtoAutocomplete(dto) {
  for (let key in emptyData) {
    if (dto[key] === undefined || dto[key] === null) {
      dto[key] = emptyData[key]
    }
  }
}

function __dto2vo(dto) {
  __dtoAutocomplete(dto)
  let __view_type = dto.type
  // if (dto.type === 'BaseBoot') {
  //   __view_type = 'Base'
  // } else if (dto.type === 'SkynetBoot') {
  //   __view_type = 'Skynet'
  // } else if (dto.type === 'DockerBoot') {
  //   __view_type = 'Docker'
  // }

  let __view_protocol = 'TCP'
  if (dto.protocol) {
    if (dto.protocol.toLocaleLowerCase() === 'http') {
      __view_protocol = 'HTTP'
    } else if (dto.protocol.toLocaleLowerCase() === 'https') {
      __view_protocol = 'HTTPS'
    }
  }
  let __view_functions = [
    {
      code: 'enableHomepage',
      value: !!dto.homePageURL,
      extProperty: !dto.homePageURL ? '' : dto.homePageURL
    },
    {
      code: 'enableLogCollect',
      value: dto.integrationConfig && dto.integrationConfig.logbackLogCollection
    }
  ]
  if (dto.switchLabels && dto.switchLabels.length > 0) {
    for (let item of dto.switchLabels) {
      __view_functions.push({
        ...item
      })
    }
  }
  let code2FunctionValue = new Map()
  __view_functions.forEach(item => code2FunctionValue.set(item.code, item.value))
  let __view_enable_log_collect = code2FunctionValue.has('enableLogCollect') ? code2FunctionValue.get('enableLogCollect') : false
  let __view_enable_prometheus = code2FunctionValue.has('enablePrometheusTarget') ? code2FunctionValue.get('enablePrometheusTarget') : false
  let __view_sys_envs_array = []
  let sysenvs = dto.startupConfig.sysEnvironments
  if (sysenvs) {
    for (let k in sysenvs) {
      __view_sys_envs_array.push(`${k}=${sysenvs[k]}`)
    }
  }
  let __view_sys_envs = __view_sys_envs_array.length > 0 ? __view_sys_envs_array.join('\n') : ''
  let fileMapFunc = file => {
    let fileName = ''
    if (file.fileName) {
      if (file.fileName.startsWith('skynet:')) {
        fileName = file.fileName.substring(7, file.fileName.length)
      } else {
        fileName = file.fileName
      }
    }
    return {
      fileName,
      targetDir: file.targetDir,
      mode: file.mode,
      owner: file.owner
    }
  }
  let __view_ref_files = dto.referencedFiles ? dto.referencedFiles.map(fileMapFunc) : []
  return {
    ...dto,
    ...{
      __view_type,
      __view_protocol,
      __view_functions,
      __view_enable_log_collect,
      __view_enable_prometheus,
      __view_sys_envs,
      __view_ref_files
    }
  }
}

let __vo2dto = function (vo) {
  let voCopy = deepCopy(vo)
  // const bootTypeWithNoSuffix = new Set(['Base', 'Skynet', 'Docker'])
  // voCopy.type = (bootTypeWithNoSuffix.has(vo.__view_type)) ? vo.__view_type + 'Boot' : vo.__view_type
  voCopy.type = vo.__view_type

  // TODO 后台是否支持协议传'TCP'
  voCopy.protocol = vo.__view_protocol
  let switchLabels = []
  for (let switchLabelItem of vo.__view_functions) {
    if (switchLabelItem.code === 'enableHomepage') {
      // 这个label是为了界面渲染的方便临时加的
      voCopy.homePageURL = switchLabelItem.value ? switchLabelItem.extProperty : ''
    } else if (switchLabelItem.code === 'enableLogCollect') {
      // 这个label是为了界面渲染的方便临时加的
      voCopy.integrationConfig.logbackLogCollection = switchLabelItem.value || false
    } else {
      switchLabels.push({
        code: switchLabelItem.code,
        value: switchLabelItem.value,
        extProperty: switchLabelItem.extProperty
      })
    }
  }
  voCopy.switchLabels = switchLabels
  voCopy.startupConfig.sysEnvironments = __parseEnvInput(vo.__view_sys_envs)
  let referencedFiles = []
  if (voCopy.__view_ref_files) {
    referencedFiles = voCopy.__view_ref_files.map(file => {
      let lowercase = file.fileName.toLocaleLowerCase()
      let fileName = lowercase.startsWith('http://') || lowercase.startsWith('https://') ? file.fileName : 'skynet:' + file.fileName
      return {
        fileName,
        targetDir: file.targetDir,
        mode: file.mode,
        owner: file.owner
      }
    })
  }
  voCopy.referencedFiles = referencedFiles
  for (let key in vo) {
    if (key.startsWith('__view_')) {
      delete voCopy[key]
    }
  }
  return voCopy
}
let __parseEnvInput = function (text) {
  let ret = {}
  if (!text) {
    return ret
  }
  let lines = text.split('\n')
  for (let line of lines) {
    if (!line) {
      continue
    }
    let index = line.indexOf('=')
    if (index > 0) {
      let key = line.substring(0, index).trim()
      let value = line.substring(index + 1, line.length).trim()
      ret[key] = value
    }
  }
  return ret
}

// 根据服务坐标列表获取服务名称描述
function fetchActionDescList(actionPointList) {
  return new Promise((resolv, reject) => {
    request
      .post(`skynet/api/v3/actions/definition/list`, actionPointList)
      .then(data => {
        resolv(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

export default {
  getActionDefinitions,
  getActionDefinition,
  createActionDefinition,
  updateActionDefinition,
  deleteActionDefinition,
  archiveActionDefinition,
  restoreActionDefinition,
  importActionDefinition,
  exportActionDefinition,
  fetchActionDescList
}
