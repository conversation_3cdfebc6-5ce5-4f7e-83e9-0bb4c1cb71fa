/* 初始化 */
.el-table th.gutter{
    display: table-cell!important
}
//分页右边
.el-pagination{
    margin-top: 20px;
    text-align: right;
}
.queryList{
    color:#333;
    font-size: 14px;
    ul{
        overflow: hidden;
        margin-bottom: 15px;
        padding:0;
        margin:0;
    }
    li{
        float: left;
        margin-right: 20px;
        margin-bottom: 15px;
        min-height: 28px;
        list-style: none;
        label{
            font-weight: normal;
        }
    }
    .el-input{
        width:150px;
    }
    .el-input+.el-button{
        margin-left: 10px;
    }
}
.table{
    background: #fff;
    margin-top: 20px;
    .headbg th{

        background:#eee!important;
    }
    
}

.el-dialog__header{
    background: #F6F8F9;
    border-bottom:1px solid #eee;
}
.el-step__title{
    font-size:12px;
}
.el-tabs__header{
    margin-bottom: 0;
}
.el-dialog--center .el-dialog__body{
    padding:25px 40px 30px 25px
}
.el-dialog__body{
    .el-select{
        width:100%
    }
}