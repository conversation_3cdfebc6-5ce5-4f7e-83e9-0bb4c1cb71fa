import serverManage from './server-manage'
import menu from './menu'

const enLocale = {
  menu,
  serverManage,
  '0': 'Failed to retrieve node list',
  '1': 'SSH connection successful',
  '2': 'Kubernetes connection successful',
  '3': 'SSH connection failed',
  '4': 'Kubernetes connection failed',
  '5': 'Node creation successful',
  '6': 'Node creation failed',
  '7': 'Node information updated successfully',
  '8': 'Node information update failed',
  '9': 'Online',
  '10': 'Offline',
  '11': 'Distributing',
  '12': 'Core | ',
  '13': 'GB Memory',
  '14': 'Node',
  '15': 'Unknown',
  '16': 'Password changed successfully',
  '17': 'Cluster-level attributes updated successfully',
  '18': 'Cluster-level attributes update failed',
  '19': 'Cluster-level log level Config updated successfully',
  '20': 'Cluster-level log level Config update failed',
  '21': 'Config saved successfully',
  '22': 'Config save failed',
  '23': 'Order set successfully',
  '24': 'Order set failed',
  '25': 'Config imported successfully',
  '26': 'Config import failed',
  '27': 'Service creation successful',
  '28': 'Service creation failed',
  '29': 'Service updated successfully',
  '30': 'Service update failed',
  '31': 'Service definition imported successfully',
  '32': 'Service definition import failed',
  '33': 'Failed to retrieve type',
  '34': 'Operation failed',
  '35': 'Query failed',
  '36': 'Deletion failed',
  '37': 'Application plugin created successfully',
  '38': 'Application plugin created',
  '39': 'Service information updated successfully',
  '40': 'Service information update failed',
  '41': 'Plugin-level attributes updated successfully',
  '42': 'Plugin-level attributes update failed',
  '43': 'Plugin-level log level Config updated successfully',
  '44': 'Plugin-level log level Config update failed',
  '45': 'Proxy request failed',
  '46': 'File deleted successfully',
  '47': 'File deletion failed',
  '48': 'Service tag updated successfully',
  '49': 'Service tag update failed',
  '50': 'Server tag updated successfully',
  '51': 'Server tag update failed',
  '52': 'Application display order adjusted successfully',
  '53': 'Application display order adjustment failed',
  '54': 'Node display order adjusted successfully',
  '55': 'Node display order adjustment failed',
  '56': 'Service display order adjusted successfully',
  '57': 'Service display order adjustment failed',
  '58': '[Request failed]',
  '59': '[Unrecognized API response]',
  '60': '[Error code: ',
  '61': 'Session expired, redirecting to login page',
  '62': 'Copied to clipboard',
  '63': 'Create peer',
  '64': 'Edit',
  '65': 'Delete',
  '66': 'Notice',
  '67': '成就客户',
  '68': '创新坚守',
  '69': '团队协作',
  '70': '简单真诚',
  '71': '专业敬业',
  '72': '担当奋进',
  '73': 'or slide the slider to the far right to confirm',
  '74': 'Confirmed',
  '75': 'Confirm',
  '76': 'Cancel',
  '77': 'Male',
  '78': 'Female',
  '79': 'Gender',
  '80': '点击，或拖动图片至此处',
  '81': '正在上传……',
  '82': '浏览器不支持该功能，请使用IE10以上或其他现在浏览器！',
  '83': '上传成功',
  '84': '图片上传失败',
  '85': '头像预览',
  '86': '关闭',
  '87': '上一步',
  '88': 'Save',
  '89': '仅限图片格式',
  '90': '单文件大小不能超过 ',
  '91': '图片最低像素为（宽*高）：',
  '92': '點擊，或拖動圖片至此處',
  '93': '正在上傳……',
  '94': '瀏覽器不支持該功能，請使用IE10以上或其他現代瀏覽器！',
  '95': '上傳成功',
  '96': '圖片上傳失敗',
  '97': '頭像預覽',
  '98': '關閉',
  '99': '僅限圖片格式',
  '100': '單文件大小不能超過 ',
  '101': '圖片最低像素為（寬*高）：',
  '102': 'アップロード中...',
  '103': 'このブラウザは対応されていません。IE10+かその他の主要ブラウザをお使いください。',
  '104': 'アップロード成功',
  '105': 'アップロード失敗',
  '106': '閉じる',
  '107': '戻る',
  '108': '画像のみ',
  '109': '画像サイズが上限を超えています。上限: ',
  '110': '画像が小さすぎます。最小サイズ: ',
  '111': 'Press Enter to add',
  '112': 'Add',
  '113': 'Are you sure you want to delete the tag [',
  '114': ']?',
  '115': 'Already exist',
  '116': 'Chinese characters are not supported!',
  '117': 'Please enter',
  '118': 'Please select',
  '119': 'Search',
  '120': 'Reset',
  '121': 'Enter search criteria',
  '122': 'Select time',
  '123': 'Please',
  '124': '+ Select Tag',
  '125': 'Select Tag',
  '126': 'Add tag',
  '127': 'Enter tag name and press Enter',
  '128': 'Tag deletion cannot be undone, are you sure you want to delete it?',
  '129': 'A tag with the same name already exists!',
  '130': ' Drag files here, or',
  '131': 'Click to upload',
  '132': ' Drag or',
  '133': 'Administrator',
  '134': 'Logout',
  '135': 'Start Time:',
  '136': 'Up Time:',
  '137': 'BuildBranch:',
  '138': 'BuildCommit:',
  '139': 'BuildNumber:',
  '140': 'BuildTime:',
  '141': ' Version: ',
  '142': ' Language:',
  '143': 'Chinese',
  '144': 'English',
  '145': 'Unknown Version',
  '146': 'Open in New Window',
  '147': 'Refresh',
  '148': 'Close Others',
  '149': 'Close All',
  '150': 'Console',
  '151': 'Service Manage',
  '152': 'Node Manage',
  '153': 'Service Define',
  '154': 'Config Manage',
  '155': 'Cluster Monitor',
  '156': '/redirect/grafana/cluster?tabTitle=Cluster Monitoring&grafanaUrlPathInRoute=',
  '157': 'Assist Tools',
  '158': 'Backup Manage',
  '159': 'Node Details / ',
  '160': 'Host Details / ',
  '161': 'Pod Details / ',
  '162': 'Deployment Details / ',
  '163': 'DaemonSet Details / ',
  '164': 'StatefulSet Details / ',
  '165': 'Skynet Console Logs',
  '166': 'SKYNET Management Console',
  '167': 'Skynet Service Console',
  '168': 'Sun',
  '169': 'Mon',
  '170': 'Tue',
  '171': 'Wed',
  '172': 'Thu',
  '173': 'Fri',
  '174': 'Sat',
  '175': 'Just Now',
  '176': 'minutes Ago',
  '177': 'hours Ago',
  '178': '1 Day Ago',
  '179': 'Month',
  '180': 'Hour',
  '181': 'Minute',
  '182': 'Day',
  '183': 'Hour',
  '184': 'Minute',
  '185': 'Second',
  '186': 'Scheduled',
  '187': 'Initialized',
  '188': 'Container Ready',
  '189': 'Pod Ready',
  '190': 'Not Scheduled',
  '191': 'Not Initialized',
  '192': 'Container Not Ready',
  '193': 'Pod Not Ready',
  '194': 'Container Completed',
  '195': 'Pod Completed',
  '196': 'Init Container',
  '197': 'Working Container',
  '198': 'Cluster Properties Config',
  '199': 'Cluster Log Config',
  '200': 'Import',
  '201': 'Application Plugin',
  '202': 'Loading Application Plugin List',
  '203': 'Please Enter Keywords',
  '204': 'Pin to Top',
  '205': 'Pin to Bottom',
  '206': 'View',
  '207': 'Export',
  '2071': 'Archive',
  '2072': 'Restore',
  '208': 'Select',
  '209': 'Sort',
  '210': 'Add New Application Plugin',
  '211': 'Export Application Plugin Config',
  '212': 'Delete Selected Application Plugins',
  '213': 'Loading',
  '214': 'Service List',
  '215': 'Resource Repository',
  '216': 'Plugin Properties',
  '217': 'Plugin Logs',
  '218': 'All Application Plugins',
  '219': 'Successfully deleted',
  '2191': 'Successfully archived',
  '2192': 'Successfully restored',
  '220': ' application plugins,',
  '221': ' application plugin deletion failed',
  '222': 'Application plugins deleted successfully',
  '223': 'Are you sure you want to delete the following application plugins?',
  '224': 'Please select the plugins to export first',
  '225': 'Please drag the list rows to adjust the order',
  '226': 'Select Type',
  '227': 'New',
  '228': 'NO.',
  '229': 'Name',
  '2291': 'Deployed',
  '2292': 'Archived',
  '230': 'Code',
  '231': 'Type',
  '232': 'Protocol',
  '233': 'Port',
  '234': 'Tag',
  '235': 'Log',
  '236': 'Yes',
  '237': 'No',
  '238': 'Monitor',
  '239': 'Operation',
  '240': ' Edit ',
  '241': 'Copy',
  '242': ' / Edit',
  '243': 'Service Definition / ',
  '244': ' / Create Service',
  '245': 'Create Service',
  '246': ' services,',
  '247': ' services deletion failed',
  '2471': ' services archive failed',
  '2472': ' services restore failed',
  '248': 'Service Deleted Successfully',
  '2481': 'Service Archived Successfully',
  '2482': 'Service Restored Successfully',
  '249': 'Confirm Delete the Following Services?',
  '2491': 'Confirm Archive the Following Services?',
  '2492': 'Confirm Restore the Following Services?',
  '250': 'Please select the services to export first',
  '251': 'Cluster - Log Level Config',
  '252': 'Log Levels: OFF, ERROR, WARN, INFO, DEBUG, TRACE',
  '253': 'Cluster - Properties Config',
  '254': 'Available Plugin Variable Placeholders or Custom Properties References, such as ${my.prop.key}',
  '255': 'Cluster Level Log Level',
  '256': 'Failed to Retrieve Cluster Level Log Level Config',
  '257': 'Cluster Level Properties',
  '258': 'Failed to Retrieve Cluster Properties',
  '259': 'Tools',
  '260': 'Plugin Level Log Level',
  '261': 'Failed to Retrieve Plugin Level Log Level Config',
  '262': 'Plugin Level Properties',
  '263': 'Failed to Retrieve Plugin Properties',
  '264': 'Basic Config',
  '265': 'Associated Files',
  '266': 'Template Files',
  '267': 'Function Options',
  '268': 'Dependent Services',
  '269': 'Properties Config',
  '270': 'Log Config',
  '271': 'Save and Close',
  '272': 'Restore',
  '273': 'Enable HomePage',
  '274': 'URL Path',
  '275': 'Enable LogCollection',
  '276': 'Switching Back to View Mode Will Discard All Changes, Continue?',
  '277': 'Basic Information',
  '278': 'PluginName:',
  '279': 'PluginCode:',
  '280': 'ActionName:',
  '281': 'Example: [AST] Chinese Dictation Service [Rest]',
  '282': 'ActionCode:',
  '283':
    'Example: rest-ast-v20 [Naming Convention: Composed of characters such as numbers, letters, -, etc.] [Variable Reference ${SKYNET_ACTION_CODE}]',
  '284': 'Description:',
  '285': 'Example: Chinese Speech Dictation Service',
  '286': 'Tags:',
  '287': 'Type:',
  '288': 'Protocol:',
  '289': 'Port:',
  '290': 'ExtPorts:',
  '291': 'Multiple Ports Can Be Separated by Commas',
  '292': 'Start/Stop Config',
  '293': 'WorkHome:',
  '294': 'Example: /iflytek/server/skynet [Variable Reference ${WORK_HOME}]',
  '295': 'Start CMD:',
  '296': 'Please Enter the Service Start Command (Background start mode is not supported, e.g., nohup, sh * &)',
  '297': 'ENV:',
  '298': 'e.g., JAVA_HOME=/usr/java/jdk, separate multiple environment variables with new lines',
  '299': 'ExitSignal:',
  '300': 'Select Linux Signal Value',
  '301': 'FileName:',
  '302': 'JVM:',
  '303': 'e.g., -Xms512M -Xmx1G -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005',
  '304': 'Context:',
  '305': 'CLI Params:',
  '306': 'Image:',
  '307': 'Please Enter the Image Name, e.g., hub.iflytek.com/turing-common/edu_base:1.0.0',
  '308': 'Options:',
  '309':
    'OPTIONS part in docker create [OPTIONS] IMAGE [COMMAND] [ARG...] (recommended one per line), e.g., -v /data/vol/mypath:/path-in-container -p 18080:8080',
  '310': 'Params:',
  '311': 'COMMAND [ARG...] part in docker create [OPTIONS] IMAGE [COMMAND] [ARG...]',
  '312': 'Health Check',
  '313': 'Pattern:',
  '314': 'Check PID',
  '315': 'Network Protocol',
  '316': 'URL Path:',
  '317': 'Initial Delay [Seconds]:',
  '318': 'Check Interval [Seconds]:',
  '319': 'Timeout [Seconds]:',
  '320': 'Failure Count:',
  '321': 'Please Select the Associated Plugin',
  '322': 'Please Enter the Action Code',
  '323': 'The Code Supports a Combination of Letters, Numbers, Hyphens, Underscores, and Dots',
  '324': 'Please Enter the Action Name',
  '325': 'Please Enter the Port',
  '326': 'Must be a Non-negative Integer',
  '327': 'Please Enter the Working Directory',
  '328': 'Please Enter the Start Command',
  '329': 'Please Select the Exit Signal',
  '330': 'Please Enter the File Name',
  '331': 'Please Enter the URL Path',
  '332': 'Please Enter the Check Delay Time',
  '333': 'Must be an Integer',
  '334': 'Please Enter the Check Period',
  '335': 'Please Enter the Timeout Duration',
  '336': 'Please Enter the Retry Count',
  '337': 'Config Block Settings',
  '338': 'Load Config Block List',
  '339': 'Unassigned',
  '340': 'Move All to the Right',
  '341': 'Move All to the Left',
  '342': 'Assigned (Priority from High to Low, Number 1 is the Highest)',
  '343': '+!',
  '344': 'Remove!',
  '345': 'Dependency Service Settings',
  '346':
    'Startup Dependency: The service will fail to start if the dependent service is not started; Invocation Dependency: Mainly shows the invocation relationship between services;',
  '347': 'Startup Dependency:',
  '348': ' No Startup Dependencies ',
  '349': 'Invocation Dependency:',
  '350': ' No Invocation Dependencies ',
  '351': 'Loading Dependency Service List',
  '352': 'Startup Dependency',
  '353': 'Invocation Dependency',
  '354': 'Assigned',
  '355': 'Note: The content of the template file will be written into the file path after replacing the attribute variables',
  '356': 'FilePath:',
  '357': 'Encoding:',
  '358': 'Default: utf-8',
  '359': 'Permissions:',
  '360': 'Default: Read and Write',
  '361': 'Owner:',
  '362': 'Default: agent start user',
  '363': 'Content:',
  '364': 'Expand',
  '365': 'Collapse',
  '366': 'Remove This Template File',
  '367': 'File path cannot be empty',
  '368': 'This Config Only Takes Effect for SpringBoot Projects Using Logback, Log Levels: OFF, ERROR, WARN, INFO, DEBUG, TRACE',
  '369': 'Service Level Log Level (Highest Priority)',
  '370': 'Available plugin variable placeholders or custom properties references, such as ${my.prop.key}; Properties priority from high to low',
  '371': 'Service Level Properties (Highest Priority)',
  '372': 'Config Block:',
  '373': 'Loading Repository File List',
  '374':
    'Note: Associated files will be downloaded from the repository to the target directory; Files with a red border are not found in the resource repository',
  '375': 'FileName:',
  '376': 'Please Enter File Name',
  '377': 'Target:',
  '378': 'Remove This Associated File',
  '379': 'File Name Cannot Be Empty',
  '380': 'Target Directory Cannot Be Empty',
  '381': 'Agent IP',
  '382': 'Service Port',
  '383': 'Extended Port [Starting from 1]',
  '384': 'WorkHome',
  '385': 'Skynet Root Directory',
  '386': 'Skynet Temporary Directory',
  '387': 'Current ZK Address',
  '388': 'Current ZK JAAS Authentication Config File',
  '389': 'Cluster Name - ZK Root Path',
  '390': 'Console Address',
  '391': 'Local Agent Address',
  '392': 'Code',
  '393': 'Action Code',
  '394': 'Action Point',
  '395': 'Service Instance Id',
  '3951': 'Service Instance Index',
  '396': 'Application Root Directory',
  '397': 'Application HOME Directory',
  '398': 'Default Directory for Extended Config',
  '399': 'Config Service Address',
  '400': 'Local Config File',
  '401': 'Skynet Log Config File',
  '402': 'Hierarchical Config Address',
  '403': 'Predefined ENV',
  '404': 'Click to Copy',
  '405': 'Example',
  '406': 'Example:',
  '407': 'e.g., turing-tts supports a combination of letters, numbers, hyphens, and underscores',
  '408': 'Name',
  '409': 'e.g., AI Capability - Speech Synthesis',
  '410': 'Description',
  '411': 'Version',
  '412': 'e.g., v2.1.1008',
  '413': 'Please Enter Plugin Code',
  '414': 'Supports a combination of letters, numbers, hyphens, and underscores, up to 80 characters',
  '415': 'Please Enter Plugin Name',
  '416': 'Up to 80 Characters',
  '417': 'Up to 200 Characters',
  '418': 'Up to 100 Characters',
  '419': 'Application Plugin / New',
  '420': ' / View',
  '421': 'This Config only takes effect for SpringBoot projects using Logback, Log Levels: OFF, ERROR, WARN, INFO, DEBUG, TRACE',
  '422': 'Show MD5 Files',
  '4221': 'Show Archived Files',
  '4222': 'Show Archived Action',
  '423': 'Download',
  '424': 'Resource File Directory:',
  '425': '[*.md5 files are automatically generated by the plugin, no need to upload].',
  '426': 'File Name',
  '427': 'File Size',
  '428': 'Upload Time',
  '429': 'Modification Time',
  '430': 'Download',
  '431': 'Drag Files Here, or',
  '432': "'The Last Upload Has Not Completed",
  '433': "'And",
  '434': ' Files The Last Upload Has Not Completed',
  '435': 'Confirm Overwriting the Following Files?',
  '436': 'Confirm Deleting the Following Files?',
  '437': '] Health Logs:',
  '438': '] Standard Output:',
  '439': '] Service Logs:',
  '440': 'View Status',
  '441': 'ConsoleLog',
  '442': 'Restart',
  '443': 'Enable',
  '444': 'Disable',
  '445': 'Start/Stop - Enable Service',
  '446': 'Start/Stop - Disable Service',
  '447': 'Please Select Service Instance',
  '448': 'Unsupported Operation',
  '449': 'Service',
  '450': 'Success, Refreshing View in 2 Seconds',
  '451': 'Service Restart Successful, Refreshing View in 2 Seconds',
  '452': 'Service Restart Failed, Refreshing View in 2 Seconds',
  '453': 'Service Restart Failed on Some Nodes, Refreshing View in 2 Seconds',
  '454': 'Service Restart Partially Successful on Nodes: %s',
  '455': 'Unrecognized Response',
  '456': 'Failed, Refreshing View in 2 Seconds',
  '457': 'Following Services?',
  '458': ' on ',
  '459': 'Loading Service Definition List',
  '460': 'Select Service',
  '461': 'Select Server Tag',
  '462': 'for',
  '463': 'Assign service instance',
  '464': 'Service assignment',
  '465': 'Failed to retrieve service list',
  '466': 'Core|',
  '467': '[No server tags]',
  '468': 'Service assignment successful, view will refresh automatically in 2 seconds',
  '469': 'Service assignment failed',
  '470': 'Service assignment partially successful, view will refresh automatically in 2 seconds',
  '471': 'Select application plugin',
  '472': 'for',
  '473': '] Assign service',
  '474': 'Cluster',
  '475': 'Server',
  '476': ' tags',
  '477': ' ',
  '478': ' ',
  '479': '[Disabled]',
  '480': 'Show',
  '481': 'Group',
  '482': 'Tile',
  '483': 'Collapse',
  '484': 'MultiSelect',
  '485': 'SelectAll',
  '486': 'ServicesCounter',
  '487': 'Normal',
  '488': 'Abnormal',
  '489': 'Stop',
  '490': 'Starting',
  '491': 'Failed to start',
  '492': 'View - Service definition',
  '493': 'View - Service properties',
  '494': 'View - Startup details',
  '495': 'View - Workload',
  '496': 'View - Health check',
  '497': 'View - Service log',
  '498': 'View - Standard output',
  '499': 'View - Monitoring chart',
  '500': 'Start/Stop - Restart service',
  '501': 'Health check output / ',
  '502': 'Standard output / ',
  '503': 'Service log / ',
  '504': 'Skynet-monitoring/dashboard service not started',
  '505': 'Service monitoring/',
  '506': 'Startup Details',
  '507': 'Startup CMD',
  '508': 'View Log',
  '509': 'Copy Command',
  '510': 'Detailed Data',
  '511': 'Failure Reason',
  '512': 'Failed to get service startup details',
  '513': 'Click to Copy',
  '514': 'ServiceId',
  '515': 'ServiceType',
  '516': 'IPPoint',
  '517': 'DnsAddress',
  '518': 'StartupTime',
  '519': 'UpDuration',
  '520': 'Homepage',
  '521': 'Service Properties',
  '522': 'Workload',
  '523': 'Service Log',
  '524': 'StandardOutput',
  '525': 'HealthCheck',
  '526': 'Monitoring',
  '527': 'Request executed successfully:',
  '528': 'Request execution failed',
  '529': 'Are you sure to execute [',
  '530': '] operation?',
  '531': 'Failed to get node details',
  '532': 'Type ',
  '533': '[Click to Select Execution]',
  '534': 'Output',
  '535': 'Description: ',
  '536': 'Command: ',
  '537': 'Overview',
  '538': 'System Environment',
  '539': 'Host Monitoring',
  '540': 'Event Notification',
  '541': 'Deploy Logs',
  '542': ']ant-xagent@ant] Service Log:',
  '543': '][ant-xagent@ant] Event Notification:',
  '544': '][ant-xagent@ant] Deploy Logs:',
  '545': 'Education',
  '546': '96 Cores | 254GB Memory | 0xGPU',
  '547': '7 days 12 hours 5 minutes 12 seconds',
  '548': 'Skynet Monitoring/Host Monitoring',
  '549': 'Name:',
  '550': 'OS:',
  '551': 'HW:',
  '552': 'URL:',
  '553': 'ActionPoint:',
  '554': 'Note:',
  '555': 'SSH User:',
  '556': 'SSH Port:',
  '557': 'Tags',
  '558': 'Version Information',
  '559': 'Version:',
  '560': 'SkynetVersion:',
  '561': 'Runtime Information',
  '562': 'Status:',
  '563': 'StartTime:',
  '564': 'Duration:',
  '565': 'ZK Address:',
  '566': 'Add Node',
  '567': 'Edit Node',
  '568': 'Node Type',
  '569': 'IP',
  '570': 'Please Input Agent IP',
  '571': 'SSH Port',
  '572': 'Please Input SSH port',
  '573': 'SSH User',
  '574': 'Please Input SSH user',
  '575': 'Please input the content of the ~/.kube/config file',
  '576': 'Image Address',
  '577': 'Please Input image repository address',
  '578': 'Image Username',
  '579': 'Please Input image repository username',
  '580': 'Image Password',
  '581': 'Please Input image repository password',
  '582': 'Keep this input empty if not changing the password',
  '583': 'SSH Password',
  '584': 'Please Input SSH Password',
  '585': 'Remarks',
  '586': 'Please Input Description',
  '587': 'Server Tags',
  '588': 'Auto Distribute',
  '589': 'Auto Install Docker',
  '590': 'Test SSH Connection',
  '591': 'Test Kubernetes Connection',
  '592': 'Invalid IP format',
  '593': 'Node already exists',
  '594': 'Port range 0~65535',
  '595': 'Username cannot exceed 32 characters',
  '596': 'Remarks cannot exceed 70 characters',
  '597': 'Please enter KubeConfig content',
  '598': 'Loading list',
  '599': 'Status',
  '600': 'Select Status',
  '601': 'IP:',
  '602': 'Please enter IP',
  '603': 'Add',
  '604': 'Start',
  '605': 'UpdateVersion',
  '606': 'Terminal',
  '607': 'Name',
  '608': 'HW',
  '609': 'OS',
  '610': 'Version',
  '611': 'Terminal',
  '612': 'Cannot delete a running node, please stop the node first',
  '613': 'Can only update offline nodes, please wait for the update to finish or stop the online node',
  '614': 'Please select a node',
  '615': 'Update',
  '616': 'Failure',
  '617': 'Success',
  '618': ' nodes,',
  '619': ' nodes',
  '620': 'Confirm',
  '621': 'the following nodes?',
  '622': 'Force update',
  '623': 'Install Docker',
  '624': 'Confirm update of the following nodes?',
  '625': 'Host',
  '626': 'CPU (used/capacity)',
  '627': 'Memory (used/capacity)',
  '628': 'Loading data...',
  '629': 'Namespace:',
  '630': 'All',
  '631': 'Copy to clipboard',
  '632': 'Preview simplified YAML',
  '633': 'Hide status field',
  '634': ' Select YAML file ',
  '635': ' Cancel ',
  '636': ' OK ',
  '637': ' Edit YAML ',
  '638': 'Please enter name',
  '639': 'Create',
  '640': 'Name',
  '641': 'Namespace',
  '642': 'Cannot Modify',
  '643': 'Tags',
  '644': 'Created',
  '645': 'View YAML',
  '646': 'Config dictionary: ',
  '647': 'Create from YAML',
  '648': 'Deleting Config dictionary...',
  '649': 'Config dictionary [ ',
  '650': ' ] successfully deleted, page will refresh automatically in 2 seconds.',
  '651': 'Confirm delete Config dictionary: [ ',
  '652': 'Min/Max/Current replicas',
  '653': 'Auto-scaling: ',
  '654': 'Deleting HPA auto-scaling...',
  '655': 'HPA auto-scaling [ ',
  '656': 'Confirm delete HPA auto-scaling: [ ',
  '657': 'Secret: ',
  '658': 'Deleting secret Config...',
  '659': 'Secret Config [ ',
  '660': 'Confirm delete secret Config: [ ',
  '661': 'Custom resource: ',
  '662': 'Deleting custom resource...',
  '663': 'Custom resource [ ',
  '664': 'Confirm delete custom resource: [ ',
  '665': '[Workload-DaemonSet]',
  '666': ' Log/Terminal',
  '667': ' Adjust Image Version ',
  '668': 'Update Strategy',
  '669': 'Restart DaemonSet',
  '670': 'Delete DaemonSet',
  '671': 'Metadata',
  '672': 'Runtime',
  '673': 'Loading...',
  '674': 'Loading Workload-DaemonSet YAML',
  '675': 'Workload-DaemonSet: ',
  '676': 'Loading Pod List...',
  '677': 'Restarting Workload-DaemonSet...',
  '678': 'Workload-DaemonSet ',
  '679': ' successfully restarted, page will refresh automatically in 2 seconds',
  '680': 'Confirm restart Workload-DaemonSet: [',
  '681': 'Deleting Workload-DaemonSet...',
  '682': ' successfully deleted',
  '683': 'Confirm delete Workload-DaemonSet: [',
  '684': '[Workload-Deployment]',
  '685': 'Scale',
  '686': 'Auto-scaling [HPA] ',
  '687': 'History versions',
  '688': 'Restart deployment',
  '689': 'Delete deployment',
  '690': 'Loading Workload-Deployment YAML',
  '691': 'Workload-Deployment: ',
  '692': 'Loading HPA YAML',
  '693': 'Edit HPA YAML: ',
  '694': 'Create HPA YAML: ',
  '695': 'Restarting Workload-Deployment...',
  '696': 'Workload-Deployment ',
  '697': 'Confirm restart Workload-Deployment: [',
  '698': 'Deleting Workload-Deployment...',
  '699': 'Confirm delete Workload-Deployment: [',
  '700': 'Updating update strategy',
  '701': 'Please select update strategy type',
  '702': 'Manual Pod Deletion Before Update [OnDelete]',
  '703': 'Rolling Update [RollingUpdate]',
  '704': 'Update Strategy: ',
  '705': ' When OnDelete is used as the upgrade strategy, after the new DaemonSet Config is created, ',
  '706': ' the new Pod will not be created until the user manually deletes the old version of the Pod, triggering the creation of the new Pod, ',
  '707': ' that is, the new Pod replica will only be created after manually deleting the Pod replicas created by DaemonSet.',
  '708': ' It is a smooth upgrade strategy that ensures service availability during the update process.',
  '709': ' Default option. When RollingUpdate is used as the upgrade strategy to update the DaemonSet, ',
  '710': ' the old version of the Pod will be automatically "killed", and then the new version of the DaemonSet Pod will be automatically created.',
  '711': ' The entire process is as controllable as a regular Deployment rolling upgrade.',
  '712': 'Maximum Unavailable Pods',
  '713': 'Maximum Unavailable Pods [maxUnavailable]',
  '714': ' It can be an absolute value (e.g., 3) or a percentage (e.g., 10%).',
  '715': ' If it is a percentage, Kubernetes will round it up to an absolute value.',
  '716': 'Update strategy update successful',
  '717': ' Current Replica Sets: ',
  '718': ', Maximum Historical Replica Sets: ',
  '719': 'Maximum Historical Replica Sets',
  '720': 'Creation Time',
  '721': 'Expected/Current/Ready',
  '722': 'Rollback to #',
  '723': 'Deleting...',
  '724': 'Adjusting maximum historical replica sets..',
  '725': 'Maximum historical replica sets successfully adjusted to ',
  '726': 'Loading Replica Set YAML...',
  '727': 'Workload-Replica Set: ',
  '728': 'Are you sure you want to delete [',
  '729': '] replica set?',
  '730': 'Successfully deleted [',
  '731': '] replica set',
  '732': 'Rollback in progress...',
  '733': 'Successfully rolled back to [',
  '734': 'Recreate',
  '735': 'This is an update strategy that completely deletes the old container group and creates a new container group.',
  '736': ' This update strategy does not guarantee service availability because the service may be interrupted during the update process.',
  '737':
    'In rolling update, Kubernetes updates each container in the container group sequentially and checks whether the new version of the container can work properly during the update process.',
  '738': ' If the new version does not work properly, Kubernetes will roll back to the old version.',
  '739': 'Summary:',
  '740':
    'The rolling update strategy is safer and ensures service availability, while the recreate strategy updates faster but does not guarantee service availability.',
  '741': 'Maximum Surge Pods',
  '742': 'Maximum Surge Pods [maxSurge]',
  '743': ' During the rolling update process, the maximum value that can exceed the expected number of pods.',
  '744': ' This value can be an absolute value (e.g., 3) or a percentage relative to the expected number of pods (e.g., 15%);',
  '745':
    ' if a percentage is entered, the corresponding absolute value is calculated by rounding up the expected number of pods multiplied by that percentage;',
  '746': ' When the maximum unavailable pods maxUnavailable is 0, this value (maxSurge) cannot be 0; the default value is 25%.',
  '747': ' For example: Suppose this value is set to 30%, when the rolling update starts, the new replica set can immediately scale up,',
  '748': ' but the total number of old and new pods does not exceed 130% of the Deployment expected replica set (spec.replicas).',
  '749':
    ' Once the old pod is terminated, the new replica set can further scale up, but throughout the rolling update process, the total number of old and new pods',
  '750': ' does not exceed 130% of the Deployment expected replica set (spec.replicas).',
  '751': 'Maximum value of unavailable replicas during rolling update.',
  '752':
    ' If a percentage is entered, the corresponding absolute value is calculated by rounding down the expected number of replicas multiplied by that percentage;',
  '753': ' When the maximum surge of replicas maxSurge is 0, this value (maxUnavailable) cannot be 0; the default value is 25%;',
  '754':
    ' For example: Suppose this value is set to 30%, when the rolling update starts, the old replica set (ReplicaSet) can scale down to 70% of the expected number of replicas;',
  '755': ' during the scaling up of the new replica set, once the new Pods are ready, the old replica set can further scale down,',
  '756':
    ' ensuring that the sum of the ready replicas of the old and new does not fall below 70% of the expected number of replicas throughout the rolling update process.',
  '757': 'Adjusting Image Versions',
  '758': 'Adjusting',
  '759': 'Container Type',
  '760': 'Image',
  '761': 'Current Version',
  '762': 'New Version',
  '763': 'Please enter the new version',
  '764': 'Version number has not been modified',
  '765': 'Will adjust the image version of ',
  '766': ' containers, do you want to continue?',
  '767': 'Confirm image version adjustment',
  '768': 'Container Logs',
  '769': 'Terminal Access',
  '770': 'Close Window',
  '771': 'Updating replica count',
  '772': 'Current replica count:',
  '773': 'Adjust to:',
  '774': 'Replica count successfully updated to ',
  '775': 'Confirm Rollback',
  '776': 'Current Version #',
  '777': 'Target Version #',
  '778': ' If the .spec.updateStrategy.type field of the StatefulSet is set to OnDelete,',
  '779': ' when you modify the .spec.template content, the StatefulSet Controller will not automatically update its Pods.',
  '780': ' You must manually delete the Pods. At this time, when the StatefulSet Controller recreates the Pods,',
  '781': ' it uses the modified .spec.template content to create new Pods.',
  '782': ' The default value of the .spec.updateStrategy.type field is RollingUpdate,',
  '783': ' which implements automatic rolling updates of Pods for StatefulSet.',
  '784':
    ' When a user updates the .spec.tempalte field of the StatefulSet, the StatefulSet Controller automatically deletes and rebuilds each Pod in the StatefulSet.',
  '785': ' The processing sequence is as follows:',
  '786':
    ' Starting from the Pod with the highest sequence number, each Pod is deleted and updated one by one until the Pod with the lowest sequence number is updated.',
  '787': ' Only after the Pods being updated reach the Running and Ready states, will the updating of the preceding Pods continue.',
  '788': 'Sharding',
  '789':
    ' Container groups with a sequence number greater than or equal to this number will be updated, while those with a lower sequence number will not be updated.',
  '790': ' If the sharding is 0, all container groups will be updated.',
  '791':
    ' If the sharding is 3, container groups with a sequence number greater than or equal to 3 will be updated, while those with a sequence number of 1 or 2 will remain unchanged.',
  '792': 'Workload Deletion Confirmation:',
  '793': 'Workload deleting',
  '794': 'Resource Type:',
  '795': 'Namespace:',
  '796': 'Resource Name:',
  '797': 'Force Delete:',
  '798': 'Confirm Deletion',
  '799': 'Please enter the resource name to confirm deletion',
  '800': 'Container Identifier:',
  '801': 'Image Name:',
  '802': 'Pull Policy:',
  '803': 'StartTime:',
  '804': 'Resource Usage:',
  '805': ' / Memory ',
  '806': 'Container Ports',
  '807': 'Host Ports',
  '808': 'Command',
  '809': '/  Params',
  '810': 'Command Params',
  '811': 'Environment Variables',
  '812': 'Volume Mounts',
  '813': 'Read-Only',
  '814': 'ReadWrite',
  '815': 'Volumes',
  '816': 'Resource Requests',
  '817': 'Resource Limits',
  '818': 'Resource Requests/Limits',
  '819': 'Startup Probe',
  '820': 'Readiness Probe',
  '821': 'Liveness Probe',
  '822': 'Probe',
  '823': '[Not Set]',
  '824': 'Params',
  '825': 'Memory',
  '826': 'Not Configured',
  '827': 'Initial Delay Time',
  '828': 'Seconds',
  '829': ' Not Configured ',
  '830': 'Execution Frequency',
  '831': 'Timeout',
  '832': 'Healthy Threshold',
  '833': 'Unhealthy Threshold',
  '834': 'Tags: ',
  '835': 'Annotations: ',
  '836': 'Workload Name',
  '837': 'Workload Type',
  '838': '[Container Group Info]',
  '839': 'Delete Container Group',
  '840': 'Container Group: ',
  '841': 'Deleting Workload-Container Group...',
  '842': 'Workload-Container Group ',
  '843': 'Confirm deletion of Workload-Container Group: [',
  '844': 'Status: ',
  '845': 'Message:',
  '846': 'Reason:',
  '847': ' Deleting this Pod. Deletion time: ',
  '848': ' seconds',
  '849': 'Container List',
  '850': 'Related Events',
  '851': '[None]',
  '852': 'Container Group IP',
  '853': 'Host',
  '854': 'Created By',
  '855': ' Container Logs:',
  '856': '[Replica Set]',
  '857': 'Created:',
  '858': ' Creation Time:',
  '859': 'Desired:',
  '860': 'Current:',
  '861': 'Ready:',
  '862': 'Delete ',
  '863': 'Container Groups Managed by Replica Set:',
  '864': '[Container Group]',
  '865': 'Started: ',
  '866': ' Start Time:',
  '867': 'Loading Container Group List...',
  '868': 'Loading Workload-Replica Set YAML',
  '869': 'Replica Set Deleting...',
  '870': 'Replica Set ',
  '871': 'Confirm deletion of Replica Set: [',
  '872': 'Loading Workload-Container Group YAML',
  '873': 'Workload-Container Group: ',
  '874': 'Container Group Deleting...',
  '875': 'Container Group ',
  '876': 'Confirm deletion of Container Group: [',
  '877': '[Workload-Stateful Set]',
  '878': 'Restart Stateful Set',
  '879': 'Delete Stateful Set',
  '880': 'Workload-Stateful Set YAML Loading',
  '881': 'Workload-Stateful Set: ',
  '882': 'Workload-Stateful Set Restarting...',
  '883': 'Workload-Stateful Set ',
  '884': 'Confirm restart of Workload-Stateful Set: [',
  '885': 'Workload-Stateful Set Deleting...',
  '886': 'Confirm deletion of Workload-Stateful Set: [',
  '887': 'K8S Cluster [',
  '888': 'Cluster Overview',
  '889': 'Load Overview',
  '890': 'Container Group',
  '891': 'Deployment',
  '892': 'Daemon Set',
  '893': 'Stateful Set',
  '894': 'Cron Job',
  '895': 'Job',
  '896': 'Config',
  '897': 'Config Dictionary',
  '898': 'Secrets',
  '899': 'Auto Scaling',
  '900': 'Network',
  '901': 'Endpoints',
  '902': 'Routes',
  '903': 'Custom Resources',
  '904': 'Namespace: ',
  '905': 'Deleting Namespace...',
  '906': 'Namespace [ ',
  '907': 'Confirm deletion of Namespace: [ ',
  '908': 'Endpoints: ',
  '909': 'Deleting Endpoints...',
  '910': 'Endpoints [ ',
  '911': 'Confirm deletion of Endpoints: [ ',
  '912': 'Routes: ',
  '913': 'Deleting Routes...',
  '914': 'Routes [ ',
  '915': 'Confirm deletion of Routes: [ ',
  '916': 'Cluster IP',
  '917': 'Service: ',
  '918': 'Deleting Service...',
  '919': 'Service [ ',
  '920': 'Confirm deletion of Service: [ ',
  '921': '[Host Details]',
  '922': 'Host YAML',
  '923': 'Resume Scheduling',
  '924': 'Block Scheduling',
  '925': 'Drain Node',
  '926': 'Host Overview',
  '927': 'Host Status',
  '928': 'Node scheduling is blocked!',
  '929': 'Confirm blocking scheduling for this node?',
  '930': 'Node scheduling is resumed!',
  '931': 'Confirm resuming scheduling for this node?',
  '932': 'Node drained successfully!',
  '933': 'Are you sure you want to drain all containers on this node?',
  '934': 'Host YAML: ',
  '935': 'Size',
  '936': 'Basic Information ',
  '937': 'Annotations/Tags',
  '938': 'Annotations:',
  '939': 'Allocatable ',
  '940': ' / Total ',
  '941': ' / Total  ',
  '942': 'Container Group CIDR',
  '943': 'Plugin Architecture',
  '944': 'OS Image',
  '945': 'OS Kernel',
  '946': 'Kernel Container Engine',
  '947': 'Containers',
  '948': ' Start Time: ',
  '949': ' Container Image: ',
  '950': ' Exit Code: ',
  '951': 'Please enter hostname or IP',
  '952': 'Hostname',
  '953': 'Pause Scheduling',
  '954': 'Internal IP',
  '955': 'Kubelet Version',
  '956': 'OS Version',
  '957': 'Resume Scheduling ',
  '958': 'Confirm blocking scheduling for the following nodes?',
  '959': 'Node:',
  '960': 'Confirm resuming scheduling for the following nodes?',
  '961': 'Are you sure you want to drain all containers on the following nodes?',
  '962': 'Reason',
  '963': 'Last Heartbeat Time',
  '964': 'Last Change Time',
  '965': 'Node has insufficient disk space?',
  '966': 'Node healthy and ready to accept new Pods?',
  '967': 'Node memory pressure?',
  '968': 'Too many processes on node?',
  '969': 'Node disk space pressure?',
  '970': 'Node network Config issue?',
  '971': 'CPU Requests',
  '972': 'CPU Limits',
  '973': 'Memory Gib',
  '974': 'Memory Requests Gib',
  '975': 'Memory Limits Gib',
  '976': 'Pods Count',
  '977': 'Image Address:',
  '978': 'Image Username:',
  '979': 'Compute Resources',
  '980': 'Cron Expression',
  '981': 'Suspended',
  '982': 'Last Scheduling Time',
  '983': 'Cron Job: ',
  '984': 'Deleting Workload-Cron Job...',
  '985': 'Workload-Cron Job [ ',
  '986': 'Confirm deletion of Workload-Cron Job: [ ',
  '987': 'Daemon Set: ',
  '988': 'Workload-Daemon Set [ ',
  '989': 'Confirm deletion of Workload-Daemon Set: [ ',
  '990': ' Successfully restarted',
  '991': 'Confirm restart of Workload-Daemon Set: [ ',
  '992': 'Desired',
  '993': 'Current',
  '994': 'Ready',
  '995': 'Workload-Deployment [ ',
  '996': 'Confirm deletion of Workload-Deployment: [ ',
  '997': 'Confirm restart of Workload-Deployment: [ ',
  '998': 'Successful / Total',
  '999': 'Start Time',
  '1000': 'Completion Time',
  '1001': 'Job: ',
  '1002': 'Deleting Workload-Job...',
  '1003': 'Workload-Job [ ',
  '1004': 'Confirm deletion of Workload-Job: [ ',
  '1005': 'Status Statistics',
  '1006': 'Events: ',
  '1007': 'Restart Count',
  '1008': 'Workload-Container Group [ ',
  '1009': 'Confirm deletion of Workload-Container Group: [ ',
  '1010': 'Stateful Set: ',
  '1011': 'Workload-Stateful Set [ ',
  '1012': 'Confirm deletion of Workload-Stateful Set: [ ',
  '1013': 'Confirm restart of Workload-Stateful Set: [ ',
  '1014': 'Stop all managed services simultaneously',
  '1015': 'Confirm stopping the following nodes?',
  '1016': 'Please enter backup name for retrieval',
  '1017': 'Manually create backup',
  '1018': 'Historical Config (Len)',
  '1019': 'Current Config (Len)',
  '1020': 'Compare',
  '1021': 'Cluster-level Property Config',
  '1022': 'Cluster-level Log Config',
  '1023': 'Are you sure you want to restore ',
  '1024': ']?',
  '1025': 'Restore Successful',
  '1026': 'Backup created successfully',
  '1027': 'Are you sure you want to delete backup ',
  '1028': '?',
  '1029': 'Deletion Successful',
  '1030': 'Please enter Config name for retrieval',
  '1031': 'Create New Config',
  '1032': 'Import Config',
  '1033': 'Export Config',
  '1034': 'Delete Selected Config',
  '1035': 'Name:',
  '1036': 'e.g.: Database',
  '1037': 'Code:',
  '1038': 'e.g.: mysql',
  '1039': 'Content:',
  '1040': 'Please select config name',
  '1041': 'Please enter config code',
  '1042': '[Config Name]',
  '1043': 'Export Successful',
  '1044': 'New Config Name',
  '1045': '# Comment\nkey=value',
  '1046': 'Are you sure you want to delete selected Configs?',
  '1047': 'Are you sure you want to delete ',
  '1048': '] Configs?',
  '1049': 'Back to Home',
  '1050': 'Page Not Found!',
  '1051': 'Failed to retrieve data ',
  '1052': 'Clear',
  '1053': 'Download ',
  '1054': 'To Top',
  '1055': 'To Bottom',
  '1056': 'Pause',
  '1057': 'Log file has been deleted.',
  '1058': 'Error deleting log',
  '1059': 'Are you sure you want to delete service logs?',
  '1060': 'Resume',
  '1061': 'Your browser does not support WebSocket',
  '1062': ' Connection Error: %o',
  '1063': 'UserName',
  '1064': 'Password',
  '1065': 'Change Password',
  '1066': 'Login',
  '1067': 'Please enter username',
  '1068': 'Please enter password',
  '1069': 'Login failed: ',
  '1070': 'Request Error: HTTP ',
  '1071': 'Network Error',
  '1072': 'Enter Username',
  '1073': 'Current Pwd',
  '1074': 'Enter Current Password',
  '1075': 'New Pwd',
  '1076': 'Enter New Password',
  '1077': 'Confirm',
  '1078': 'Re-enter new password to confirm',
  '1079': 'Confirm Change',
  '1080': 'Password must be at least 8 characters and contain uppercase, lowercase letters, numbers, and special characters',
  '1081': 'Passwords do not match',
  '1082': 'Username cannot be empty',
  '1083': 'Please enter current password',
  '1084': 'Please enter new password',
  '1085': 'Please re-enter new password to confirm',
  '1086': 'Password change failed: ',
  '1087': 'Failed to change password',
  '1088': 'Service not started',
  '1089': 'Failed to get Grafana address',
  '1090': 'Skynet Monitoring/Dashboard',
  '1091': ' Log in ',
  '1092': 'Latest Updates',
  '1093': 'Product Introduction',
  '1094': 'User Cases',
  '1095': 'Contact Us',
  '1096': 'Personal Center',
  '1097': 'Jump 1',
  '1098': 'Jump 2',
  '1099': 'WebShell feature not enabled',
  '1100': 'Control Panel',
  '1101': 'Host Address:',
  '1102': 'Resource Account:',
  '1103': 'Root Path',
  '1104': 'Please enter folder name',
  '1105': 'Directory',
  '1106': 'Create New Folder',
  '1107': 'Confirm Deletion?',
  '1108': 'Folder name cannot be empty!',
  '1109': 'Modification successful!',
  '1110': 'WebSFTP feature not enabled',
  '1111': 'Login',
  '1112': 'Please enter IP address',
  '1113': 'Connect',
  '1114': 'Please enter host IP',
  '1115': 'Please enter host port',
  '1116': 'Go Up One Level',
  '1117': 'Please enter content to encode or decode',
  '1118': 'Base64 Encode',
  '1119': 'Base64 Decode',
  '1120': 'Copy Output',
  '1121': 'Please enter content to format',
  '1122': 'JSON Formatting',
  '1123': 'String format error',
  '1124': 'Please enter content to encrypt or decrypt',
  '1125': 'MD5 Encryption',
  '1126': 'SkynetSecurity Encryption',
  '1127': 'SkynetSecurity Decryption',
  '1128': 'Password Encryption/Decryption',
  '1129': 'Base64 Encoding/Decoding',
  '1130': 'URL Encoding/Decoding',
  '1131': 'MD5 Encoding/Decoding',
  '1132': 'URI Encoding',
  '1133': 'URI Decoding',
  '1134': 'Close',
  '1135': ' ',
  '1201': 'Loki Log',
  '1202': 'Please select App',
  '1203': 'Please select Pod',
  '1204': 'Please select time',
  '1205': 'Please select direction',
  '1206': 'Please select log limit',
  '1207': 'query',
  '1208': 'after',
  '1209': 'previous',
  '1210': 'item',
  '1211':'Press F1 to display full screen and Esc to exit full screen',
  '1212':'Delete extra files',
  '1213':'Number of instances：',
  '1214':'Mesh Config',
  '1215':'Enable Mesh Mode'
}
export default enLocale
