import locale from '@/i18n/index.js'
import router from '@/router'
import Vue from 'vue'
import StartupCmdDialog from './StartupCmdDialog'
import view from '@/axios/api/view'
import { Message } from 'element-ui'
import { encryptUrl } from '@/utils/auth'
let startupCmdDialogVue
function showActionDef(badgeEvent) {
  router.push({
    name: 'ActionDefDetail',
    params: {
      id: badgeEvent.actionPoint
    },
    query: {
      tabTitle: `${locale.t('243')}${badgeEvent.actionName}`,
      isCreate: false,
      edit: false
    }
  })
}
function showProps(badgeEvent) {
  let url = `http://${badgeEvent.ip}:${badgeEvent.agentPort}/skynet/agent/config/${badgeEvent.actionID}`
  let eUrl = encryptUrl(url)
  window.open(`skynet/proxy/http?u=${eUrl}`, '_blank')
}
function showHealthEvent(badgeEvent) {
  let params = {
    aid: badgeEvent.actionID,
    ip: badgeEvent.ip
  }
  let query = {
    port: badgeEvent.agentPort,
    tabTitle: `${locale.t('501')}${badgeEvent.actionName}`
  }
  router.push({
    name: 'ActionHealth',
    params,
    query
  })
}
function showStdout(badgeEvent) {
  let params = {
    aid: badgeEvent.actionID,
    ip: badgeEvent.ip
  }
  let query = {
    port: badgeEvent.agentPort,
    tabTitle: `${locale.t('502')}${badgeEvent.actionName}`
  }
  router.push({
    name: 'ActionStdout',
    params,
    query
  })
}
function showStdoutLog(badgeEvent) {
  let params = {
    aid: badgeEvent.actionID,
    ip: badgeEvent.ip,
    agentType: badgeEvent.agentType
  }
  let query = {
    port: badgeEvent.agentPort,
    actionPoint: badgeEvent.actionPoint,
    tabTitle: `${locale.t('503')}${badgeEvent.actionName}`,
    agentType: badgeEvent.agentType
  }

  router.push({
    name: 'ActionStdoutLog',
    params,
    query
  })
}
function showStartupCmd(badgeEvent) {
  if (!startupCmdDialogVue) {
    startupCmdDialogVue = new (Vue.extend(StartupCmdDialog))({
      i18n: locale
    })
    startupCmdDialogVue.$mount('#startUpCmdDialog') // See ActionManage.vue
  }
  startupCmdDialogVue.open(badgeEvent)
}
function showDeployment(badgeEvent) {
  let workload = badgeEvent.actionID.replaceAll('@', '-').replaceAll('_', '-')
  if (badgeEvent.workloadType == 'daemonset') {
    router.push({
      name: 'k8sDaemonsetDetail',
      params: {
        ip: badgeEvent.ip,
        namespace: badgeEvent.k8sNamespace,
        daemonset: workload
      }
    })
  } else if (badgeEvent.workloadType == 'statefulset') {
    router.push({
      name: 'k8sStatefulsetDetail',
      params: {
        ip: badgeEvent.ip,
        namespace: badgeEvent.k8sNamespace,
        statefulset: workload
      }
    })
  } else {
    router.push({
      name: 'k8sDeploymentDetail',
      params: {
        ip: badgeEvent.ip,
        namespace: badgeEvent.k8sNamespace,
        deployment: workload
      }
    })
  }
}
function showMonitGraph(badgeEvent) {
  view.getGrafanaAddr().then(d => {
    if (!d || !d.grafanaServer) {
      Message(locale.t('504'))
      return
    }
    let url = `${d.grafanaContextPath}/d/action?refresh=10s&orgId=1&from=now-3h&to=now&var-actionTitle=${badgeEvent.actionName}&var-actionPoint=${
      badgeEvent.actionPoint
    }&var-actionIp=${badgeEvent.ip}&var-actionPid=${badgeEvent.pid}&theme=light&kiosk=tv`
    router.push({
      path: `/external_link/${badgeEvent.ip}/${badgeEvent.actionID}`,
      query: {
        tabTitle: `${locale.t('505')}${badgeEvent.ip}/${badgeEvent.actionName}`,
        url: encodeURI(url)
      }
    })
  })
}
function showHistoryLogEvent(badgeEvent, host) {
  let params = {
    aid: badgeEvent.actionID,
    host: host
  }
  let query = {
    tabTitle: `${locale.t('1201')} / ${badgeEvent.actionName}`
  }
  router.push({
    name: 'HistoryLog',
    params,
    query
  })
}
export default {
  showActionDef,
  showProps,
  showStartupCmd,
  showDeployment,
  showHealthEvent,
  showStdout,
  showStdoutLog,
  showMonitGraph,
  showHistoryLogEvent
}
