<template>
  <div class="footer-statis">
    <span>
      <span>{{ $t('486') }}</span>
      <span class="number">{{ statis.total }}</span>
    </span>
    <div class="splitter" />
    <span>
      <svg-icon class="running" icon-class="yuandianzhong" />
      <span class="number">{{ statis.running }}</span>
    </span>
    <span>
      <svg-icon class="disabled" icon-class="yuandianzhong" />
      <span class="number">{{ statis.disabled }}</span>
    </span>
    <span>
      <svg-icon class="starting" icon-class="yuandianzhong" />
      <span class="number">{{ statis.starting }}</span>
    </span>
    <span>
      <svg-icon class="failed" icon-class="yuandianzhong" />
      <span class="number">{{ statis.failed }}</span>
    </span>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  props: {
    statis: {
      type: Object,
      required: true
    }
  }
}
</script>
<style scoped lang="scss">
.footer-statis {
  .splitter {
    display: inline-block;
    width: 0;
    height: 10px;
    border-left: 1px solid $border-color-light;
    margin-right: 15px;
  }

  .number {
    margin-left: 5px;
  }
  .running {
    color: $status-running-color;
  }
  .disabled {
    color: $status-disabled-color;
  }
  .starting {
    color: $status-starting-color;
  }
  .failed {
    color: $status-failed-color;
  }
}
</style>
