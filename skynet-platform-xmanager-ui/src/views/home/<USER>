<template>
  <div class="home-container">
    <svg class="icon" aria-hidden="true">
      <use xlink:href="#turing-welcome"></use>
    </svg>
    welcome
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import { mapGetters } from 'vuex'
export default {
  name: 'Dashboard',
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['roles'])
  },
  created() {}
}
</script>
<style lang="scss">
.home-container {
  padding: 20px;
}
</style>
