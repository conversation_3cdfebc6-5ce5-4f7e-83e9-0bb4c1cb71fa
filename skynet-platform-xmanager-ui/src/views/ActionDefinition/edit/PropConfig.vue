<template>
  <div class="prop-config">
    <div v-if="edit" class="buttons">
      <el-button size="mini" type="primary" plain icon="el-icon-set-up" @click="onConfig">{{ $t('337') }}</el-button>
    </div>
    <div v-else>
      <prompt class="prompt-wrap">{{ $t('370') }}</prompt>
    </div>
    <!-- 服务级 -->
    <section-container :title="$t('371')" bgColor="inherit" :isCollapsable="false" v-if="defaultConfig && defaultConfig.prismEditor.show">
      <div :class="{ text: true, 'no-data': !data }">
        <prism-editor
          class="my-prism-editor"
          :readonly="!edit"
          v-model="lines"
          @blur="onBlur"
          :highlight="highlighter"
          line-numbers
        ></prism-editor>
      </div>
    </section-container>

    <section-container
      bgColor="inherit"
      :isCollapsable="false"
      v-for="(item, index) in configBlocks"
      :key="index"
      :title="`${$t('372')}${item.name} [${item.code}]`"
      class="config-block"
    >
      <div :class="{ text: true }">
        <prism-editor :readonly="true" class="my-prism-editor" v-model="item.text" :highlight="highlighter" line-numbers></prism-editor>
      </div>
    </section-container>
    <!-- 系统级配置 -->
    <plugin-prop-config
      :pluginCode="pluginCode"
      :initialCollapsed="false"
      v-if="defaultConfig && defaultConfig.pluginConfig.show"
      :showOperations="defaultConfig && defaultConfig.pluginConfig.canEdit"
    ></plugin-prop-config>
    <!-- 集群集配置 -->
    <cluster-prop-config
      :initialCollapsed="false"
      v-if="defaultConfig && defaultConfig.clusterConfig.show"
      :showOperations="defaultConfig && defaultConfig.clusterConfig.canEdit"
    ></cluster-prop-config>
    <ConfigBlockTransfer ref="configBlockTransferRef"></ConfigBlockTransfer>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ClusterPropConfig from '../components/ClusterPropConfig'
import PluginPropConfig from '../components/PluginPropConfig'
import SectionContainer from '@/components/Container/SectionContainer'
import Prompt from '@/components/common/Prompt'
import { highlight, languages } from 'prismjs/components/prism-core'
import ConfigBlockTransfer from './ConfigBlockTransfer'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-properties'
import 'prismjs/themes/prism.css'
export default {
  name: 'PropConfig',
  components: {
    ClusterPropConfig,
    PluginPropConfig,
    SectionContainer,
    Prompt,
    ConfigBlockTransfer
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    pluginCode: {
      type: String
    },
    data: {
      type: String
    },
    edit: {
      type: Boolean,
      default: true
    },
    configBlockCodes: {
      type: Array,
      default: function() {
        return []
      }
    },
    defaultConfig: {
      type: Object,
      default: () => {
        return {
          prismEditor: {
            show: true,
            canEdit: true
          },
          pluginConfig: {
            show: true,
            canEdit: true
          },
          clusterConfig: {
            show: true,
            canEdit: true
          }
        }
      }
    }
  },
  data() {
    return {
      lines: '',
      configBlocks: [],
      loading: false,
      dialogVisible: false
    }
  },
  watch: {
    data(val, oldval) {
      this.lines = this.filterLastLine(val)
    },
    configBlockCodes(codes, oldval) {
      // 从全量配置块列表中筛查依赖的块
      if (codes && Array.isArray(codes)) {
        this.$api.config.getBlocks().then(blockList => {
          let blocks = []
          codes.forEach(c => {
            let arrays = blockList.filter(item => c === item.code)
            if (arrays && arrays.length > 0) blocks.push(arrays[0])
          })
          blocks.forEach(item => {
            if (item.text) item.text = this.filterLastLine(item.text)
          })
          this.configBlocks = blocks
        })
      } else {
        this.configBlocks = []
      }
    }
  },
  methods: {
    highlighter(data) {
      return highlight(data, languages.properties) // languages.<insert language> to return html with markup
    },
    onConfig() {
      this.$refs.configBlockTransferRef.open(this.configBlockCodes || [], codes => this.$emit('updateConfigBlockCodes', codes))
    },
    onBlur() {
      this.$emit('change', this.lines)
    },
    refreshClusterProps() {
      this.clusterLoading = true
      let thisVue = this
      this.$api.cluster.getClusterProps().then(d => {
        thisVue.clusterProps = d
        thisVue.clusterLoading = false
      })
    },
    refreshPluginProps() {
      if (this.pluginCode) {
        this.pluginLoading = true
        let thisVue = this
        this.$api.plugin.getPlugin(this.pluginCode).then(d => {
          thisVue.pluginProps = d['properties']
          thisVue.pluginLoading = false
        })
      }
    },
    filterLastLine(data) {
      let ret = data || ''
      // 去除最后一个\n（后台提供数据的缺陷)
      if (ret.length > 0 && ret[ret.length - 1] === '\n') {
        ret = ret.substring(0, ret.length - 1)
      }
      return ret
    }
  }
}
</script>
<style scoped lang="scss">
.buttons {
  height: 43px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid $border-color-light;
}

.buttons,
.tmpl-propmt {
  margin-bottom: 15px;
}

.prop-config {
  padding: 0 20px 0 0;

  .text {
    width: calc(100% - 80px);
    // min-height: 90px;
    // margin-left: 13px;
    // border: 1px dashed $border-color-dark;
    // padding: 5px 15px;
    color: #909399;

    .line {
      height: 25px;
      line-height: 25px;
    }
  }

  .no-data {
    color: $border-color-dark;
  }

  .prompt-wrap {
    margin-bottom: 10px;
  }
}

.edit-mode {
  background-color: #efefef;
}
.config-block {
  margin-top: 20px !important;
}
.section-container {
  margin-top: 20px;
}
</style>
