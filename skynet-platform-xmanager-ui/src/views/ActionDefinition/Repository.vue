<template>
  <div class="file-repo" v-loading="loading">
    <div class="operations">
      <div class="left">
        <el-input
          :placeholder="$t('203')"
          v-model="searchInput"
          size="mini"
          style="width:200px"
          clearable
          @clear="searchKeyword = null"
          @keyup.enter.native="searchKeyword = searchInput"
        >
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor:pointer"
             @click.stop="searchKeyword = searchInput"></i>
        </el-input>
        <el-checkbox v-model="showMD5File" style="margin-left: 5px">{{ $t('422') }}</el-checkbox>
        <el-checkbox v-model="showArchived" style="margin-left: 5px">{{ $t('4221') }}</el-checkbox>
        <span>({{ archivedCount }}) </span>
      </div>
      <div class="right">
        <span>Num:<b> {{ availableCount }}</b></span>
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-download" @click="onDownloadCompress()">{{
            $t('423')
          }}
        </el-button>
        <el-button
          size="mini"
          plain
          type="danger"
          icon="el-icon-delete"
          :disabled="!tableSelection || tableSelection.length === 0"
          @click="doDelete(tableSelection)"
        >{{ $t('65') }}
        </el-button
        >
        <el-button
          size="mini"
          plain
          type="danger"
          icon="el-icon-delete"
          @click="deleteAllOther"
        >{{ $t('1212') }}
        </el-button
        >
      </div>
      <div class="clear"></div>
      <prompt class="prompt-wrap" @click="onCopy(path)">{{ $t('424') }}{{ path }}{{ $t('425') }}</prompt>
    </div>
    <div class="table ele-mod">
      <el-table
        max-height="100%"
        height="100%"
        header-row-class-name="headbg"
        :data="rendering"
        @select="onTableRowSelect"
        @select-all="onTableRowSelect"
      >
        <el-table-column type="selection" width="30" align="center"></el-table-column>
        <el-table-column type="index" :label="$t('228')" width="50" align="center"></el-table-column>
        <el-table-column prop="fileName" :label="$t('426')" min-width="200" :sortable="true">
          <template #default="scope">
            <span style="color:#3b74f0" :class="{ 'archived': scope.row.archived }">
                       <i v-if="isUse(scope.row)" class="el-icon-success"></i>{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="__view_filesize"
          :label="$t('427')"
          width="200"
          :sortable="true"
          :sort-method="
            (a, b) => {
              return a.fileSize - b.fileSize
            }
          "
        ></el-table-column>
        <el-table-column prop="createTime" :label="$t('428')" width="180" :sortable="true"></el-table-column>
        <el-table-column prop="lastUpdateTime" :label="$t('429')" width="180" :sortable="true"></el-table-column>
        <el-table-column :label="$t('239')" width="130" align="center">
          <template slot-scope="scope">
            <span class="link-type"
            ><a href="javascript:void(0)" @click="onDownload(scope.row)">{{ $t('430') }}</a></span
            >
            <span class="link-type" @click="onDeleteOne(scope.row)"
            ><a href="javascript:void(0)">{{ $t('65') }}</a></span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-upload
      class="upload"
      action="/__not_exist_path"
      drag
      :auto-upload="false"
      :show-file-list="false"
      multiple
      :on-change="onUploadChange"
      ref="uploadRef"
    >
      <i class="el-icon-upload"></i>
      <span class="el-upload__text"
      >{{ $t('431') }}<em>{{ $t('131') }}</em></span
      >
    </el-upload>
    <el-popover v-model="popoverShow" popper-class="mypopper" trigger="manual" placement="bottom-end"
                :visible-arrow="false">
      <div class="hidden-anchor" slot="reference"></div>
      <div class="pop-content">
        <div class="header"><i class="el-icon-close" @click="popoverShow = false"/></div>
        <div v-for="(item, index) in percentages" :key="'uploading-' + index" class="pop-content-item">
          <span class="filename">[{{ index + 1 }}] {{ item.name }}</span>
          <el-progress :text-inside="true" :stroke-width="14" :percentage="item.percent"></el-progress>
        </div>
        <el-divider v-if="percentages && successList && percentages.length > 0 && successList.length > 0"></el-divider>
        <div v-for="(item, index) in successList" :key="'success-' + index" class="pop-content-item">
          <span class="filename">[{{ index + 1 }}] {{ item }}</span>
          <i class="el-icon-success" style="color: #67C23A"></i>
        </div>
        <el-divider v-if="successList && failedList && successList.length > 0 && failedList.length > 0"></el-divider>
        <div v-for="(item, index) in failedList" :key="'failed-' + index" class="pop-content-item">
          <span class="filename">[{{ index + 1 }}] {{ item }}</span>
          <i class="el-icon-error" style="color: #F56C6C"></i>
        </div>
      </div>
    </el-popover>
    <confirm-dialog ref="confirmRef"></confirm-dialog>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import Prompt from '@/components/common/Prompt'
import { copyToClip } from '@/common/util'

export default {
  name: 'FileRepo',
  components: {
    ConfirmDialog,
    Prompt
  },
  props: {
    plugin: {
      type: Object
    },
    referencedFiles: {
      type: Array
    }
  },
  data() {
    return {
      list: [],
      successList: [],
      failedList: [],
      percentages: [],
      loading: false,
      searchInput: null,
      searchKeyword: null,
      popoverShow: false,
      tableSelection: [],
      showMD5File: false,
      showArchived: false,
      path: '',
      requests: [],
      // waiting: false, // 正在等待新的upload请求(时长1秒，用以解决el-upload批量上传时on-change多次回调问题)
      // uploading: false // 有文件正在上传中
      uploadChangeHandler: null
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    archivedCount() {
      return this.list.filter(item => item.archived).length
    },
    availableCount() {
      return this.list.length - this.list.filter(item => item.archived).length
    },
    uploadingNameSet() {
      return new Set(
        this.percentages.map(v => {
          return v.name
        })
      )
    },
    rendering() {
      let filteredList = this.list.filter(item => this.showArchived || !item.archived)
      if (!this.showMD5File) {
        filteredList = filteredList.filter(v => {
          return !v.fileName.endsWith('.md5')
        })
      }
      if (this.searchKeyword) {
        filteredList = filteredList.filter(v => {
          return v.fileName.toLocaleLowerCase().indexOf(this.searchKeyword.toLocaleLowerCase()) > -1
        })
      }
      return filteredList
    }
  },
  watch: {
    plugin() {
      this.onRefresh()
    }
  },
  methods: {
    onRefresh() {
      if (this.plugin && this.plugin.code) {
        let thisVue = this
        this.loading = true
        this.$api.repo.getPath(this.plugin.code).then(d => {
          this.path = d
        })
        this.$api.repo
          .getFiles(this.plugin.code)
          .then(d => {
            thisVue.list = d
          })
          .finally(() => {
            thisVue.loading = false
          })
      } else {
        this.list = []
      }
    },
    onUploadChange(file, fileList) {
      if (!this.plugin || !this.plugin.code || file.status !== 'ready') {
        return
      }
      const __this = this
      this.requests.push(file)
      if (!this.uploadChangeHandler) {
        this.uploadChangeHandler = this.handleChange
        setTimeout(() => {
          let handler = __this.handleChange
          __this.uploadChangeHandler = null
          handler()
        }, 500)
      }
    },
    onCopy(text) {
      copyToClip(this, text)
    },
    async handleChange() {
      let requests = await this.checkUploadRequests()
      this.requests = [] // reset
      this.doUpload(requests)
    },
    checkUploadRequests() {
      const __this = this
      return new Promise(resolv => {
        let uploadingRequests = []
        let requests = []
        __this.requests.forEach(v => {
          if (__this.uploadingNameSet.has(v.raw.name)) {
            uploadingRequests.push(v)
          } else {
            requests.push(v)
          }
        })
        if (uploadingRequests.length > 0) {
          let message =
            uploadingRequests.length === 1
              ? `'${uploadingRequests[0].raw.name}${locale.t('432')}`
              : `'${uploadingRequests[0].raw.name}${locale.t('433')}${uploadingRequests.length}${locale.t('434')}`
          __this.$message({
            message,
            type: 'info'
          })
        }
        __this.requests = requests
        if (requests.length === 0) {
          resolv(__this.requests)
        }
        let setOfFileNames = new Set(__this.list.map(v => v.fileName))
        let overrideNames = requests.map(v => v.raw.name).filter(name => setOfFileNames.has(name))
        if (overrideNames.length > 0) {
          let confirmCallback = function() {
            resolv(__this.requests)
          }
          let cancelCallback = function() {
            requests = requests.filter(v => overrideNames.indexOf(v.raw.name) < 0)
            __this.requests = requests
            resolv(__this.requests)
          }
          __this.$refs.confirmRef.open(locale.t('435'), overrideNames, confirmCallback, cancelCallback)
        } else {
          resolv(__this.requests)
        }
      })
    },
    doUpload(requests) {
      let thisVue = this
      for (let file of requests) {
        let percentage = {
          name: file.raw.name,
          percent: 0
        }
        this.percentages.push(percentage)
        this.popoverShow = true
        this.$api.repo
          .upload(this.plugin.code, file.raw, progressEvent => {
            percentage.percent = parseInt((progressEvent.loaded / progressEvent.total) * 100)
          })
          .then(() => {
            let indexOf = thisVue.percentages.findIndex(v => {
              return v.name === file.raw.name
            })
            if (indexOf > -1) {
              thisVue.percentages.splice(indexOf, 1)
              thisVue.successList.push(file.raw.name)
            }
          })
          .catch(() => {
            let indexOf = thisVue.percentages.findIndex(v => {
              return v.name === file.raw.name
            })
            if (indexOf > -1) {
              thisVue.percentages.splice(indexOf, 1)
              thisVue.failedList.push(file.raw.name)
            }
          })
          .finally(() => {
            thisVue.$refs.uploadRef.clearFiles()
            if (thisVue.percentages.length === 0 && thisVue.failedList.length === 0) {
              thisVue.onRefresh()
              setTimeout(() => {
                thisVue.popoverShow = false
                thisVue.successList = []
                thisVue.failedList = []
              }, 5000)
            }
          })
      }
    },
    onTableRowSelect(selection, row) {
      this.tableSelection = selection
    },
    onDeleteOne(data) {
      let filenames = [data.fileName]
      let thisVue = this
      let confirmCallback = function() {
        this.$api.repo.remove(thisVue.plugin.code, data.fileName, data.archived).then(() => {
          thisVue.onRefresh()
        })
      }
      this.$refs.confirmRef.open(locale.t('436'), filenames, confirmCallback)
    },
    doDelete(selection) {
      if (!selection || selection.length === 0) {
        return
      }
      this.deleteFun(selection)
    },
    deleteFun(data) {
      let filenames = data.map(v => {
        return v.fileName
      })
      let thisVue = this
      let confirmCallback = function() {
        this.$api.repo.batchRemove(thisVue.plugin.code, filenames).then(() => {
          thisVue.onRefresh()
        })
      }
      this.$refs.confirmRef.open(locale.t('436'), filenames, confirmCallback)
    },
    deleteAllOther() {
      // 处理不再选中范围内的数据
      let otherArr = this.rendering.filter(item => {
        return !this.isUse(item)
      })
      this.deleteFun(otherArr)
    },
    onDownload(row) {
      this.$api.repo.download(row.plugin, row.fileName, row.archived)
    },
    onDownloadCompress() {
      this.$api.repo.downloadCompress(this.plugin.code)
    },
    isUse(data) {
      return this.referencedFiles.filter((item) => {
        return item.fileName.indexOf(data.fileName) >= 0
      }).length
    }
  }
}
</script>
<style scoped lang="scss">
.operations {
  height: 93px;
  padding: 15px 0;

  .left,
  .right {
    float: left;
  }

  .left {
    width: calc(100% - 500px);
  }

  .right {
    width: 500px;
    text-align: right;
  }
}

.table {
  height: calc(100% - 160px); //.operations height: 58px; .upload height: 67px;
  .tag + .tag {
    margin-left: 5px;
  }
}

.upload {
  padding: 10px;
  // background-color: #f4f4f5;
}

.pop-content {
  width: 350px;
  padding: 10px;
  padding-top: 0;

  .header {
    height: 25px;
    line-height: 25px;
    font-size: 16px;
    text-align: right;

    i {
      cursor: pointer;
    }
  }

  .pop-content-item {
    font-size: 12px;
    text-overflow: ellipsis;

    i {
      margin-left: 12px;
    }
  }

  .pop-content-item + .pop-content-item {
    margin-top: 14px;
  }

  .filename {
    text-overflow: ellipsis;
  }
}
</style>
<style lang="scss">
.file-repo {
  .hidden-anchor {
    position: fixed;
    z-index: -9999;
    visibility: hidden;
    right: 20px;
    top: $headtopHeight;
  }

  .prompt-wrap {
    margin: 10px 0;
  }

  .upload {
    .el-upload {
      height: 100%;
      width: 100%;
    }

    .el-upload-dragger {
      height: 44px;
      line-height: 40px;
      width: 100%;
      border: 2px dashed gray;
      background-color: inherit;

      &:hover {
        border-color: #367ae0;
      }

      i {
        vertical-align: middle;
        font-size: 40px;
        line-height: 40px;
        margin: 0;
      }

      .el-upload__text {
        vertical-align: middle;
      }
    }
  }

  span.archived {
    color: #909399 !important;
  }
}
</style>
