<template>
  <div class="action-list-container" v-loading="loading">
    <div class="operations">
      <div class="sort" v-show="showSortable">
        <el-checkbox v-model="sorting">{{ $t('209') }}</el-checkbox>
        <el-button v-show="sorting" size="mini" icon="el-icon-check" @click="onSortConfirm" type="primary">{{
            $t('75')
          }}
        </el-button>
        <prompt v-show="sorting" class="prompt-wrap">{{ $t('225') }}</prompt>
      </div>
      <div v-show="!sorting" class="filter">
        <el-select v-model="selectedType" :placeholder="$t('226')" size="mini" clearable style="width:120px">
          <el-option v-for="item in typeOpts" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="selectedTag" :placeholder="$t('125')" size="mini" clearable>
          <el-option v-for="item in tagOpts" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input :placeholder="$t('203')" v-model="searchInput" size="mini" style="width:200px" clearable
                  @clear="searchKeyword = null" @keyup.enter.native="searchKeyword = searchInput">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor:pointer"
             @click.stop="searchKeyword = searchInput"></i>
        </el-input>
        <el-checkbox v-model="showArchived" style="margin-left: 5px">{{ $t('4222') }}</el-checkbox>
        <span>({{ archivedCount }}) </span>
      </div>
      <div v-show="!sorting" class="buttons">
        <span>Num:<b> {{ availableCount }}</b></span>
        <el-button type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button type="primary" icon="el-icon-plus" plain @click="onCreate">{{ $t('227') }}</el-button>
        <el-button type="danger" plain icon="el-icon-delete" :disabled="!tableSelection || tableSelection.length === 0"
                   @click="doDelete(tableSelection)">{{ $t('65') }}
        </el-button>
        <el-button type="primary" plain icon="el-icon-download" @click="onExport">{{ $t('207') }}</el-button>
      </div>
      <div class="clear"></div>
    </div>
    <div class="table ele-mod">
      <el-table id="action-list-table" row-key="actionPoint" :data="rendering" header-row-class-name="headbg"
                style="overflow-x:auto; overflow-y:auto" height="100%"
                :row-style="{ cursor: this.sorting ? 'move' : '' }"
                @select="onTableRowSelect" @select-all="onTableRowSelect">
        <el-table-column type="selection" width="30" align="center"></el-table-column>
        <el-table-column type="index" :label="$t('228')" width="50" align="center"></el-table-column>
        <el-table-column prop="actionName" :label="$t('229')" align="left" min-width="140" :sortable="true"
                         :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <i v-if="scope.row.deployed" class="el-icon-success" :title="$t('2291')"></i>
            <i v-if="scope.row.archived" class="el-icon-remove" :title="$t('2292')"></i>
            <el-link style="color:#3b74f0" :class="{ 'archived': scope.row.archived }" :underline="false"
                     @click="onView(scope.row)">{{
                scope.row.actionName
              }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="actionCode" :label="$t('230')" align="center" min-width="140" :sortable="true"
                         :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="__view_type" :label="$t('231')" width="100" :sortable="true"
                         align="center"></el-table-column>
        <el-table-column prop="__view_protocol" :label="$t('232')" width="100" :sortable="true"
                         align="center"></el-table-column>
        <el-table-column prop="port" :label="$t('233')" width="80" align="center"></el-table-column>
        <el-table-column :label="$t('234')" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-for="tag in scope.row.tags" :key="tag" size="mini" class="tag">{{ tag }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('235')" width="95" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.__view_enable_log_collect">{{ $t('236') }}</span>
            <span v-else>{{ $t('237') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('238')" width="85" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.__view_enable_prometheus">{{ $t('236') }}</span>
            <span v-else>{{ $t('237') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('239')" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-dropdown split-button plain size="mini" icon="el-icon-view" @command="handleCommand($event, scope.row)"
                         @click="onEdit(scope.row)">{{ $t('240') }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="el-icon-document-copy" command="copy">{{ $t('241') }}</el-dropdown-item>
                <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
                <el-dropdown-item v-if="!scope.row.archived" icon="el-icon-remove-outline" command="archive">
                  {{ $t('2071') }}
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.archived" icon="el-icon-circle-plus-outline" command="restore">
                  {{ $t('2072') }}
                </el-dropdown-item>
                <el-dropdown-item icon="el-icon-download" command="export">{{ $t('207') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <confirm ref="deleteConfirmRef" :isDragVerify="true"></confirm>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import emptyData from './empty-data'
import { deepCopy, newSortableTable } from '@/common/util'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import Prompt from '@/components/common/Prompt'

export default {
  name: 'ActionList',
  components: {
    confirm: ConfirmDialog,
    prompt: Prompt
  },
  props: {
    plugin: {
      type: Object
    },
    noData: {
      type: Boolean
    },
    showSortable: {
      type: Boolean
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      selectedType: null,
      selectedTag: null,
      searchInput: null,
      searchKeyword: null,
      lastRefreshTimestamp: 0,
      tableSelection: [],
      sorting: false,
      showArchived: false,
      sortingBackup: [],
      sortable: null
    }
  },
  mounted() {
    this.sortable = newSortableTable('action-list-table', this, 'list')
  },
  watch: {
    plugin(val, oldVal) {
      if (val.code !== oldVal.code) {
        this.onRefresh()
      }
    },
    sorting(val) {
      if (val) {
        this.sortable.option('disabled', false)
        this.sortingBackup = Array.from(this.list)
      } else {
        this.sortable.option('disabled', true)
        /**
         * 解决表格数据还原后不重新渲染问题
         */
        this.$nextTick(() => {
          this.list = this.sortingBackup
        })
      }
    }
  },
  computed: {
    archivedCount() {
      return this.list.filter(item => item.archived).length
    },
    availableCount() {
      return this.list.length - this.list.filter(item => item.archived).length
    },
    rendering() {
      let filteredList = this.list
        .filter(item => this.showArchived || !item.archived)

      if (!this.selectedTag && !this.selectedType && !this.searchKeyword) {
        return filteredList
      }

      if (this.selectedType) {
        filteredList = filteredList.filter(item => item.__view_type === this.selectedType)
      }

      if (this.selectedTag) {
        filteredList = filteredList.filter(item => {
          return 'tags' in item && item.tags.findIndex(v => v === this.selectedTag) > -1
        })
      }

      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filteredList = filteredList.filter(item =>
          item.actionName.toLowerCase().includes(keyword) ||
          item.actionCode.toLowerCase().includes(keyword)
        )
      }

      return filteredList
    },

    typeOpts() {
      return [
        {
          value: 'BaseBoot',
          label: 'BaseBoot'
        },
        {
          value: 'SpringBoot',
          label: 'SpringBoot'
        },
        {
          value: 'SkynetBoot',
          label: 'SkynetBoot'
        },
        {
          value: 'DockerBoot',
          label: 'DockerBoot'
        },
        {
          value: 'K8sBoot',
          label: 'K8sBoot'
        }
      ]
    },
    tagOpts() {
      let set = new Set()
      for (let item of this.list) {
        let tags = item.tags
        if (tags && tags.length > 0) {
          for (let tag of tags) {
            set.add(tag)
          }
        }
      }
      return Array.from(set.values())
        .sort()
        .map(v => {
          return {
            label: v,
            value: v
          }
        })
    }
  },
  methods: {
    handleCommand(cmd, actionDefItem) {
      if (cmd === 'copy') {
        this.onCopy(actionDefItem)
      } else if (cmd === 'edit') {
        this.onEdit(actionDefItem)
      } else if (cmd === 'delete') {
        this.doDelete([actionDefItem])
      } else if (cmd === 'archive') {
        this.doArchive([actionDefItem])
      } else if (cmd === 'restore') {
        this.doRestore([actionDefItem])
      } else if (cmd === 'export') {
        this.doExport([actionDefItem.actionPoint])
      }
    }, //
    onEdit(actionDefItem) {
      this.$router.push({
        name: 'ActionDefDetail',
        params: {
          id: actionDefItem.actionPoint
        },
        query: {
          tabTitle: `${actionDefItem.actionName}${locale.t('242')}`,
          isCreate: false,
          edit: true
        }
      })
    },
    onView(actionDefItem) {
      this.$router.push({
        name: 'ActionDefDetail',
        params: {
          id: actionDefItem.actionPoint
        },
        query: {
          tabTitle: `${locale.t('243')}${actionDefItem.actionName}`,
          isCreate: false,
          edit: false
        }
      })
    },
    onCreate() {
      let definition = emptyData
      definition['pluginCode'] = this.plugin ? this.plugin.code : ''
      definition['pluginName'] = this.plugin ? this.plugin.name : ''
      this.$router.push({
        name: 'ActionDefDetail',
        params: {
          id: this.plugin ? this.plugin.code : 'unknown',
          definition
        },
        query: {
          tabTitle: this.plugin && this.plugin.name ? `${this.plugin.name}${locale.t('244')}` : locale.t('245'),
          isCreate: true,
          edit: true
        }
      })
    },
    onCopy(actionDefItem) {
      let dup = deepCopy(actionDefItem)
      dup.actionCode = actionDefItem.actionCode + '--COPY'
      dup.actionName = actionDefItem.actionName + '--COPY'
      this.$router.push({
        name: 'ActionDefDetail',
        params: {
          id: dup.actionCode,
          definition: dup
        },
        query: {
          tabTitle: `${locale.t('243')}${dup.actionName}`,
          isCreate: true,
          edit: true
        }
      })
    },
    onRefresh() {
      /** 使用时间戳来避免比较慢的响应覆盖之前的操作结果 */
      let ts = new Date().getTime()
      if (this.noData) {
        this.list = []
        return
      }
      const sortFunc = (a, b) => {
        return a.actionName.localeCompare(b.actionName)
      }
      let thisVue = this
      this.loading = true
      if (this.plugin && this.plugin.code) {
        this.$api.definition.getActionDefinitions(this.plugin.code).then(d => {
          if (ts > thisVue.lastRefreshTimestamp) {
            thisVue.lastRefreshTimestamp = ts
            // d = d.sort(sortFunc)
            thisVue.list = d
            thisVue.loading = false
            thisVue.setReferencedFiles()
          }
        })
      } else {
        this.$api.definition.getActionDefinitions().then(d => {
          if (ts > thisVue.lastRefreshTimestamp) {
            thisVue.lastRefreshTimestamp = ts
            d = d.sort(sortFunc)
            thisVue.list = d
            thisVue.setReferencedFiles()
            thisVue.loading = false
          }
        })
      }
    },
    setReferencedFiles() {
      let tempArr = []
      this.list.forEach(item => {
        tempArr = tempArr.concat(item.referencedFiles)
      })
      this.$emit('updateReferencedFiles', tempArr)
    },
    // end onRefresh
    onTableRowSelect(selection, row) {
      this.tableSelection = selection
    },
    doRestore(selection) {
      if (!selection || selection.length === 0) {
        return
      }
      let names = selection.map(v => {
        return v.actionName
      })
      let confirmCallback = () => {
        let promises = selection.map(v => {
          return this.$api.definition.restoreActionDefinition(v.actionPoint)
        })
        Promise.allSettled(promises).then(results => {
          let failedCount = results.filter(result => {
            return result.status === 'rejected'
          }).length
          let successCount = results.length - failedCount
          if (failedCount > 0) {
            this.$message({
              message: `${locale.t('2192')}${successCount}${locale.t('246')}${failedCount}${locale.t('2472')}`,
              type: 'error'
            })
          } else {
            this.$message({
              message: `${locale.t('2482')}`,
              type: 'success'
            })
          }
          if (failedCount !== selection.length) {
            this.onRefresh()
          }
        })
      }
      this.$refs.deleteConfirmRef.open(locale.t('2492'), names, confirmCallback)
    },
    doArchive(selection) {
      if (!selection || selection.length === 0) {
        return
      }
      let names = selection.map(v => {
        return v.actionName
      })
      let confirmCallback = () => {
        let promises = selection.map(v => {
          return this.$api.definition.archiveActionDefinition(v.actionPoint)
        })
        Promise.allSettled(promises).then(results => {
          let failedCount = results.filter(result => {
            return result.status === 'rejected'
          }).length
          let successCount = results.length - failedCount
          if (failedCount > 0) {
            this.$message({
              message: `${locale.t('2191')}${successCount}${locale.t('246')}${failedCount}${locale.t('2471')}`,
              type: 'error'
            })
          } else {
            this.$message({
              message: `${locale.t('2481')}`,
              type: 'success'
            })
          }
          if (failedCount !== selection.length) {
            this.onRefresh()
          }
        })
      }
      this.$refs.deleteConfirmRef.open(locale.t('2491'), names, confirmCallback)
    },
    doDelete(selection) {
      if (!selection || selection.length === 0) {
        return
      }
      let names = selection.map(v => {
        return v.actionName
      })
      let confirmCallback = () => {
        let promises = selection.map(v => {
          return this.$api.definition.deleteActionDefinition(v.actionPoint)
        })
        Promise.allSettled(promises).then(results => {
          let failedCount = results.filter(result => {
            return result.status === 'rejected'
          }).length
          let successCount = results.length - failedCount
          if (failedCount > 0) {
            this.$message({
              message: `${locale.t('219')}${successCount}${locale.t('246')}${failedCount}${locale.t('247')}`,
              type: 'error'
            })
          } else {
            this.$message({
              message: `${locale.t('248')}`,
              type: 'success'
            })
          }
          if (failedCount !== selection.length) {
            this.onRefresh()
          }
        })
      }
      this.$refs.deleteConfirmRef.open(locale.t('249'), names, confirmCallback)
    },
    onSortConfirm() {
      let actionCodeList = this.list.map(v => v.actionCode)
      this.$api.view.updateActionOrder(this.plugin.code, actionCodeList).then(() => {
        this.sortingBackup = this.list
        this.sorting = false
      })
    },
    onExport() {
      if (this.tableSelection && this.tableSelection.length > 0) {
        let codes = this.tableSelection.map(v => {
          return v.actionPoint
        })
        this.doExport(codes)
      } else {
        this.$message.info(locale.t('250'))
      }
    },
    doExport(codes) {
      this.$api.definition.exportActionDefinition(codes)
    }
  }
}
</script>
<style lang="scss">
.action-list-container {
  .el-dropdown {
    .el-button-group > .el-button:first-child {
      padding-bottom: 8px;
    }
  }
}
</style>
<style scoped lang="scss">
// .action-list-container{
//   padding: 10px 0;
// }

.operations {
  height: 58px;
  line-height: 28px;
  padding: 15px 0;

  .sort,
  .filter {
    float: left;
  }

  .sort {
    margin-right: 10px;

    .el-checkbox + .el-button {
      margin-left: 20px;
    }
  }

  .buttons {
    float: right;
    // width: 250px;
    text-align: right;
  }
}

.table {
  height: calc(100% - 68px);

  .tag + .tag {
    margin-left: 5px;
  }
}

.prompt-wrap {
  display: inline-block;
  margin-left: 20px;
}

.el-icon-success {
  color: #367AE0;
}

.el-link.archived {
  color: #909399 !important;
}
</style>
