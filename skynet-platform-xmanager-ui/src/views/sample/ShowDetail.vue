<template>
  <div>
    <p>{{ msg }}</p>
    <el-input type="text" v-model="input" width="300"></el-input>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'ShowDetail',
  data() {
    return {
      msg: 'default',
      input: ''
    }
  },
  mounted() {
    //console.log('ShowDetail mounted')
  },
  updated() {
    //console.log('ShowDetail updated')
  },
  created() {
    //console.log('ShowDetail created')
  },
  beforeRouteEnter(to, from, next) {
    let msg = to.params['msg']
    //console.log('beforeRouteEnter : from[%s] to[%s] para[%s]', from.path, to.path, msg)
    next(vm => {
      vm.msg = msg
    })
  },
  beforeRouteUpdate(to, from, next) {
    this.msg = to.params['msg']
    //console.log('beforeRouteUpdate : from[%s] to[%s] para[%s]', from.path, to.path, this.msg)
    next()
  }
}
</script>
