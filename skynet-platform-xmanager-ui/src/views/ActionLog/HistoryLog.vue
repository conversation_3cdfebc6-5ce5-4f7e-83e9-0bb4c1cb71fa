<template>
  <iframe :src="host" ref="iframe" frameborder="0" style="width: 100%; height: 100%"></iframe>
</template>
<script>
export default {
  name: 'HistoryLog',
  components: {},
  data() {
    return {
      host: '',
      param: {}
    }
  },
  computed: {},
  created() {
    this.host = this.$route.params.host
    let aid = this.$route.params.aid
    this.param = {
      aid: aid.replace('@', '-')
    }
  },
  mounted() {
    var _self = this
    let myFrame = this.$refs['iframe']
    if (myFrame.attachEvent) {
      // 兼容浏览器判断
      myFrame.attachEvent('onload', function() {
        const iframeWin = myFrame.contentWindow
        iframeWin.postMessage(_self.param, '*')
      })
    } else {
      myFrame.onload = function() {
        const iframeWin = myFrame.contentWindow
        iframeWin.postMessage(_self.param, '*')
      }
    }
  }
}
</script>
