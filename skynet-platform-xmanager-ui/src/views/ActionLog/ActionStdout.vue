<template>
  <log-view :param="param"></log-view>
</template>
<script>
import locale from '@/i18n/index.js'

import LogView from '../log/LogView'
import { encryptUrl } from '@/utils/auth'
export default {
  name: 'ActionStdout',
  components: {
    LogView
  },
  data() {
    return {
      param: {}
    }
  },
  created() {
    let actionID = this.$route.params.aid
    let ip = this.$route.params.ip
    let port = this.$route.query.port
    let proxyUri = `ws://${ip}:${port}/skynet/agent/stdout/${actionID}`
    let path = window.location.pathname.endsWith('/') ? window.location.pathname : window.location.pathname + '/'
    let eUrl = encryptUrl(proxyUri)
    let logUri = `ws://${window.location.host}${path}skynet/proxy/ws?u=${eUrl}`
    this.param = {
      logUri,
      title: `[${ip}][${actionID}${locale.t('438')}`
    }
  }
}
</script>
