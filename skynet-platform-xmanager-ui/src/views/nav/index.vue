<template>
  <div class="nav-container">
    <div class="logo">
      <img :src="navParam.logo" alt="" />
    </div>
    <div class="right">
      <ul>
        <li v-for="item in navParam.list" :key="item.index">
          {{ item.label }}
        </li>
        <li v-if="!navParam.user.name" @click="login">{{ $t('1091') }}</li>
        <li v-else>
          <el-dropdown>
            <div class="el-dropdown-link">
              <p>{{ navParam.user.name }}</p>
              <img :src="navParam.user.head" /><i class="el-icon-arrow-down el-icon--right"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(i, index) in navParam.user.operate" :key="index" :divided="index != 0">{{ i.label }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import logo from '@/assets/img/logo.png'
import logo_en from '@/assets/img/logo_en.png'
import head from '@/assets/img/head.png'
export default {
  name: 'Nav',
  data() {
    return {
      navParam: {
        logo: logo_en,
        list: [
          {
            label: locale.t('1092'),
            code: '1'
          },
          {
            label: locale.t('1093'),
            code: '2'
          },
          {
            label: locale.t('1094'),
            code: '3'
          },
          {
            label: locale.t('1095'),
            code: '4'
          }
        ],
        user: {
          name: 'dorisZhang',
          head: head,
          operate: [
            {
              label: locale.t('1096'),
              code: 'userCenter'
            },
            {
              label: locale.t('134'),
              code: 'LogOut'
            }
          ]
        }
      }
    }
  },
  watch: {
    activeName(val) {}
  },
  created() {},
  methods: {}
}
</script>
<style lang="scss">
.nav-container {
  background: #263445;
  width: 100%;
  height: 50px;
  overflow: hidden;
  padding-right: 20px;
  box-sizing: border-box;
  img {
    display: block;
    height: $navbarHeight;
    width: auto;
  }
  .logo {
    margin-left: 20px;
    margin-top: 11px;
    float: left;
    cursor: pointer;
    img {
      height: 28px;
    }
  }
  .right {
    float: right;
    ul {
      overflow: hidden;
      line-height: 50px;
      color: #fff;
      font-size: 14px;
      margin: 0;
      li {
        margin-right: 40px;
        float: left;
        cursor: pointer;
        list-style: none;
        p {
          margin: 0;
        }
        &:hover {
          color: #409ee4;
          p {
            color: #409ee4 !important;
            margin: 0;
          }
        }
        &:last-child {
          margin-right: 0;
          .el-dropdown-link {
            overflow: hidden;
          }
          .el-dropdown {
            overflow: hidden;
            p {
              float: left;
              color: #fff;
              margin-right: 10px;
            }
            img {
              float: left;
              margin-top: 5px;
              border-radius: 5px;
            }
            i {
              float: right;
              color: #fff;
              margin-top: 18px;
            }
          }
        }
      }
    }
  }
}
</style>
