<template>
  <div>
    <div class="region">
      <div class="title">{{ $t('277') }}</div>
      <div class="content">
        <div class="item">
          <span class="label">{{ $t('549') }}</span>
          <span class="value">{{ overview.__view_hostname }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('550') }}</span>
          <span class="value">{{ overview.__view_os }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('551') }}</span>
          <span class="value">{{ overview.__view_hardware }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('552') }}</span>
          <span class="value">{{ overview.ip }}:{{ overview.serverPort }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('553') }}</span>
          <span class="value">{{ actionPoint }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('554') }}</span>
          <span class="value">{{ overview.description }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('977') }}</span>
          <span class="value">{{ overview.registryUrl }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('978') }}</span>
          <span class="value">{{ overview.registryUsername }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('557') }}</span>
          <span class="value">{{ overview.serverTags }}</span>
        </div>
        <div class="clear" />
      </div>
    </div>
    <div class="region">
      <div class="title">{{ $t('558') }}</div>
      <div class="content">
        <div class="item">
          <span class="label">{{ $t('559') }}</span>
          <span class="value">{{ overview.version.projectVersion }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('560') }}</span>
          <span class="value">Skynet-{{ overview.version.skynetBootVersion }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('139') }}</span>
          <span class="value">{{ overview.version.buildSid }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('137') }}</span>
          <span class="value">{{ overview.version.buildBranch }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('138') }}</span>
          <span class="value">{{ overview.version.buildNumber }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('140') }}</span>
          <span class="value">{{ overview.version.buildTime }}</span>
        </div>
      </div>
    </div>
    <div class="region">
      <div class="title">{{ $t('561') }}</div>
      <div class="content">
        <div class="item">
          <span class="label">{{ $t('562') }}</span>
          <span class="value">{{ overview.status === 'ONLINE' ? $t('9') : overview.status === 'OFFLINE' ? $t('10') : $t('11') }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('563') }}</span>
          <span class="value">{{ overview.startupTime }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('564') }}</span>
          <span class="value">{{ overview.runningDuration }}</span>
        </div>
        <div class="item">
          <span class="label">{{ $t('565') }}</span>
          <span class="value">{{ overview.zkServers }}</span>
        </div>
        <div class="clear" />
      </div>
    </div>

    <div class="region">
      <div class="title">{{ $t('979') }}</div>
      <div class="content">
        <cluster-status :ip="ip"></cluster-status>
      </div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import ClusterStatus from './ClusterStatus.vue'
export default {
  name: 'AgentOverview',
  components: {
    ClusterStatus
  },
  data() {
    return {
      loading: false,
      overview: {
        version: {},
        springbootEndpoints: []
      },
      actionPoint: 'ant-xagent@ant'
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    }
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.agent
        .getAgentByIp(this.ip)
        .then(res => {
          this.overview = res
          this.initStateData()
        })
        .finally(() => {
          this.loading = false
        })
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    initStateData() {
      if (this.overview.status === 'ONLINE') {
        this.$api.runtime.getNodeStatus(this.overview.ip, this.overview.serverPort, '').then(data => {
          let nodeData = data
            ? JSON.parse(data)
            : {
              startTime: '',
              upTime: '',
              zkServers: ''
            }
          this.$set(this.overview, 'startupTime', nodeData.startTime.substring(0, 19).replace('T', ' '))
          this.$set(this.overview, 'runningDuration', nodeData.upTime)
          this.$set(this.overview, 'zkServers', nodeData.zkServers)
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.title {
  font-weight: bold;
  font-size: 16px;
  height: 18px;
  line-height: 18px;
  border-left: 3px solid #367ae0;
  margin: 20px 0;
  padding-left: 10px;
}

.content {
  font-size: 14px;
}

.item {
  display: inline-block;
  padding: 4px;
  width: 33%;
  text-align: left;
  white-space: nowrap;

  .label {
    width: 20%;
    color: #7a8599;
  }

  .value {
    width: 80%;
    color: #42526e;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
