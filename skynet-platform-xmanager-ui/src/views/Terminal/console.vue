<template>
  <div class="terminal-contanier">
    <div id="terminalHeader" class="terminal-header">
      <span class="terminal-ip">
        <i class="iconfont" :class="[loginSuccess ? 'icon-success-fill' : 'icon-reeor-fill']"></i>
        <span>{{ title }} </span>
      </span>
    </div>
    <div id="terminalContanierBox">
      <div id="xterm" class="xterm"></div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

// path: '/#/kubernetes/:ip/node/:node/console'
// path: '/#/kubernetes/:ip/namespace/:namespace/pod/:pod/container/:container/console',
// 集成使用说明：
// 主机：必选：ip ，node
// http://************:9527/#/console?ip=*************&node=turing.cloud.053-no.03.novalocal
// 容器器：必选：ip pod 可选：namespace(默认skynet-system)，container（默认pod内第一个）
// http://************:9527/#/console?ip=*************&namespace=kuboard&pod=kuboard-pv-browser-d7jqx&container=delegator
import { refreshToken } from '@/axios/api/auth'
import ui from '@/axios/api/ui'
import 'xterm/css/xterm.css'
import { Terminal } from 'xterm'
import { FitAddon } from 'xterm-addon-fit'
export default {
  name: 'Terminal',
  components: {},
  data() {
    return {
      target: {},
      socket: null,
      term: null,
      fitAddon: null,
      row: 80,
      col: 40,
      wp: 0,
      hp: 0,
      commandCount: 0,
      loginSuccess: false,
      title: ''
    }
  },
  async mounted() {
    let resp = await ui.getUIVariables()
    if (!resp.k8s_console_enabled) {
      alert(locale.t('1099'))
      window.open('', '_self').close()
      return
    }
    this.sizeInit()
    this.wsInit()
  },
  methods: {
    sizeInit() {
      // 根据屏幕大小动态计算 tty size
      this.wp = document.getElementById('terminalContanierBox').offsetWidth
      this.hp = document.getElementById('terminalContanierBox').offsetHeight - document.getElementById('terminalHeader').offsetHeight
      this.col = parseInt(this.wp / 9) - 6
      this.row = parseInt(this.hp / 16) - 6
      if (this.fitAddon) {
        this.fitAddon.fit()
      }
    },
    wsInit() {
      if (typeof WebSocket === 'undefined') {
        alert(locale.t('1061'))
        window.open('', '_self').close()
        return
      } else {
        this.target.ip = this.$route.params.ip
        if (this.$route.params.node) this.target.node = this.$route.params.node
        if (this.$route.params.namespace) this.target.namespace = this.$route.params.namespace
        if (this.$route.params.pod) this.target.pod = this.$route.params.pod
        if (this.$route.params.container) this.target.container = this.$route.params.container

        // const searchParams = new URLSearchParams(location.hash.indexOf('?') > -1 ? location.hash.substring(location.hash.indexOf('?')) : location.search)
        // for (let item of searchParams) {
        //   this.target[item[0]] = item[1]
        // }
        // console.log(this.target)

        this.title = `${this.target.ip} / ${this.target.node ? this.target.node : this.target.pod}`

        // skynet/api/k8s/console/{ip}?namespace=namespace&pod=pod&container=container
        // skynet/api/k8s/console/{ip}?node=turing.cloud.053-no.03.novalocal
        let shellUri = `ws://${window.location.host}${window.location.pathname}skynet/api/k8s/console/${this.target.ip}?${new URLSearchParams(
          this.target
        ).toString()}`
        this.socket = new WebSocket(shellUri)
        this.socket.onopen = this.wsOpen
        this.socket.onerror = this.wsError
        this.socket.onmessage = this.wsMessage
        this.socket.onclose = this.wsClose
      }
    },
    wsOpen() {
      // console.log(`websocket 连接成功`)
      document.title = this.title + ' - Skynet'
      this.loginSuccess = true
      this.socket.send(
        JSON.stringify({
          operate: 'connect',
          command: '',
          row: this.row,
          col: this.col
        })
      )
      this.$nextTick(() => {
        this.initTerm()
      })
    },
    wsError() {
      // console.log(`websocket wsError`)
      this.socket.close()
      this.socket = null
      this.loginSuccess = false
    },
    wsMessage(msg) {
      // console.log('ws传输的数据：', msg)
      this.term.write(msg.data)
    },
    wsClose() {
      // console.log(`websocket wsClose`)
      this.socket = null
      this.loginSuccess = false
    },
    initTerm() {
      const term = new Terminal({
        rendererType: 'canvas',
        // 渲染类型
        pid: 1,
        name: 'terminal',
        rows: this.row,
        // 行数
        cols: this.col,
        // 列数
        convertEol: true,
        // 启用时，光标将设置为下一行的开头
        scrollback: 500,
        // 终端中的回滚量
        disableStdin: false,
        // 是否应禁用输入。
        cursorStyle: 'underline',
        // 光标样式
        cursorBlink: true,
        // 光标闪烁
        tabStopWidth: 8,
        // 制表宽度
        screenKeys: true,
        // Xterm下的快捷键
        theme: {
          foreground: '#FFFFFF',
          // 前景色
          background: '#000000',
          // 背景色
          cursor: 'Orange',
          // 设置光标
          lineHeight: 16
        }
      })
      this.fitAddon = new FitAddon()
      term.loadAddon(this.fitAddon)
      term.open(document.getElementById('xterm'))
      term.onData(data => {
        if (this.socket) {
          this.socket.send(
            JSON.stringify({
              operate: 'command',
              command: data,
              row: this.row,
              col: this.col
            })
          )
        }
        this.commandCount = this.commandCount + 1
        // 每输入30个字符 刷新token，避免会话过期
        if (this.commandCount > 30) {
          refreshToken()
          this.commandCount = 0
        }
      })
      this.fitAddon.fit()
      term.focus()
      this.term = term
    }
  },
  beforeDestroy() {
    if (this.socket) {
      this.socket.close()
    }
    if (this.term) {
      this.term.dispose()
    }
  }
}
</script>
<style lang="scss">
.terminal-header {
  position: fixed;
  top: 0;
  width: 100%;
  height: 36px;
  background-color: #3a3333;
  display: block;
}

.terminal-ip {
  line-height: 36px;
  text-align: center;
  padding-left: 5px;
  color: #fff;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.terminal-contanier {
  width: 100%;
  height: 100%;
  background-color: #000;
  padding-top: 36px;
}

#terminalContanierBox {
  width: 100%;
  height: 100%;
  transition: 0.5s;
}

.xterm {
  height: 100%;
}

.iconfont {
  font-size: 20px;
}

.icon-success-fill {
  color: green;
  font-size: 16px;
}

.icon-reeor-fill {
  color: #d81e06;
  font-size: 16px;
}
</style>
