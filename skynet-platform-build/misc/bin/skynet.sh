#!/bin/bash

# Check if APP, IP, and PORT are set, otherwise exit
if [[ -z "$APP" || -z "$IP" || -z "$PORT" ]]; then
    echo "[ERROR] APP, IP, and PORT must be set before running this script." >&2
    exit 1
fi
export MAIN_CLASS=skynet.platform.$APP.Bootstrap
# Detect OS type for compatibility
OS_TYPE="unknown"
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS_TYPE=$ID
fi


echo =====================================================================
echo "[INFO] APP=$APP, IP=$IP, PORT=$PORT, MAIN_CLASS=$MAIN_CLASS"
echo "[INFO] Detected OS: $OS_TYPE"

ISOLATION=${ISOLATION:=false}

JAVA_OPTS="-Xms512M -Xmx1G"
DEBUG_PORT=$((PORT+30000))

TimeOffset=$(date +%z)

# Detect Skynet Home directory
SKYNET_HOME=$(cd $(dirname $0); cd ..; pwd)
# Application configuration paths
APP_PROP=$SKYNET_HOME/conf/application.properties
SKYNET_ZK_JAAS=$SKYNET_HOME/conf/skynet.zk.jaas.cfg
SKYNET_SERVICE=$SKYNET_HOME/conf/skynet.service
SKYNET_PROP=$SKYNET_HOME/conf/skynet.properties
LIB_DIR=$SKYNET_HOME/lib

# PID file path based on isolation mode
if [[ "$ISOLATION" == "true" ]];then
    PID_FILE=$SKYNET_HOME/bin/$IP/$APP.pid
    mkdir -p "$(dirname $PID_FILE)"
else
    PID_FILE=$SKYNET_HOME/bin/$APP.pid
fi

opr=$1
if [ -n "$2" ]; then
    IP=$2
fi
if [ -n "$3" ]; then
    PORT=$3
fi

# Get cluster name from properties, fallback to 'skynet' if not set
CLUSTER_NAME=$(grep 'skynet.zookeeper.cluster_name' $SKYNET_PROP | awk -F"=" '{print $2}' | awk '{gsub(/^\s+|\s+$/, "");print}')
if [[ -z $CLUSTER_NAME ]];then
    CLUSTER_NAME=skynet
fi

export SYS_SVC_NAME=$CLUSTER_NAME'_'$APP
export JAVA_HOME=$SKYNET_HOME/runtime/jdk-21.0.2
# JDK 21 no longer contains dt.jar and tools.jar, these tools are now integrated into JDK
export CLASSPATH=.:$JAVA_HOME/lib/*:$SKYNET_HOME/lib/*
export PATH=$JAVA_HOME/bin:$PATH



# Function to check and link python if needed (Ubuntu/CentOS compatible)
check_python() {
    if command -v python &> /dev/null; then
        echo "[INFO] Python detected: $(python --version 2>&1)"
    elif command -v python3 &> /dev/null; then
        echo "[INFO] Python3 detected: $(python3 --version 2>&1)"
        if ! command -v python &> /dev/null; then
            if [ "$OS_TYPE" = "ubuntu" ] || [ "$OS_TYPE" = "debian" ]; then
                sudo ln -sf /usr/bin/python3 /usr/bin/python
            elif [ "$OS_TYPE" = "centos" ] || [ "$OS_TYPE" = "rhel" ]; then
                sudo ln -sf /usr/bin/python3 /usr/bin/python
            else
                ln -sf /usr/bin/python3 /usr/bin/python
            fi
            echo "[INFO] Created symlink: python -> python3"
        fi
    else
        echo "[WARNING] Python is not installed. Please install Python 3.x."
        return 1
    fi
}

check_python

pid=0
fill_pid(){
    # Find process using main class name instead of JAR file
    pid=$(jcmd|grep $MAIN_CLASS|awk '{print $1}')

    echo $pid > $PID_FILE
    if [[ ! -e $PID_FILE ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    pid=$(cat $PID_FILE)
    if [[ -z $pid ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    n=$(ps -ef |grep $pid|awk '{print $2}'|grep $pid|wc -l)
    if [[ n -eq 0 ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    return
}

system_start(){
    if [ -f $SKYNET_SERVICE ]; then
        echo "[INFO] deploy $SYS_SVC_NAME.service ..."
        sed -i "s|^WorkingDirectory=.*$|WorkingDirectory=$SKYNET_HOME/bin|g" $SKYNET_SERVICE
        sed -i "s|^Description=.*$|Description=$SYS_SVC_NAME|g" $SKYNET_SERVICE
        sed -i "s|^ExecStart=.*$|ExecStart=$JAVA_HOME/bin/java $CMD_LINE|g" $SKYNET_SERVICE
        sed -i "s|^Environment=JAVA_HOME=.*$|Environment=JAVA_HOME=$JAVA_HOME|g" $SKYNET_SERVICE
        sed -i "s|^Environment=CLASSPATH=.*$|Environment=CLASSPATH=$CLASSPATH|g" $SKYNET_SERVICE
        sed -i "s|^Environment=PATH=.*$|Environment=PATH=$PATH|g" $SKYNET_SERVICE
        # Exception handling
            \cp $SKYNET_SERVICE /usr/lib/systemd/system/$SYS_SVC_NAME.service >> $SKYNET_HOME/log/deploy.log 2>&1
        echo "[INFO] deploy $SYS_SVC_NAME.service done."
        if [ $? -eq 0 ]; then
            systemctl daemon-reload
            # Enable automatic startup
            systemctl enable $SYS_SVC_NAME
            # Start service
            systemctl start $SYS_SVC_NAME
        else
            return 1
        fi
    else
	    return 1
    fi
}

system_stop(){
	KillMode=process
	if [[ $1 -eq 15 ]];then
		KillMode=control-group
	fi
	echo "$SYS_SVC_NAME is stopping..."
	if [ -f /usr/lib/systemd/system/$SYS_SVC_NAME.service ]; then
		sed -i "s|^KillMode=.*$|KillMode=$KillMode|g" /usr/lib/systemd/system/$SYS_SVC_NAME.service >> $SKYNET_HOME/log/deploy.log 2>&1
    systemctl daemon-reload
    systemctl stop $SYS_SVC_NAME
    # remove
    rm -f /usr/lib/systemd/system/$SYS_SVC_NAME.service
    systemctl daemon-reload
	else
		return 1
	fi
}

start(){
    chmod -R 770 $JAVA_HOME/bin
    chmod +x $JAVA_HOME/lib/jspawnhelper
    chmod 770 $SKYNET_HOME/runtime/k8s/*

    # Modify configuration file if ZK_HOSTS environment variable exists (passed from outside container
    if [[ -n $ZK_HOSTS ]];then
       echo 'modify skynet.properties by sed ...'
       sed -i "s/skynet\.zookeeper\.server_list=.*/skynet\.zookeeper\.server_list=$ZK_HOSTS/g" $SKYNET_HOME/conf/skynet.properties
    fi

    fill_pid
    if [[ $pid -ne 0 ]];then
       echo "skynet-platform-$APP is running now (pid=$pid) , please stop or kill it first!" 
       exit 0
    fi

    echo ============================== Env ==================================
    echo SKYNET_HOME    =   $SKYNET_HOME
    echo JAVA_HOME      =   $JAVA_HOME
    echo PATH      =   $PATH
    java -version
    echo =====================================================================

    if [ $opr = 'debug' ]; then
        JAVA_OPTS="$JAVA_OPTS -Ddebug=true -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=$DEBUG_PORT"
    fi

    # Build classpath: JDK21 no longer supports -Djava.ext.dirs, use -classpath instead
    # Include JDK libraries and application libraries
    CLASSPATH_DIRS="$JAVA_HOME/lib/*:$SKYNET_HOME/lib/*"
    SKYNET_PROP_OPTS=""

    if [ -f "$SKYNET_PROP" ]; then
        #dos2unix $SKYNET_PROP
        while read line
            do
               line=$(echo $line | tr -d '\r')
               if [[ $line && ! "$line" =~ "#" ]]; then
                   SKYNET_PROP_OPTS="$SKYNET_PROP_OPTS"" -D$line"
               fi
        done < $SKYNET_PROP
    fi
    SKYNET_PROP_OPTS=${SKYNET_PROP_OPTS//\$\{SKYNET_HOME\}/$SKYNET_HOME}

    #echo ----------------
    #echo 'arg:' $opr
    #echo $SKYNET_PROP_OPTS
    #echo ----------------
    
    echo "try starting skynet-platform-$APP : "

    # Using main class startup instead of -jar to resolve JDK 21 compatibility issues
    # Build complete command line with classpath, system properties and Spring Boot arguments
    CMD_LINE="$JAVA_OPTS $SKYNET_PROP_OPTS -classpath $CLASSPATH_DIRS -Dskynet.home=$SKYNET_HOME -Dskynet.ipAddress=$IP -Dskynet.isolation=$ISOLATION $MAIN_CLASS --spring.config.additional-location=$APP_PROP --server.port=$PORT"

    echo ----- Command -------------------------------------------------------
    for word in $CMD_LINE
    do
	    echo $word
    done
    echo =====================================================================

    if [ $opr = 'daemon' ]; then
	    system_start && echo "systemctl start $SYS_SVC_NAME success!!!"
        if [ $? -ne 0 ]; then
            echo "systemctl start $SYS_SVC_NAME failed, try start $SYS_SVC_NAME without systemctl."
            (nohup java $CMD_LINE >>/dev/null 2>&1 &) && echo "no systemctl start $SYS_SVC_NAME success!"
        fi
    else
        java $CMD_LINE
        echo $! > $PID_FILE
    fi

    fill_pid

    sysctl -w vm.max_map_count=262144
    ulimit -c 0

    if [[ $pid -ne 0 ]];then
        echo "skynet-platform-$APP is Startup Succeeded (pid=$pid)." 
    else
        echo "skynet-platform-$APP Start Failure, please try use [ant-$APP.sh start] Cause of Locating Failure."
    fi
}

# Help
help(){
    echo    "------------------------------------------------------------------"
    echo    "Usage: ant-$APP.sh  help"
    echo    "       ant-$APP.sh (start|debug|daemon) [ip] [port]"
    echo    "       ant-$APP.sh (status|stop|kill)"
    echo    "help       - this screen"
    echo    "start      - Start the program in the foreground"
    echo    "debug      - Start the program in the foreground, and add JAVA remote debug options in command line"
    echo    "daemon     - Start the program in the background"
    echo    "stop       - stop  the service and sub process."
    echo    "kill       - kill (-9) $APP process"
    echo    "status     - show running status"
}


do_kill(){
	if [ -z $1 ];then
		sig=15
	else
		sig=$1
	fi
	fill_pid

	if [[ $pid -eq 0 ]];then
		echo "$APP is not running."
		exit 0
	fi

    # Fallback to command line if systemctl service fails
    #kill -$sig $pid
	system_stop $1 && echo "systemctl stop $SYS_SVC_NAME success!"
	if [ $? -ne 0 ]; then
        kill -$sig $pid
        echo 'finish!'
	fi
}

case $opr in
start|daemon|debug)
    start
    ;;
stop)
    do_kill 15
    exit 0
    ;;
kill)
    do_kill 9
    exit 0
    ;;
status)
    fill_pid
    if [[ $pid -ne 0 ]];then
       echo "running (pid=$pid)"
    else
       echo 'stopped'
    fi
    ;;
*)
    help
    ;;
esac