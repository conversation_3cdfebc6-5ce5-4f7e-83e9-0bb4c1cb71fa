# 发布记录

---

# 3.4.15_Build${} [2025年6月]

## fea
- `skynet-boot-starter-parent` 升级到 `4.3.3`
- jdk 升级到 jdk21 （jdk8u412-b08 => openjdk-21.0.2）
- 支持边车模式 
- 移除skynet-manager 和 agent 对 ant插件的属性配置（相关配置移动到了 `conf/application.properties`）
- 用户 admin 默认密码重新修改。
- 服务定义：增加归档功能

---

# 3.4.14_Build2181 [2024年12月]

## fea
- `skynet-boot-starter-parent` 升级到 `4.0.17`
- 服务节点增加属性配置，目标为了运行时节点环境变量配置；
- 优化 agent 性能，缓存 k8s 服务状态；
- 新增 k8s支持pod日志功能；
- [FAQ]Skynet 如何配置日志保存时间？
- 服务管理：支持节点刷新优化
- 环境变量 增加 `SKYNET_ACTION_INDEX`
- 所有托管服务使用统一的 ServiceAccountName
- 支持k8s部署，命名空间可以通过配置文件修改，新增配置 `skynet.k8s.namespace=default`，调整配置文件
- 移除集群级属性的配置对 `Skynet托管平台`本身的影响（特别是`skynet.security`相关的属性）
- 新增 `K8S_NODE_IP` 环境变量，支持在服务定义中使用该变量替换成集群内部主机 IP
- 增加agent: `/skynet/agent/prometheus/endpoint`
- 服务服务端口：在多实例的情况下自动递增，主要是希望固定端口。（解决场景问题：在服务汇报到TLB中事避免标签丢失）
- 服务管理，筛选条件增加前端缓存功能，避免每次刷新筛选条件丢失
- K8s 服务分配支持 副本数`replicas`，同时UI支持副本数提示。
- 支持 `skynet.k8s.registry.context-path=hy-docker-private/skynet` 系统镜像推送自定义上传路径
- 执行 `start.s`h 脚本的超时时间支持可配，`skynet.shell.timeout=5000`

## fix
- 修复 CORS信任任意来源漏洞。
- 修复metrics-server 镜像拉取失败异常
- 修复agent构建打包问题
- 修复跨机器创建k8s Secret异常问题，修复agent构建打包问题
- 调整配置文件 manifest 说明以及推送镜像代码优化
- 节点管理排序，按照Index、AgentType、IP 排序；
- [FAQ] 解决页面显示乱码

---
 
# 3.4.13_Build2074 [2024年07月]

## fea
- 支持国际化
- `skynet-boot-starter-parent` 升级到 `4.0.15`
- 依赖包版本升级：
  - docket-client-java.version 16.0.2 > 16.0.3
  - docker-java.version 3.2.13  > 3.2.14
  - oshi.version 6.3.1 > 6.3.2
  - jsch.version 0.2.6 > 0.2.17
- jdk1.8.0_261 => jdk8u412-b08
  

## fix
- 修复 登录token的安全漏洞。  

---
 

# 3.4.12_Build2054 [2024年05月]

## fea
- 移除了mesh
- k8s agent 支持使用集群内部 IP
- 优化在挂载相同存储的服务器集群场景下，状态文件覆盖，可增加工作目录通过IP地址隔离。
  - 可通过修改 `skynet\bin\skynet.sh` 配置文件中的，`ISOLATION=${ISOLATION:=false}` 将 false 改为 true；
- 支持 `gpus` 选项的服务部署到 K8s 集群
- `skynet-boot-starter-parent` 升级到 `4.0.14`
- docker 升级到 docker-20.10.24
  

## fix
- 修复 在AsyncAppender 中 ConsoleLoggerFilter 不生效的bug
- 修复部署多实例时挂载路径不对的问题
- 修复启动脚本中由于使用 chmod -R +x 命令在包含大量文件的文件夹中导致的耗时较长，从而引发的获取到错误的服务进程PID的问题。
- DnsEnvironmentBuilder 修复 Ports 为null 空指针异常，兼容老版本的ZK状态。
- 修复在服务视图中分配服务时导致所有禁用服务重启的问题
- 

---

# 3.4.11_Build2035 [2023年10月]

## fea
- 支持扩展端口配置为 0 时随机生成；
- 汇报服务状态时新增扩展端口信息；
- k8s agent 支持汇报服务状态；
- 服务定义校验优化，关联文件和模板文件不能为空，自动切换到校验不通过的标签页；
- 支持在服务管理页面快捷复制服务的DNS地址；
- 卷挂载支持 Mount Propagation 绑定传播
> 当 DockerBoot 配置 `-v xxx:yyy:rshared` 或 `-v xxx:yyy:rslave` 时，映射成 Kubernetes 的 `mountPropagation: Bidirectional` 或 `mountPropagation: HostToContainer`
- 支持伪域名解析，便于快速交付：
> - 可以通过域名的方式配置 Skynet 上托管的服务地址，这样可以不用在服务定义中写死依赖服务的 IP 地址，比如 `${dns.mysql.paas.skynet.cluster.local}` 表示 `mysql@paas` 服务的 `IP:PORT`，多个实例时以逗号分割，注意，依赖的服务需要先启动。
> - 备注：${dns.`mysql.paas`.skynet.cluster.local}  占位符中的 `mysql.paas` 是根据具体的服务坐标确定。
> - 除此之外，还支持以下几种格式：
>   - `${dns.ext1.tlb.paas.skynet.cluster.local}` 表示 `tlb@paas` 服务的 `IP:PORT1`
>   - `${dns.ip.tlb.paas.skynet.cluster.local}` 表示 `tlb@paas` 服务的 `IP`
>   - `${dns.port.tlb.paas.skynet.cluster.local}` 表示 `tlb@paas` 服务的 `PORT`
>   - `${dns.port1.tlb.paas.skynet.cluster.local}` 表示 `tlb@paas` 服务的 `PORT1`
- 支持配置块管理；
> - 目的：便于将有业务含义的或共用的配置集中到一个配置块中，然后需要依赖的服务直接引用，解决以前 共用配置在集群级别和服务级配置中 被冗余应用到不需要的服务中；
> - 使用：先在 `配置管理` 中配置复用的配置块，然后在具体的服务定义中配置（编辑模式) `属性配置` 中 `配置块设置` 选择共用的配置，(注意配置的优先级)。

## fix
- 修复 `ssh` 请求超时（如 目标服务器不存在），而丢失 `Agent`
- 修复 `nodeSelector` 为空导致服务反复更新的问题；
- 修复新增模板文件时展开和收起按钮点击无效的问题；
- 修复关联文件中不存在的文件未高亮显示的问题；
- 修复更新 k8s 节点导致所有服务重启的问题；
- 修复查看服务日志时 <> 符号不显示的问题；
- docker 安装脚本，在有的操作系统上（国产化系统概率高） 出现 `systemctl status docker` 分页输出交互，导致卡顿停止，不能继续安装`docker`运行时;
  - systemctl status docker => systemctl `--no-pager` status docker  (增加了 `--no-pager` 选项)
- 启动脚本增加 `ant-xagent.sh` 增加 `python` 检测（如果只部署了`python3`，自动创建软链接）


# 3.4.10_Build1991 [2023年9月]

## fea
- `skynet-boot` 升级到 4.0.13-SNAPSHOT
- 优化  skynet-auth 属性配置
  - `skynet.boot.auth.app.apiKey` => `skynet.auth.api-key` 
  - `skynet.boot.auth.app.apiSecret` => `skynet.auth.api-secret`
- 新增服务定义备份管理
- Logback默认属性配置优化，支持 `%trace` 和 `%em`（缩短大对象log日志）
- skynet-agent （k8s环境）拉取策略改为 `Always`
- 支持快捷复制资源文件目录

## fix
- 修复系统属性和系统日志中注释行不显示的问题
- 修复启动详情查看日志对话框未关闭的问题
- 修复在非私网环境下推送镜像报错的问题

---

# 3.4.9_Build1922 [2023年7月]

## fea
- `skynet-boot` 升级到 2.0.10
- 增加 `显示[Actuator]`功能项
- 新增 `V3Deployment.getDeploymentByActionPoint` 接口，支持查询某台机器上指定服务的状态
- 优化服务管理接口性能 
- 优化 logging.pattern 配置增加 SKYNET_CURRENT_TRACE_ID 输出

## fix
- 修复  SpringBoot Actuator有时为空的bug（原因：`/actuator/mappings`中存在多个`dispatcherServlet`节，只解析了第一个）
- 修复 discovery.target.namespaces 配置不生效的问题

---

# 3.4.8_Build1910 [2023年6月]

## fea
- 新增 K8sBoot 服务类型
  - 支持以 YAML 形式部署 Deployment、DaemonSet 和 StatefulSet
  - 支持创建 RBAC 权限相关的资源
  - 支持创建 ConfigMap 相关的资源
  - 支持多个初始化容器
- 优化 DockerBoot 服务类型
  - 支持特权模式（`--privileged=true`）
  - 支持主机网络模式（`--network=host`）
- 支持为关联文件和模板文件设置文件权限
- agent 启动服务，支持分组和延迟设置（skynet.agent.load.delay.size=4|skynet.agent.load.delay.seconds=8）

## fix
- 修复磁盘使用率统计不正确的问题
- 修复启动失败后启动详情中无任何提示
- 修复守护进程集和有状态副本集元数据页面的标签和注解未显示的问题
- 修复日志显示不全的问题
- 优化分配服务标签的样式错乱
- 修复扩展配置文件和关联的 zip 文件冲突的问题
- 修复第一次注册节点时，zkPath 不存在导致 agent 启动报错
- 修复 SkynetApp 创建失败 agent 崩溃退出的问题
- 修复偶现节点无法删除的问题

---

# 3.4.7_Build1869 [2023年2月]

## fea
- 支持显示守护进程集和有状态副本集详情
- 优化工作负载的更新策略页面，添加提示信息
- 优化主机详情的容器组列表，显示删除中状态
- skynet-boot 升级到 4.0.7（springboot 2.7.8；springcloud 2021.0.5）

## fix
- 修复首次更新节点时报 404 错误
- 修复工作负载下偶现容器组列表显示错乱的问题

---

# 3.4.6_Build1867 [2023年1月]

## fea
- 增加K8S操作相关的审计日志
- 分配的服务按照应用系统（插件）总体排序

## fix
- 修复注册本机节点时Docker不安装的问题
- 修复带引号的属性导致服务定义 JSON 格式异常的问题

---


# 3.4.5_Build1865 [2023年1月]
## fea
- 节点管理支持添加 Kubernetes 类型的节点
- 支持常用 Kubernetes 对象的管理功能
  - 支持显示集群概览，包括 CPU 和内存信息（内置 metrics server）
  - 支持主机管理（封锁调度/恢复调度、排空节点、终端访问），支持修改主机标签
  - 支持部署管理（伸缩、重启、日志、终端、自动伸缩、调整镜像版本、历史版本和回滚、更新策略）
  - 支持容器组、守护进程集、有状态副本集、定时任务、任务的管理
  - 支持配置字典、密文、HPA 的管理
  - 支持服务、端点、路由的管理
  - 支持命名空间和自定义资源的管理
  - 支持通用 YAML 执行功能
- 新增 Skynet Operator、Agent、Init 基础镜像，支持将 Skynet 服务部署在 Kubernetes 集群
- 服务分配时支持按节点标签进行分配
- 服务分配 UI 优化，显示服务分组及禁用状态

## fix
- 优化 IP 地址获取逻辑，排除 docker0 和虚拟网卡
- 修复 xagent daemon 启动未注入环境变量的问题
- 修复偶现服务无法启用也无法停止的问题
- 增加 DockerClient 为空时可能原因的友好提示（未安装 docker 运行时或 docker.service 未设置启动参数）

---

# 3.4.4_Build1844 [2022年11月]

## fea
- skynet-boot 升级到 4.0.5（springboot 2.7.5；springcloud 22021.0.5）
- arthas升级到 3.6.6

## fix
- xmanager: 支持传入未加密的admin密码（方便iDeploy自动化脚本部署登录获取token）
- xmanager: 修复 xagent daemon 启动未注入环境变量的问题
- xmanager：主机登录优先使用password认证
- xmanager：修订 节点管理 agent 乱序问题
- xmanager：修复偶现服务无法启用也无法停止的问题
- 漏洞修复：缓慢的 HTTP 的拒绝服务攻击（增加配置 server.tomcat.connection-timeout=1000，server.tomcat.max-connections=10000）

---

# 3.4.3_Build1804 [2022年10月]
## fea
- 升级oshi（6.2.2）和 jsch（0.2.4）版本号
- http 代理 url 加密处理【安全-未分类漏洞)服务器请求伪造ssrf】
- 启动脚本优化，支持进程守护（通过systemctl service 实现）
- skynet-boot 升级到 4.0.4（springboot 2.7.3；springcloud 22021.0.4）

## fix
- xmanager: 跳转地址优化
- xmanager：修复日志文件按照服务ID分目录，历史文件滚动压缩的bug 
- xmanager：修复：会话过期 无法跳转的问题

---

# 3.4.2_Build1779 [2022年09月]
## fea
- manager 服务器节点支持WebShell 远程登录.[配置开关：skynet.webshell.enabled=true;skynet.webshell.sftp-enabled=true]
- manager 登录安全模块优化：同时支持滑块到最右端以确定。
- manager 日志查看：优化了日志查看页面体验效果
- agent 启动服务异常时，启动详情 中 异常原因更加明确。

## fix
- 修复 服务定义属性为空时，保持异常的bug

---

# 3.4.1-RELEASE-1664 [2022年07月]
## fea
- manager 服务发现多路径汇报(zkPath), 目的，便于托管的子系统或其他服务容易通过 在同一命名空间下服务发现。（对应属性 skynet.manager.discovery.target.namespaces）
- manager 登录安全模块优化； 登录密码签名公钥，通过后台获取等；
- manager 重启服务等操作 风险确认提示控件优化，由滑动改为点击。

---

# 3.4.0-RELEASE-1664 [2022年06月13日]
## fea:
- 0、Springboot 升级到了 2.6.6;
- 1、agent增加 托管的java服务可以统一追加设置jvm参数 功能设置，（支持ZK鉴权认证设置）;
  说明：在启动java服务的启动命令行中增加-D参数:
  应用场景：通过-D参数 设置 java.security.auth.login.config 安全认证，做到托管服务的无代码侵入（zk认证）;
- 2、用户口令采用 Pbkdf2 加密存储;
- 3、服务器口令采用RSA加密存储;
- 4、springboot 升级到了 2.6.6，修复了若干类库的安全性;
- 5、webapi 增加了 服务定义定义接口 增加 功能标签过滤属性;
- 6、升级openssh 连接库（jsch-0.2.1.jar） 支持到 openssh 8.8;
- 7、fastjson 升级到 fastjson2. 速度提升明显，约30%;
- 8、托管服务控制台日志按照服务坐标分目录存储。（解决日志目录下如果有海量日志文件，导致logback FullGC让CPU飙升不下降的风险）;
- 9、移除sigar，采用 oshi 代替（agent 跨平台稳定性增强，奔溃概率大幅下降，还有服务资源指标采集性能提升，agent负载下降30%）;

## fix:

- 1、登录口令 秘钥错误修复;
- 2、修复xmanager通过 skynet_token免登录的bug(前端进行了拦截);
- 3、修复已经分配的服务定义 通过 非正常方式被删除，导致前台页面所有服务无法显示的问题。


---

# 3.2.0-RELEASE-1447 [2022年03月15日]

- 1、fea：为了安全性：springboot 版本升级到 2.6.4。
- 2、fea：移除 zuul
- 3、fea：docker 升级到 docker-20.10.9。
- 4、fea：agent 和manager 增加 server.address 属性配置，绑定IP
- 5、fea：LoggerServer 中的 LogstashTcpSocketAppender，Loki4jAppender 优化。
- 6、fea：支持属性配置中 设置了第三方SpringCloudConfigServer，属性再次增补。
- 7、fea：log4j 依赖排除。


---

# 3.0.2-RELEASE-1432 [2021年11月04日]

- 1、fea：增加 prometheus 磁盘统计 排除 docker目录。缺省(skynet.prometheus.ignore.docker.dir.enabled=true)
- 2、fix：排除 agent JMXAttacher 功能，存在潜在jvm不停打印堆栈信息和不能连接的bug；
- 3、fea：agent metric 优化
- 4、fix：xmanager 修复服务监控grafana图表Tab跳转URL错误

---

# 3.0.1-RELEASE-1421 [2021年09月23日]

- 1、fea：增加 grafana 磁盘统计 排除 docker目录。缺省(skynet.grafana.ignore.docker.dir.enabled=true)
- 2、fix：修复agent重启，导致托管的服务随机重启的bug；
- 3、fix：修复upgrade.sh 删除正在运行服务依赖的二进制文件问题。 
- 4、fix：修复集群节点 agent更新时移动了skynet/runtime目录.(影响正在运行sdk的服务)
- 5、fix: 修复 Skynet包分发部署 unzip时 提示挂起的bug（改为不覆盖）
- 6、fix: 修复skynet-xmanager-ui 节点管理表格滚动条不显示问题
- 7、other：修改 spring.security.user.password 加密配置 排除 {cipher} 
- 8、other：xmanger v2版本html 移动到 test/resource目录下


---

# 3.0.1-RELEASE [2021年08月22日]

- 功能优化
	- agent:
		- fea: 增加服务注册 增加命名空间的支持；
	- xmanager： 
		- fea: 服务管理，优化UI中服务分组的展开收起；
		- fea: 服务定义-服务列表，将“启用NG网关”列改为“日志采集”；
		- fea: 服务定义-功能特性，启用监控采集 开放给所有类型的服务；
		- fea：Web-API支持,通过服务坐标重启服务(可以不指定IP)；
- 缺陷修复：
	- agent:
	  	- fix: 修复agent重启服务时遗留文件删除失败的问题；
	- xmanager：
		- fix: 服务管理，修复UI 服务分组状态下无法全选的问题；
		- fix: 服务管理，修复卡片无法折叠展开的bug；
		- fix: 服务管理，修复bug "服务视图过滤后，再点击服务分配，保存，把不显示的服务全部解除部署"。


---

# 3.0.1-RELEASE [2021年04月21日]

- 功能优化
	- xmanger：安全性增强，防用户登录暴力破解。
	  	- skynet.api.auth.failLockDurationSecond = 180
	  	- skynet.api.auth.failTriesTimes = 5
	- xmanager：服务管视图，默认分组
	- xmanager：服务定义，增加模板文件（支持占位符替换），适用自定义配置文件
	- xmangaer,agent: 支持LOKI数据采集
		- skynet.logging.loki.enabled=true
		- skynet.logging.loki.host=127.0.0.1:3100
- 缺陷修复：
	- 修复内网场景，服务状态汇报IP地址不一致的问题。


---

# 3.0.0-RELEASE [2020年12月30日]

- 特性：
    - 全新交互：图聆UI风格，人性化交互，从不同角度场景定位操作、高亮提示等提高运维效率。
    - 分类聚焦；将服务器、服务定义、服务管理分层分类，并根据各模块的特点加强了优化完善。    
    - 安全可靠：对访问接口进行skynet.security.api-auth签名、jwt token认证、base-auth分级鉴权验证。
    - 开放共享：提供丰富的WebAPI接口以及java-feign-sdk，方便第三方集成服务托管治理操作。

- 功能优化
    - xagent：为了安全禁用 route功能接口，（skynet.endpoint.route.enabled=false 缺省禁用）
    - xagent：服务状态、日志等内容通过代理接口访问
    - xagent: 本集群Manager地址：${SKYNET_MANAGER_URI} 和 本机Agent地址 ${SKYNET_AGENT_URI}环境常量
    - xagent: 国产化服务器上 numa绑定功能 缺省改为 关闭，（skynet.agent.numa.bind.enabled=false）
    - xagent: 支持HTTPS协议健康状态检测；服务定义增加模板文件扩展
    - xmanger: 增加系统诊断模块，便于快速定位日常常见的问题，如快速 查看 hostname配置，内存占用等信息
    - xmanger：所有新增、更改、删除数据 增加了审计日志（<EMAIL>），可以关闭（skynet.audit.enabled=false）
    - xmanger: api/v2 接口增加条件注解，默认开启，为了后续只开启v3接口
    - xmanger: 登录授权jwt优化，采用 spring-boot-starter-security组件 
    - xmanger: 增加Zuul网关，可以直接通过xmanager访问托管的服务，如：http://*************:2230/z/grafana-server-v6/
    - 日志统一采集：将agent（socket）=> Kakfa => Logstash+ES+Kibana 简化 为 agent（socket）=> Logstash+ES+Kibana，        
- 安全加强    
    - Skynetboot版本升级到3.0.3(Springboot版本升级到2.3.6) 
    - 升级JDK到 261
    - xagent、xmanager 服务对外接口采用分级安全验证机制：skynet.security.api-auth(service-api管理接口)，basic-auth(promethus,grafana)、jwt-token（xmanager管理、/skynet/api/）并存
    - 监控套件相关：grafana、Promethus采用 basic auth验证
    - 密码强调增强：字母大小写，特殊字符，数字，至少8位
    - xmanager 登录密码、修改密码、服务器密码等从浏览器客户端到服务端都采用了加密传输
	- 支持Zookeeper ACL安全防护设置，可在 skynet.properties 中配置 skynet.zookeeper.acl属性
- 缺陷修复
    - 服务定义：修订服务和资源文件删除
    - 服务托管：agent(-15)退出后不要删除托管服务状态，让agent启动后直接续上托管状态

---

# 2.1.1013-RELEASE [2020年09月18日]

- 功能优化
    - xagent：为了安全禁用 route功能接口，（skynet.endpoint.route.enabled=false 缺省禁用）
    - xagent：服务状态、日志等内容通过代理接口访问
    - xagent: 本集群Manager地址：${SKYNET_MANAGER_URI} 和 本机Agent地址 ${SKYNET_AGENT_URI}环境常量
    - xagent: 国产化服务器上 numa绑定功能 缺省改为 关闭，（skynet.agent.numa.bind.enabled=false）
    - xagent: 支持HTTPS协议健康状态检测
    - xmanger：所有新增、更改、删除数据 增加了审计日志（<EMAIL>），可以关闭（skynet.audit.enabled=false）
    - xmanger: api/v2 接口增加条件注解，默认开启，为了后续只开启v3接口
    - xmanger: 登录授权jwt优化，采用 spring-boot-starter-security组件 
    - xmanger: 增加Zuul网关，可以直接通过xmanager访问托管的服务，如：http://*************:2230/z/grafana-server-v6/
    - 日志统一采集：将agent（socket）=> Kakfa => Logstash+ES+Kibana 简化 为 agent（socket）=> Logstash+ES+Kibana，        
- 安全加强    
    - Skynetboot版本升级到3.0.3(Springboot版本升级到2.3.6) 
    - 升级JDK到 261
    - xagent、xmanager 服务对外接口采用分级安全验证机制：skynet.security.api-auth(service-api管理接口)，basic-auth(promethus,grafana)、jwt-token（xmanager管理、/skynet/api/）并存
    - 监控套件相关：grafana、Promethus采用 basic auth验证
    - 密码强调增强：字母大小写，特殊字符，数字，至少8位
    - xmanager 登录密码、修改密码、服务器密码等从浏览器客户端到服务端都采用了加密传输
- 缺陷修复
    - 服务定义：修订服务和资源文件删除
    - 服务托管：agent(-15)退出后不要删除托管服务状态，让agent启动后直接续上托管状态
    
# 2.1.1012-RELEASE [2020年09月07日]

- 功能优化
    - 日志采集，支持所有托管类型的Boot控制台标准输出日志的存储和统一采集到ELK。
    - 服务托管：DockerBoot 参数支持启动参数和运行参数（新增），同时支持同一个docker可以启动多个实例；镜像名称支持占位符替换。
    - 服务定义：移除Eureka服务发现配置，以后业务根据需求自行在属性中配置。
    - 服务启动：服务启动参数 针对SpringBoot fatJar 增加manifest元数据信息展示，方便版本查看。 
    - 服务发现：支持SpringCloudZookeeperDiscovery规范，自动将托管的服务注册汇报到zookeeper,方便SpringCloud服务发现规范访问Skynet托管的服务。缺省zkpath=/skynet/discovery
    - 服务管理：服务管理增加 /cluster 访问路径【支持AIlab项目】  
    - xmansger：服务IP地址如是回调地址，通过Spring.cloud.client.ipAddress 获取本机实际IP，汇报到服务发现中心。
    - xmansger：下载文件失败日志简化处理，排除了日志堆栈信息，减少日志体检。
    - xmansger: jquery 升级到 3.5.1；xmanager.sh|agent.sh 启动脚本配置优化。    
    - xmansger：增加IP地址是否是本机开关，缺省是开启。（应用场景：服务器虚拟映射新的IP）【skynet.check-ip.enabled=true】
    - xmansger：注册服务器是增加是否自动部署Skynet开关，默认开启，主要应用场景之一，服务器虚拟映射新的IP，系统判断是否不是自己本机，后续在skynet3.0中注册前台增加是否推送选型 【skynet.auto.deploy.enabled=true】
    - xmansger：优化Shell，增加智能判断，是本地时直接执行脚本不通过用户和密码
- 缺陷修复
    - 修订：日志输出页面标题，由Skynet标注输出改为Skynet标准输出

# 2.1.1011.RELEASE [2020年04月17日]

- 缺陷修复
    - 修复在某些国产化平台（银河麒麟OS + FT2000 CPU)上xagent崩溃问题。

# 2.1.1010-RELEASE [2020年03月02日]

- 功能优化
    - springboot服务设置了server.context-path时，能相应调整xmanager界面获取actuator mapping的链接URL path
- 缺陷修复
    - 修复通过命令行./ant-xagent.sh stop命令停止xagent及被托管服务时，被托管服务反复重启的bug
	- 修复因为jar包加载顺序不固定导致的在某些环境下无法打开xmanager界面的问题（HTTP 500: Error resolving 'v2/login',template might not exist or...)
- 安全漏洞修复
    - 修复Tomcat中存在的文件读取/包含漏洞，升级 Apache Tomcat 到 8.5.51。

# 2.1.1009.4.RELEASE [2019年12月26日]

- 功能优化
    - 服务托管，增加随机端口区间，缺省区间：[22300,32300]（可以通过 skynet.random.port.range.begin/skynet.random.port.range.end 属性设置）
	- 服务托管，优化日志配置选项，logging.path、logging.file 以外围配置优先，如果没有配置将采用默认。
	- 服务托管，优化托管启动脚本支持 Ubuntu操作系统 （通过skynet.shell.command属性配置，缺省：/bin/bash）
	- xmanager 自定义首页 https页面支持弹出新窗口
	- xmanager 服务管理页面，增加服务器拖拽排序。
	- xmanager 服务定义增加系统编码（pluginCode），方便应用系统的快速定位；应用系统、服务列表增加拖拽排序。
	- xmanager 服务定义移除 Flylog 配置项

# 2.1.1009.3.RELEASE [2019年11月18日]

- bug修复
    - 解决stream.py脚本100%CPU占用问题
	- 解决ant-xagent.sh/ant-xmanager.sh进程检测的缺陷（实际服务不存在但是检测出服务在运行的结果）
- NUMA平台自动识别自动绑定CPU内存到节点

# 2.1.1009.2.RELEASE [2019年10月14日]

- bug修复：
    - 修复stop.sh无法kill整个进程子树的问题
	- 修复sigar引发的UnstatisfiedLinkError，导致xagent启动失败的问题
	- 修复python3环境下调用报错导致无法获取标准输出的问题

# 2.1.1009.1.RELEASE [2019年09月24日]

- 高危漏洞修复:
    - Fastjson版本升级到1.2.61版本
- 流式计算：  
 	- 节点数据统计：统计计数器进行了容错优化，不会因为中间一次的采集数据丢失而导致前后的数据对不上。
- 服务托管：  
	- 服务托管模式优化，xagent与被托管进程进程关系解耦，xagent被kill不影响被托管进程，xagent重启后能继续获取目标服务的标准输出。 
	- xagent 内存占用优化，JVM标准设置512MB。
- bug 修复：  
	- xagent内存泄露：在GPU的服务器上开启性能采集场景下，频繁启动托管目标服务造成内存泄漏。
- 启动脚本优化:
    - 使用pid文件控制启停
- 集群监控后台优化：
    - 使用缓存提高页面访问速度
- 添加若干第三方开源组件监控：
    - 添加redis/mysql/mongodb/es/rabbitmq的监控
- 元数据获取优化：
    - 增加构建号和构建分支信息，提高问题追溯能力

# 2.1.1009.RELEASE [2019年07月30日]

- 1、流式计算：  
 	- 节点数据统计：增加每个节点数据流动统计和计算流程可视化展示。（详细见 http://wiki.iflytek.com/pages/viewpage.action?pageId=248974139）
	- 集成Zipkin进行流程数据链路跟踪监控；
	- 增加 @SkynetMqSvcContext @SkynetMqSvcHandler @SkynetProcessBuilder @SkynetProcessDispatchContext 注解，简化消息处理器和流程定义的注解的复杂性。
	- OnlineMQReport增加与zk掉线的补偿机制。
	
- 2、服务托管：  
	- 支持三级属性配置KEY作为占位符引用与替换；初始化工作目录占位符； 
	- JavaBoot支持skywalking 进行监控与服务,方便服务的链路跟踪和性能分析；
	- WebAPI 管理接口添加 在线服务状态查询；
	- 增加 带标签Metrics收集器 SkynetMetricsService，使用方式详细见(http://wiki.iflytek.com/pages/viewpage.action?pageId=244387897 SkynetMetricsService)；
	- 增加 SkynetMetrics gauge指标自动采集，主要针对ObjectPool<?>.  使用方式详细见(http://wiki.iflytek.com/pages/viewpage.action?pageId=244387897 SkynetMetricsService)
	- 支持 springboot 1.x 和 2.x 的托管（右键服务节点“启动参数”可以查看具体的SpringBoot版本）；
	- 添加本地配置文件占位符：${SKYNET_CONFIG_LOCATION_FILE} 如：/iflytek/server/skynet/tmp/<EMAIL>；
	- xmanager和xagent 服务停止时主动从服务注册中心下线（kill -15 pid)；
	- dashboard报表定义的自动导入。（自动导入 将放在 服务的工作目录和对应的仓库目录下的  dashboard*.json 到 Grafana）；
	- 增加缺省jdk1.8.0_261运行环境包。（意味目标部署环境可以不用安装JAVA运行环境）；
	- 移除服务管理7230端口；
	- 增加xManager查看入口；
	- 日志配置使用SpringBoot规范，如 logging.pattern.console,logging.pattern.file等；
	- 日志查看页面字体样式优化；
	- xMnager集群管理页面，状态获取优化排除跨域问题、服务节点右键菜单整理优化等。

- 3、系统监控与预警：  
	- 采集方案调整：将以前定时日志主动采集上报 改为 Prometheus 拉取模式，
	    - 优势：
			- 数据存储体积减小（一个指标2字节存储空间）
			- 同时充分利用业界成熟的Prometheus Exporter进行组件数据的采集。
		- 增加自定义采集指标端点。
	- 监控图表优化：增加cpu占用、内存占用的TopN排序显示，GPU、CPU温度等。
	- 图表组件版本升级：Grafana 6.2.4
	- 增加预警模块，支持 监控模板、计算指标的自定义，预警通道支持：微信、邮件、Webhook。

- 4、配置管理：  
	- 三级配置中支持 占位符属性名替换，如：mysql.ip=${xx.ip} 
	
- 5、bug 修复：  
	- serverSession内存泄露（会话没有设置超时）

- 6、其他改进：  
	- 支持国产化ARM架构服务器。（重点 sigar 在arm上的适配 ）

# 2.1.1008.RELEASE【2019年04月22日】

- 1、流式计算：  
	- Kafka消费者线程池添加threadName，以及消费者线程丢失（如jvm内存紧张时）后的自动补偿功能完善；  
	- 消息生产者增加了消息分组TopicKey的设置；（方便同一业务的消息发送到同一topic，保证整体有序）；  
- 2、服务托管：  
	- 对关联文件为空时，兼用性优化；  
	- Java服务（SpringBoot|SkynetBoot）增加 JavaOpts 缺省 jvm参数设置（-Xms128M -Xmx2G）；  
	- 增加服务注册中心在线服务状态异常下线的补偿注册机制；  
	- 优化服务治理的 WebAPI 接口。详细说明可参考[SkynetWiki 服务治理-管理API](http://wiki.iflytek.com/pages/viewpage.action?pageId=248038691);  
	- DockerBoot 启动优化：未安装情况下提示；
	- 服务器注册：并发注册与推送控制处理；
	- 对skynet基础包并发部署做了控制和优化，同时加快了前台进度推送。
- 3、系统监控：  
	- 优化监控图表，包括集群、主机、服务状态；  
	- 增加 Prometheus 数据采集配置集成，(访问地址： http://{ip:port}/prometheus)
- 4、基础服务：  
	- 日志docker部署包优化，如ES服务支持日志的TTL设置；  
- 5、配置管理：  
	- 三级配置支持 SpringCloudConfig规范，xAgent同是SpringCloudServer，详细说明可参考[SkynetWiki 4.配置服务](http://wiki.iflytek.com/pages/viewpage.action?pageId=238871758)  
- 6、服务定义：  
	- SpringBoot托管FatJar支持文件通配符；docker服务定义将目录映射参数合并到docker参数中  
- 7、服务发现：  
	- 支持技术中心API网关 的服务策略节点地址的自动注册与删除；  
- 8、管理后台：  
	- 对Xmanager管理界面进行了页面风格修改；  
	- 增加登录首页背景和系统标题自定义；
	- 优化xmanger集群管理页面的服务状态获取速度（特别是在集群中有服务器未启动的情况下）
- 9、流式计算
	- 配置兼用性优化（在多个MqParam Bean存在的情况下冲突）
- 10、代码优化与bug修复：  
	- 线程池 线程添加 线程名称  
	- 修复Log日志记录是使用了为格式化的日志串   
	- 修复grafana dashboard/datasource自动加载的相关bug  
	- 解决docker下的/dev/shm操作权限的问题，让路径其可配置(skynet.dev.shm.path系统属性)

# 2.1.1007.RELEASE [2019年01月24日]

- 1、流式计算框架，独立调度服务，解决 实际流程节点 与 流程定义逻辑 在运行时的隔离。
- 2、服务托管：支持docker化服务。
- 3、基础服务组件Docker化 ：ELK、Kafka、Grafana等减轻部署复杂度。

# 2.1.1006.RELEASE  [2018年12月19日]

- 1、集成Flylog、Grafana。
- 2、健康检测优化：PID、TCP、HTTP
- 3、上线成功标志修改：健康检测成功时
