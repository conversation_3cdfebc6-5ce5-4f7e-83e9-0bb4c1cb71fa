package skynet.xmanager;


import skynet.platform.common.utils.DockerLoginCheckerUtils;

public class DockerTest {

    public static void main(String[] args) {
//        String registryUrl = "artifacts.iflytek.com/hy-docker-private";
//        String username = "yjchu";
//        String password = "AKCp8k7uj1QjiCgA5LmKDDJcMZFC7yWRgYdovYjg2dEUCsE4DM9nGoiAjZGSGJyKuTYfiCcXU";

        String registryUrl = "172.30.209.220:30002";
        String username = "admin";
        String password = "Harbor12345";



        boolean loginSuccess = DockerLoginCheckerUtils.checkDockerLoginWithProtocolSupport(registryUrl, username, password);
        if (loginSuccess) {
            System.out.println("Docker login successful!");
        } else {
            System.out.println("Docker login failed!");
        }
    }
}
