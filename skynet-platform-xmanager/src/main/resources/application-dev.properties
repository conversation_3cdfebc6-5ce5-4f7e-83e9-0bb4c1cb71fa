#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=2230
server.servlet.session.cookie.name=SkynetUI
#server.session.timeout=300000
#------------------------------------------------------------------------------------------------------
# Spring Cloud Config
#------------------------------------------------------------------------------------------------------
spring.cloud.config.enabled=false
#------------------------------------------------------------------------------------------------------
# Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.file.path=../log
logging.file.name=<EMAIL>
logging.config=classpath:logback-xmanager.xml
#------------------------------------------------------------------------------------------------------
# Application Info
#------------------------------------------------------------------------------------------------------
spring.application.name=ant-xmanager@ant
skynet.action-point=ant-xmanager@ant
skynet.actionId=ant-xmanager@ant
#------------------------------------------------------------------------------------------------------
# Zookeeper Configuration
#------------------------------------------------------------------------------------------------------
skynet.zookeeper.cluster_name=skynet
skynet.zookeeper.connection_timeout=30000
skynet.zookeeper.enabled=true
skynet.zookeeper.session_timeout=20000
#------------------------------------------------------------------------------------------------------
# Data Source & Debug
#------------------------------------------------------------------------------------------------------
skynet.zookeeper.server_list=10.103.240.170:2181
debug=true
skynet.k8s.namespace=lynxiao-svc
skynet.backup.enabled=false

skynet.xmanager.repo.path=/Users/<USER>/Downloads/skynet