#------------------------------------------------------------------------------------------------------
# Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=2230
server.servlet.session.cookie.name=skynet-manager
spring.servlet.multipart.max-file-size=4096MB
spring.servlet.multipart.max-request-size=4096MB
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
skynet.platform.manager.enabled=true
skynet.ui.version=v3
skynet.api.swagger2.enabled=false
#------------------------------------------------------------------------------------------------------
# Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.config=classpath:logback-xmanager.xml
logging.level.org.springframework.cloud.config.server.encryption.EncryptionController=OFF
logging.level.org.springframework.security.config.annotation.web.builders.WebSecurity=OFF
#logging.level.skynet.platform.manager.admin.service.ActuatorService=OFF
logging.level.skynet.platform.manager.auth.security=ERROR
logging.level.skynet.platform.common.env.BootEnvironment=ERROR
#------------------------------------------------------------------------------------------------------
# Authentication & Security
#------------------------------------------------------------------------------------------------------
skynet.auth.api-key=skynet
skynet.auth.api-secret=SKYNET_API_SECRET_PLACEHOLDER
#skynet.k8s.namespace=default
skynet.security.enabled=true
skynet.security.sign-auth.enabled=true
skynet.security.sign-auth.app.skynet=${skynet.auth.api-secret}
skynet.security.sign-auth.app.feign=S1K2Y3N4E5T0F8B76E7181A12BF1FNET
skynet.security.sign-auth.path-patterns=/repo/**,/skynet/auth/token,/skynet/auth/addUser,/skynet/auth/resetPwd
skynet.security.sign-auth.ignore-patterns=
# base-auth 只有 /actuator/prometheus 为base-auth
skynet.security.base-auth.enabled=true
skynet.security.base-auth.path-patterns=${spring.cloud.config.server.prefix}/**,/skynet/config/**,/grafana/**,/actuator/prometheus
skynet.security.base-auth.ignore-patterns=${spring.cloud.config.server.prefix}/**crypt
skynet.security.base-auth.actuator-security-enabled=false
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=skynet2230
# actuator 采用 form表单
skynet.security.form-auth.enabled=true
skynet.security.form-auth.path-patterns=/**
skynet.security.form-auth.ignore-patterns=/,/**/static/**,/**.ico,\
  /skynet/auth/updatePwd,\
  /skynet/auth/logout,\
  /skynet/ui/**,\
  /skynet-log/**/view,\
  /skynet/auth/login,\
  /skynet/health,\
  ${spring.cloud.config.server.prefix}/**crypt
skynet.security.form-auth.login-path=/#/login
skynet.security.rsa-private-file=classpath:rsa-key/rsa_2048_priv.pem
skynet.security.rsa-public-file=classpath:rsa-key/rsa_2048_pub.pem
# 关闭默认的 skynet 框架 默认的 login 功能
skynet.security.form-auth.default-login-enabled=false
#------------------------------------------------------------------------------------------------------
# UI & Static Resources
#------------------------------------------------------------------------------------------------------
spring.thymeleaf.prefix=classpath:/static/${skynet.ui.version}/
spring.web.resources.static-locations=${spring.thymeleaf.prefix}
#------------------------------------------------------------------------------------------------------
# Spring Cloud & Cache
#------------------------------------------------------------------------------------------------------
spring.cache.type=SIMPLE
spring.cloud.config.label=${spring.application.name}
spring.cloud.config.name=${spring.application.name}
spring.cloud.config.uri=http://localhost:9000
spring.profiles.active=skynet-zk-config
spring.cloud.config.server.prefix=/skynetconfig
management.endpoint.env.show-values=always
#------------------------------------------------------------------------------------------------------
# WebShell
#------------------------------------------------------------------------------------------------------
skynet.webshell.enabled=true
skynet.webshell.sftp-enabled=true
