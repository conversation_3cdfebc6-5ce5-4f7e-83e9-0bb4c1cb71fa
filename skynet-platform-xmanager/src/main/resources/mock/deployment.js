[
    {
        "actions": [
            {
                "actionID": "mysql",
                "actionName": "人脸服务-mysql",
                "actionPoint": "mysql@face",
                "pluginCode": "face",
                "pluginName": "人脸识别服务",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 3332,
                "port": 2630,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 1,
                "status": "ONLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "es",
                "actionName": "人脸服务-es",
                "actionPoint": "es@face",
                "pluginCode": "face",
                "pluginName": "人脸识别服务",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 3335,
                "port": 2631,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 2,
                "status": "ONLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "api",
                "actionName": "人脸服务-api",
                "actionPoint": "api@face",
                "pluginCode": "face",
                "pluginName": "人脸识别服务",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 33312,
                "port": 28000,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 2,
                "status": "ONLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "engine",
                "actionName": "人脸服务-引擎",
                "actionPoint": "engine@face",
                "pluginCode": "face",
                "pluginName": "人脸识别服务",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 33313,
                "port": 28001,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 3,
                "status": "ONLINE",
                "upTime": "12小时12分"
            }
        ],
        "agentActionID": "ant-xagent@ant",
        "agentActionPoint": "ant-xagent@ant",
        "agentPort": 6230,
        "agentStatus": "ONLINE",
        "ip": "************",
        "serverInfo": null,
        "serverTags": ['人脸应用']
    },
    {
        "actions": [
            {
                "actionID": "prometheus",
                "actionName": "prometheus",
                "actionPoint": "prometheus@ant-mon",
                "pluginCode": "ant-mon",
                "pluginName": "skynet-监控",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 3332,
                "port": 2630,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 1,
                "status": "ONLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "grafana",
                "actionName": "grafana",
                "actionPoint": "grafana@ant-mon",
                "pluginCode": "ant-mon",
                "pluginName": "skynet-监控",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 3335,
                "port": 2631,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 2,
                "status": "OFFLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "node-exporter@ant-mon",
                "actionName": "主机监控",
                "actionPoint": "node-exporter@ant-mon",
                "pluginCode": "ant-mon",
                "pluginName": "skynet-监控",
                "enabled": false,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 33312,
                "port": 28000,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 4,
                "status": "OFFLINE",
                "upTime": "12小时12分"
            },
            {
                "actionID": "config-center@ant-mon",
                "actionName": "监控配置中心",
                "actionPoint": "config-center@ant-mon",
                "pluginCode": "ant-mon",
                "pluginName": "skynet-监控",
                "enabled": true,
                "homePageURL": "",
                "instanceIndex": 0,
                "pid": 33313,
                "port": 28001,
                "startTime": "2020-07-04 00:00:00",
                "startupOrder": 4,
                "status": "ONLINE",
                "upTime": "12小时12分"
            }
        ],
        "agentActionID": "ant-xagent@ant",
        "agentActionPoint": "ant-xagent@ant",
        "agentPort": 6230,
        "agentStatus": "ONLINE",
        "ip": "************",
        "serverInfo": null,
        "serverTags": ['监控基础层']
    }
]