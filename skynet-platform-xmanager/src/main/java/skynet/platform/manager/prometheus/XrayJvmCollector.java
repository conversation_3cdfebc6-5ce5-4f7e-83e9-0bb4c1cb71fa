//package skynet.platform.manager.prometheus;
//
//import io.prometheus.client.Collector;
//import io.prometheus.client.CollectorRegistry;
//import io.prometheus.client.GaugeMetricFamily;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import skynet.boot.SkynetProperties;
//import skynet.boot.common.OsUtil;
//
//import jakarta.annotation.PostConstruct;
//import java.lang.management.GarbageCollectorMXBean;
//import java.lang.management.ManagementFactory;
//import java.lang.management.MemoryMXBean;
//import java.lang.management.ThreadMXBean;
//import java.lang.reflect.Field;
//import java.lang.reflect.Modifier;
//import java.util.*;
//
//@Slf4j
//@Component
//public class XrayJvmCollector extends Collector implements Collector.Describable {
//
//    private static final List<String> LABEL_NAMES = Arrays.asList("pid", "name");
//
//    private static final String METRIC_NAME_MEM_NONHEAP_USED = "xray_jvm_mem_nonheap_used";
//    private static final String METRIC_NAME_MEM_NONHEAP_MAX = "xray_jvm_mem_nonheap_max";
//    private static final String METRIC_NAME_MEM_NONHEAP_INIT = "xray_jvm_mem_nonheap_init";
//    private static final String METRIC_NAME_MEM_NONHEAP_COMMITTED = "xray_jvm_mem_nonheap_committed";
//    private static final String METRIC_NAME_MEM_HEAP_USED = "xray_jvm_mem_heap_used";
//    private static final String METRIC_NAME_MEM_HEAP_MAX = "xray_jvm_mem_heap_max";
//    private static final String METRIC_NAME_MEM_HEAP_INIT = "xray_jvm_mem_heap_init";
//    private static final String METRIC_NAME_MEM_HEAP_COMMITTED = "xray_jvm_mem_heap_committed";
//    private static final String METRIC_NAME_GC_PS_MARKSWEEP_COUNT = "xray_jvm_gc_ps_marksweep_count";
//    private static final String METRIC_NAME_GC_PS_MARKSWEEP_TIME = "xray_jvm_gc_ps_marksweep_time";
//    private static final String METRIC_NAME_GC_PS_SCAVENGE_COUNT = "xray_jvm_gc_ps_scavenge_count";
//    private static final String METRIC_NAME_GC_PS_SCAVENGE_TIME = "xray_jvm_gc_ps_scavenge_time";
//    private static final String METRIC_NAME_THREAD_DAEMON_COUNT = "xray_jvm_thread_daemon_count";
//    private static final String METRIC_NAME_THREAD_PEAK_COUNT = "xray_jvm_thread_peak_count";
//    private static final String METRIC_NAME_THREAD_COUNT = "xray_jvm_thread_count";
//
//    private static final List<String> ALL_METRIC_NAMES = new LinkedList<>();
//
//    static {
//
//        try {
//            for (Field field : XrayJvmCollector.class.getDeclaredFields()) {
//                if (field.getName().startsWith("METRIC_NAME") && Modifier.isStatic(field.getModifiers())) {
//                    log.debug("try add metric name ={}", field.getName());
//                    field.setAccessible(true);
//                    ALL_METRIC_NAMES.add(field.get(XrayJvmCollector.class).toString());
//                }
//            }
//        } catch (Exception e) {
//            log.error("", e);
//        }
//
//    }
//
//    private final SkynetProperties skynetProperties;
//    private final CollectorRegistry collectorRegistry;
//
//    public XrayJvmCollector(SkynetProperties skynetProperties, CollectorRegistry collectorRegistry) {
//        this.skynetProperties = skynetProperties;
//        this.collectorRegistry = collectorRegistry;
//    }
//
//    @PostConstruct
//    public void init() {
//        super.register(collectorRegistry);
//    }
//
//    @Override
//    public List<MetricFamilySamples> collect() {
//
//        List<MetricFamilySamples> ret;
//        try {
//            String pid = "" + OsUtil.getCurrentPid();
//            String actionPoint = skynetProperties.getActionPoint();
//            Map<String, GaugeMetricFamily> metricMap = metricMap();
//            List<GarbageCollectorMXBean> gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
//            MemoryMXBean memMXBean = ManagementFactory.getMemoryMXBean();
//            ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
//            handleGCMXBean(pid, actionPoint, gcMXBeans, metricMap);
//            handleMemoryMXBean(pid, actionPoint, memMXBean, metricMap);
//            handleThreadMXBean(pid, actionPoint, threadMXBean, metricMap);
//            ret = new LinkedList<>(metricMap.values());
//        } catch (Exception e) {
//            log.error("", e);
//            throw e;
//        }
//
//        return ret;
//    }
//
//    @Override
//    public List<MetricFamilySamples> describe() {
//        return Collections.emptyList();
//    }
//
//    private Map<String, GaugeMetricFamily> metricMap() {
//        Map<String, GaugeMetricFamily> ret = new HashMap<>();
//        for (String metricName : ALL_METRIC_NAMES) {
//            ret.put(metricName, new GaugeMetricFamily(metricName, "", LABEL_NAMES));
//        }
//        return ret;
//    }
//
//    private void handleMemoryMXBean(String pid, String name, MemoryMXBean bean, Map<String, GaugeMetricFamily> metricMap) {
//        List<String> labelValues = Arrays.asList(pid, name);
//        metricMap.get(METRIC_NAME_MEM_HEAP_MAX).addMetric(labelValues, bean.getHeapMemoryUsage().getMax());
//        metricMap.get(METRIC_NAME_MEM_HEAP_USED).addMetric(labelValues, bean.getHeapMemoryUsage().getUsed());
//        metricMap.get(METRIC_NAME_MEM_HEAP_INIT).addMetric(labelValues, bean.getHeapMemoryUsage().getInit());
//        metricMap.get(METRIC_NAME_MEM_HEAP_COMMITTED).addMetric(labelValues, bean.getHeapMemoryUsage().getCommitted());
//        metricMap.get(METRIC_NAME_MEM_NONHEAP_MAX).addMetric(labelValues, bean.getNonHeapMemoryUsage().getMax());
//        metricMap.get(METRIC_NAME_MEM_NONHEAP_USED).addMetric(labelValues, bean.getNonHeapMemoryUsage().getUsed());
//        metricMap.get(METRIC_NAME_MEM_NONHEAP_INIT).addMetric(labelValues, bean.getNonHeapMemoryUsage().getInit());
//        metricMap.get(METRIC_NAME_MEM_NONHEAP_COMMITTED).addMetric(labelValues, bean.getNonHeapMemoryUsage().getCommitted());
//    }
//
//    private void handleGCMXBean(String pid, String name, List<GarbageCollectorMXBean> beans, Map<String, GaugeMetricFamily> metricMap) {
//        List<String> labelValues = Arrays.asList(pid, name);
//        for (GarbageCollectorMXBean bean : beans) {
//            if ("PS Scavenge".equals(bean.getName())) {
//                metricMap.get(METRIC_NAME_GC_PS_SCAVENGE_COUNT).addMetric(labelValues, bean.getCollectionCount());
//                metricMap.get(METRIC_NAME_GC_PS_SCAVENGE_TIME).addMetric(labelValues, bean.getCollectionTime());
//            } else if ("PS MarkSweep".equals(bean.getName())) {
//                metricMap.get(METRIC_NAME_GC_PS_MARKSWEEP_COUNT).addMetric(labelValues, bean.getCollectionCount());
//                metricMap.get(METRIC_NAME_GC_PS_MARKSWEEP_TIME).addMetric(labelValues, bean.getCollectionTime());
//            }
//        }
//    }
//
//    private void handleThreadMXBean(String pid, String name, ThreadMXBean bean, Map<String, GaugeMetricFamily> metricMap) {
//        List<String> labelValues = Arrays.asList(pid, name);
//        metricMap.get(METRIC_NAME_THREAD_COUNT).addMetric(labelValues, bean.getThreadCount());
//        metricMap.get(METRIC_NAME_THREAD_DAEMON_COUNT).addMetric(labelValues, bean.getDaemonThreadCount());
//        metricMap.get(METRIC_NAME_THREAD_PEAK_COUNT).addMetric(labelValues, bean.getPeakThreadCount());
//    }
//
//}
