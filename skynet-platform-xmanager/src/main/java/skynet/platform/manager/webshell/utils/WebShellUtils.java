/*
 * Copyright © 2020-present zmzhou-star. All Rights Reserved.
 */

package skynet.platform.manager.webshell.utils;

import skynet.platform.manager.webshell.Constants;

/**
 * 常用工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @title WebShellUtils
 * @date 2021/2/23 20:45
 */
public final class WebShellUtils {
    private WebShellUtils() {
    }

    /**
     * 文件大小转换单位
     *
     * @param size 文件大小
     */
    public static String convertFileSize(long size) {
        long kb = Constants.KB;
        long mb = kb * Constants.KB;
        long gb = mb * Constants.KB;
        String fileSize;
        if (size >= gb) {
            fileSize = String.format("%.1f GB", (float) size / (float) gb);
        } else {
            float f;
            if (size >= mb) {
                f = (float) size / (float) mb;
                fileSize = String.format(f > 100.0F ? "%.0f MB" : "%.1f MB", f);
            } else if (size >= kb) {
                f = (float) size / (float) kb;
                fileSize = String.format(f > 100.0F ? "%.0f KB" : "%.1f KB", f);
            } else {
                fileSize = String.format("%d B", size);
            }
        }
        return fileSize;
    }
}
