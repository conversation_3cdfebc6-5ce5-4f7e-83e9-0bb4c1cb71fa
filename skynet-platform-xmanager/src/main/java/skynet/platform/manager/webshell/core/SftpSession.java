package skynet.platform.manager.webshell.core;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import skynet.platform.manager.webshell.Constants;
import skynet.platform.manager.webshell.utils.FileType;
import skynet.platform.manager.webshell.utils.SftpFileUtils;
import skynet.platform.manager.webshell.vo.WebShellLoginData;

import java.io.File;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Vector;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SFTP服务器工具类
 * SftpUtils
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/25 11:46
 */
@Slf4j
public final class SftpSession implements AutoCloseable {

    private ChannelSftp channelSftp;
    private final ShellSession shellSession;

    public SftpSession(WebShellLoginData shellLoginData) throws JSchException {
        this.shellSession = new ShellSession(shellLoginData);
        this.shellSession.login("sftp");
        this.channelSftp = (ChannelSftp) shellSession.getChannel();
    }

    /**
     * 将输入流上传到SFTP服务器，作为文件
     *
     * @param directory    上传到SFTP服务器的路径
     * @param sftpFileName 上传到SFTP服务器后的文件名
     * @param input        输入流
     * @throws SftpException SftpException
     */
    public void upload(String directory, String sftpFileName, InputStream input) throws SftpException {
        long start = System.currentTimeMillis();
        // 创建不存在的文件夹，并切换到文件夹
        createDir(directory);
        // 上传文件
        channelSftp.put(input, sftpFileName);
        log.info("upload file successfully！ cost time：{}ms", (System.currentTimeMillis() - start));
    }

    /**
     * 下载文件
     *
     * @param path SFTP服务器的文件路径
     * @return 输入流
     */
    public InputStream download(String path) {
        Assert.hasText(path, "path is blank.");
        File file = new File(path.trim());
        return download(file.getParent(), file.getName());
    }

    /**
     * 下载文件
     *
     * @param directory SFTP服务器的文件路径
     * @param fileName  SFTP服务器上的文件名
     * @return 输入流
     */
    public InputStream download(String directory, String fileName) {
        try {
            if (StringUtils.isNotBlank(directory)) {
                channelSftp.cd(directory);
            }
            log.info("download file:{}/{}", directory, fileName);
            return channelSftp.get(fileName);
        } catch (SftpException e) {
            log.error("download file:{}/{}exception！", directory, fileName, e);
        }
        return null;
    }

    /**
     * 删除文件或者空文件夹
     *
     * @param directory SFTP服务器的文件路径
     * @param fileName  删除的文件名称
     * @return 删除结果
     */
    private boolean delete(String directory, String fileName) {
        String file = directory + Constants.SEPARATOR + fileName;
        try {
            Vector<ChannelSftp.LsEntry> vector = listFiles(file);
            // 用户权限处理
            if (vector.size() > 0 && (!(Constants.USER_ROOT.equals(shellSession.getUsername())
                    || shellSession.getUsername().equals(SftpFileUtils.getOwner(vector.getFirst().getLongname()))))) {
                log.warn("user {} does not have permission to delete files：{}", shellSession.getUsername(), file);
                return false;
            }
            channelSftp.cd(directory);
            if (isDirExists(file)) {
                // 删除空文件夹
                channelSftp.rmdir(fileName);
            } else {
                channelSftp.rm(fileName);
            }
            log.info("delete file：{} successfully", file);
        } catch (SftpException e) {
            log.error("delete file exception：{}", file, e);
            return false;
        }
        return true;
    }

    /**
     * 删除文件或者文件夹
     *
     * @param path SFTP服务器的文件或者文件夹路径
     * @return 删除结果
     */
    public boolean delete(String path) {
        AtomicBoolean delFlag = new AtomicBoolean(true);
        Vector<ChannelSftp.LsEntry> vector = listFiles(path);
        // 是文件或者空文件夹
        if (isFileExists(path) || vector.isEmpty()) {
            // 文件所在目录
            File file = new File(path);
            return delete(file.getParent(), file.getName());
        } else if (isDirExists(path)) {
            // 1.先循环删除子文件
            vector.forEach(v -> {
                ChannelSftp.LsEntry lsEntry = v;
                // 如果是文件夹，递归删除
                if (FileType.DIRECTORY.getSign().equals(lsEntry.getLongname().substring(0, 1))) {
                    delFlag.set(delete(path + Constants.SEPARATOR + lsEntry.getFilename()));
                } else {
                    // 删除文件
                    delFlag.set(delete(path, lsEntry.getFilename()));
                }
            });
            // 2.再删除空文件夹
            delFlag.set(delete(path));
        }
        return delFlag.get();
    }

    /**
     * 获取文件夹下的文件列表
     *
     * @param directory 路径
     * @return 文件列表
     */
    public Vector<ChannelSftp.LsEntry> listFiles(String directory) {
        try {
            if (isDirExists(directory) || isFileExists(directory)) {
                Vector<ChannelSftp.LsEntry> vector = channelSftp.ls(directory);
                //移除上级目录和根目录："." ".."
                Iterator<ChannelSftp.LsEntry> it = vector.iterator();
                while (it.hasNext()) {
                    ChannelSftp.LsEntry lsEntry = it.next();
                    if (Constants.DOT.equals(lsEntry.getFilename())
                            || Constants.PARENT_DIRECTORY.equals(lsEntry.getFilename())) {
                        it.remove();
                    }
                }
                return vector;
            }
        } catch (SftpException e) {
            log.error("obtain folder information failed！", e);
        }
        return new Vector<>();
    }

    /**
     * 判断目录是否存在，不存在则创建，并进入目录
     *
     * @param createPath 路径
     * @return 创建成功并进入目录
     */
    public boolean createDir(String createPath) {
        try {
            if (isDirExists(createPath)) {
                this.channelSftp.cd(createPath);
                return true;
            }
            String[] pathArray = createPath.split(Constants.SEPARATOR);
            StringBuilder filePath = new StringBuilder(Constants.SEPARATOR);
            for (String path : pathArray) {
                if ("".equals(path)) {
                    continue;
                }
                filePath.append(path);
                // 路径如果是文件，跳过，保存到同级目录
                if (isFileExists(filePath.toString())) {
                    continue;
                }
                filePath.append(Constants.SEPARATOR);
                if (!isDirExists(filePath.toString())) {
                    // 建立目录
                    channelSftp.mkdir(filePath.toString());
                }
                // 并进入目录
                channelSftp.cd(filePath.toString());
            }
        } catch (SftpException e) {
            log.error("create folder exception！", e);
            return false;
        }
        return true;
    }

    /**
     * 判断目录是否存在
     *
     * @param directory 路径
     * @return 目录是否存在
     */
    public boolean isDirExists(String directory) {
        try {
            SftpATTRS attrs = this.channelSftp.lstat(directory);
            return null != attrs && attrs.isDir();
        } catch (Exception e) {
            log.error("check whether the directory is abnormal：{}", directory, e);
        }
        return false;
    }

    /**
     * 判断文件是否存在
     *
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public boolean isFileExists(String filePath) {
        try {
            SftpATTRS attrs = this.channelSftp.lstat(filePath);
            // 存在并且不是文件夹
            return null != attrs && !attrs.isDir();
        } catch (Exception e) {
            log.error("check whether the file is abnormal：{}", filePath, e);
        }
        return false;
    }


    /**
     * 修改文件名
     *
     * @param oldpath
     * @param newpath
     * @return
     */
    public boolean rename(String oldpath, String newpath) {
        try {
            channelSftp.rename(oldpath, newpath);
        } catch (SftpException e) {
            log.error("repair file name failed！{}", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public void close() throws Exception {
        this.shellSession.logout();
    }
}
