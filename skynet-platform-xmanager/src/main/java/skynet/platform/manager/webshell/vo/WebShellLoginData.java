/*
 * Copyright © 2020-present zmzhou-star. All Rights Reserved.
 */

package skynet.platform.manager.webshell.vo;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * web sftp登录信息
 */
@Getter
@Setter
public class WebShellLoginData extends Jsonable {

    /**
     * 主机IP
     */
    private String host;
    /**
     * 端口号 默认22
     */
    private Integer port = 22;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    private String privateKey;

    @Override
    public String toString() {
        return super.toString();
    }
}
