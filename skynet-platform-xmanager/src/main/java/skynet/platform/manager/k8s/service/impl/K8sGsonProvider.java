package skynet.platform.manager.k8s.service.impl;

import com.google.gson.*;

import java.lang.reflect.Type;
import java.time.*;

/**
 * 提供统一的 K8s Gson 实例和时间类型序列化器
 */
public class K8sGsonProvider {
    public static final Gson GSON_INSTANCE = new GsonBuilder()
            .registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeCustomSerializer())
            .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeCustomSerializer())
            .registerTypeAdapter(LocalDate.class, new LocalDateCustomSerializer())
            .registerTypeAdapter(LocalTime.class, new LocalTimeCustomSerializer())
            .registerTypeAdapter(ZonedDateTime.class, new ZonedDateTimeCustomSerializer())
            .registerTypeAdapter(Instant.class, new InstantCustomSerializer())
            .create();

    static abstract class AbstractDateTimeSerializer<T> implements JsonSerializer<T> {
        protected JsonObject buildDate(Integer year, Integer month, Integer day) {
            JsonObject date = new JsonObject();
            date.addProperty("year", year != null ? year : 0);
            date.addProperty("month", month != null ? month : 0);
            date.addProperty("day", day != null ? day : 0);
            return date;
        }

        protected JsonObject buildTime(Integer hour, Integer minute, Integer second, Integer nano) {
            JsonObject time = new JsonObject();
            time.addProperty("hour", hour != null ? hour : 0);
            time.addProperty("minute", minute != null ? minute : 0);
            time.addProperty("second", second != null ? second : 0);
            time.addProperty("nano", nano != null ? nano : 0);
            return time;
        }

        protected JsonObject buildOffset(Integer totalSeconds) {
            JsonObject offset = new JsonObject();
            offset.addProperty("totalSeconds", totalSeconds != null ? totalSeconds : 0);
            return offset;
        }

        protected JsonObject wrap(JsonObject date, JsonObject time, JsonObject offset) {
            JsonObject dateTime = new JsonObject();
            dateTime.add("date", date);
            dateTime.add("time", time);
            JsonObject wrapper = new JsonObject();
            wrapper.add("dateTime", dateTime);
            wrapper.add("offset", offset);
            return wrapper;
        }
    }

    static class OffsetDateTimeCustomSerializer extends AbstractDateTimeSerializer<OffsetDateTime> {
        @Override
        public JsonElement serialize(OffsetDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return wrap(
                    buildDate(src.getYear(), src.getMonthValue(), src.getDayOfMonth()),
                    buildTime(src.getHour(), src.getMinute(), src.getSecond(), src.getNano()),
                    buildOffset(src.getOffset().getTotalSeconds())
            );
        }
    }

    static class LocalDateTimeCustomSerializer extends AbstractDateTimeSerializer<LocalDateTime> {
        @Override
        public JsonElement serialize(LocalDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return wrap(
                    buildDate(src.getYear(), src.getMonthValue(), src.getDayOfMonth()),
                    buildTime(src.getHour(), src.getMinute(), src.getSecond(), src.getNano()),
                    buildOffset(0)
            );
        }
    }

    static class LocalDateCustomSerializer extends AbstractDateTimeSerializer<LocalDate> {
        @Override
        public JsonElement serialize(LocalDate src, Type typeOfSrc, JsonSerializationContext context) {
            return wrap(
                    buildDate(src.getYear(), src.getMonthValue(), src.getDayOfMonth()),
                    buildTime(0, 0, 0, 0),
                    buildOffset(0)
            );
        }
    }

    static class LocalTimeCustomSerializer extends AbstractDateTimeSerializer<LocalTime> {
        @Override
        public JsonElement serialize(LocalTime src, Type typeOfSrc, JsonSerializationContext context) {
            return wrap(
                    buildDate(0, 0, 0),
                    buildTime(src.getHour(), src.getMinute(), src.getSecond(), src.getNano()),
                    buildOffset(0)
            );
        }
    }

    static class ZonedDateTimeCustomSerializer extends AbstractDateTimeSerializer<ZonedDateTime> {
        @Override
        public JsonElement serialize(ZonedDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return wrap(
                    buildDate(src.getYear(), src.getMonthValue(), src.getDayOfMonth()),
                    buildTime(src.getHour(), src.getMinute(), src.getSecond(), src.getNano()),
                    buildOffset(src.getOffset() != null ? src.getOffset().getTotalSeconds() : 0)
            );
        }
    }

    static class InstantCustomSerializer extends AbstractDateTimeSerializer<Instant> {
        @Override
        public JsonElement serialize(Instant src, Type typeOfSrc, JsonSerializationContext context) {
            LocalDateTime ldt = LocalDateTime.ofEpochSecond(src.getEpochSecond(), src.getNano(), ZoneOffset.UTC);
            return wrap(
                    buildDate(ldt.getYear(), ldt.getMonthValue(), ldt.getDayOfMonth()),
                    buildTime(ldt.getHour(), ldt.getMinute(), ldt.getSecond(), ldt.getNano()),
                    buildOffset(0)
            );
        }
    }
}

