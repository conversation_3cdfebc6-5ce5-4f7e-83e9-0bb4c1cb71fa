package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sPodMetricService {

    /**
     * 获取 PodMetric 列表
     */
    List<JSONObject> getPodMetrics(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 PodMetric 详情
     */
    JSONObject getPodMetric(String ip, String namespace, String podName) throws Exception;
}
