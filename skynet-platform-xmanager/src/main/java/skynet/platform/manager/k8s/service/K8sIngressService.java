package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import skynet.platform.feign.model.K8sQuery;

import java.util.List;

public interface K8sIngressService {

    /**
     * 获取 Ingress 列表
     */
    List<JSONObject> getIngresss(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Ingress 详情
     */
    JSONObject getIngress(String ip, String namespace, String ingressName) throws Exception;

    /**
     * 获取 Ingress Yaml
     */
    String getIngressYaml(String ip, String namespace, String ingressName) throws Exception;

    /**
     * 删除 Ingress
     */
    JSONObject deleteIngress(String ip, String namespace, String ingressName) throws Exception;
}
