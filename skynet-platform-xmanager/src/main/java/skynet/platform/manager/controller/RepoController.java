package skynet.platform.manager.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import skynet.boot.common.domain.RestResponse;
import skynet.boot.common.domain.SampleState;
import skynet.platform.manager.admin.domain.FileRange;
import skynet.platform.manager.admin.domain.RepoFileDo;
import skynet.platform.manager.admin.service.RepoService;
import skynet.platform.manager.exception.BadRequestException;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.List;

/**
 * 资源仓库控制器
 *
 * <AUTHOR> 2018年9月26日 下午5:42:02
 */

@Slf4j
@RestController
@RequestMapping("/repo")
public class RepoController {

    private final RepoService repoService;

    public RepoController(RepoService repoService) {
        this.repoService = repoService;
    }

    /**
     * 列出资源仓库目录下的文件
     *
     * @param plugin
     * @return
     */
    @GetMapping(value = "/list", produces = {MediaType.APPLICATION_JSON_VALUE})
//    @ApiResponse(response = RestResponse.class, code = 200, message = "接口返回对象参数")
    @Operation(summary = "获取资源文件列表", description = "获取指定应用(plugin)的所有文件名满足正则表达式的文件列表(正则表达式缺失则返回所有)")
    public RestResponse<List<RepoFileDo>> list(@RequestParam(required = true) String plugin, @RequestParam(required = false, defaultValue = "") String regex) {

        Assert.hasText(plugin, "the plugin is blank.");
        RestResponse<List<RepoFileDo>> ret = new RestResponse<>();

        try {
            log.debug("/_list : plugin[{}] regex[{}]", plugin, regex);
            ret.setBody(this.repoService.list(plugin, regex));
        } catch (IOException e) {
            log.error("/_list : plugin[{}] regex[{}].error:{}", plugin, regex, e.getMessage());
            ret.setState(new SampleState(e));
        }
        return ret;
    }

    /**
     * 下载指定plugin关联的资源文件
     *
     * @param plugin
     * @param fileName 相对plugin资源目录的相对路径
     * @return
     * @throws IOException
     */
    @GetMapping(value = "/download")
    public void download(@RequestParam(required = true) String plugin,
                         @RequestParam(required = true) String fileName, //
                         @RequestHeader(required = false, name = "Range") String rangeHeader,
                         HttpServletResponse servletResp) throws IOException {

        log.debug("Download repo plugin:[{}]; file: [{}]. begin...", plugin, fileName);
        FileRange range = null;
        if (!StringUtils.isBlank(rangeHeader)) {
            try {
                range = FileRange.parse(rangeHeader);
                log.debug("range={}", range);
            } catch (BadRequestException e) {
                servletResp.setStatus(HttpStatus.BAD_REQUEST.value());
                servletResp.flushBuffer();
                return;
            }
        }
        servletResp.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        String filename = Paths.get(fileName).getFileName().toString();
        String encodedFileName = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        servletResp.addHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", encodedFileName));

        try {
            RepoFileDo repoFileDo = repoService.getFileInfo(plugin, fileName, false);

            servletResp.addHeader(HttpHeaders.LAST_MODIFIED, repoFileDo.getLastUpdateTime());
            servletResp.setHeader(HttpHeaders.CONTENT_LENGTH, "" + ((range != null) ? range.getLength() : repoFileDo.getFileSize()));

            servletResp.addHeader("FileName", encodedFileName);
            servletResp.addHeader("FileSize", String.valueOf(repoFileDo.getFileSize()));
            servletResp.addHeader("MD5", repoFileDo.getMd5sum());

            OutputStream out = servletResp.getOutputStream();
            if (range == null) {
                repoService.outputFile(plugin, fileName, repoFileDo.isArchived(), out);
            } else {
                repoService.outputFile(plugin, fileName, repoFileDo.isArchived(), range.getOffset(), range.getLength(), out);
            }
            servletResp.flushBuffer();
        } catch (FileNotFoundException e) {
            log.error("Download repo plugin:[{}]; file: [{}]. error. {}", plugin, fileName, e.getMessage());
            servletResp.sendError(500, e.getMessage());
        }

        log.debug("Download repo plugin:[{}]; file: [{}]. end.", plugin, fileName);
    }

}