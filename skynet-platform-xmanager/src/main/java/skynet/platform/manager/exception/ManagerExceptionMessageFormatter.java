package skynet.platform.manager.exception;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.DefaultExceptionMessageFormatter;
import skynet.boot.exception.message.ErrorMessage;
import skynet.platform.feign.model.SkynetApiResponse;

@Slf4j
@Component
public class ManagerExceptionMessageFormatter extends DefaultExceptionMessageFormatter {

    @Override
    public ErrorMessage format(Throwable e, int code) {
        if (e instanceof ApiException) {
            // 对 Kubernetes 异常特殊处理，优先从 responseBody 中获取 message
            return new SkynetApiResponse<>(code, getApiExceptionMessage((ApiException) e));
        } else {
            return new SkynetApiResponse<>(code, e.getMessage());
        }
    }

    @Override
    public ErrorMessage format(SkynetException e) {
        return format(e, e.getCode());
    }

    //
    // {
    //     "kind":"Status",
    //     "apiVersion":"v1",
    //     "metadata":{},
    //     "status":"Failure",
    //     "message":"pods \"skyline-console-skyline-6c87759855-5xwp\" not found",
    //     "reason":"NotFound",
    //     "details":{"name":"skyline-console-skyline-6c87759855-5xwp","kind":"pods"},
    //     "code":404
    // }
    //
    private String getApiExceptionMessage(ApiException e) {
        String responseBody = e.getResponseBody();
        try {
            if (responseBody != null) {
                JSONObject json = JSON.parseObject(responseBody);
                return json.getString("message");
            } else {
                return e.getMessage();
            }
        } catch (Exception ex) {
            log.warn("GetApiExceptionMessage Error: {}", ex.getMessage());
            return responseBody;
        }
    }
}
