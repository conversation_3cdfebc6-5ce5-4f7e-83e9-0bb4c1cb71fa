package skynet.platform.manager.admin.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RepoFileDo extends Jsonable {

    private String plugin;
    private String fileName;
    private String filePath;
    private boolean isDirectory;
    private long fileSize;
    private String lastUpdateTime;
    private String createTime;
    private String md5sum;
    private boolean isArchived;

    @Override
    public String toString() {
        return super.toString();
    }
}
