package skynet.platform.manager.admin.v3.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3ServerTag;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.List;

/**
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3ServerTagController implements V3ServerTag {
    private final IAntConfigService antConfigService;

    public V3ServerTagController(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    @Override
    public SkynetApiResponse<List<String>> getTags() {
        SkynetApiResponse<List<String>> resp;
        try {
            List<String> tags = this.antConfigService.getTags();
            resp = SkynetApiResponse.success(tags);
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }

    @Override
    @AuditLog(module = "服务器标签管理", operation = "更新标签", message = "tags=#{#tags}")
    public SkynetApiResponse<Void> updateTags(@RequestBody List<String> tags) {
        Assert.notNull(tags != null, "tags shouldn't be null");
        SkynetApiResponse<Void> resp;
        try {
            this.antConfigService.updateTags(tags);
            resp = SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("updateTags response={}", resp);
        return resp;
    }
}
