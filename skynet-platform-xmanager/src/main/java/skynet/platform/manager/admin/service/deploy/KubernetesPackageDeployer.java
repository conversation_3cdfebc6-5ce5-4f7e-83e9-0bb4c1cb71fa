package skynet.platform.manager.admin.service.deploy;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.cloud.tools.jib.api.*;
import com.google.cloud.tools.jib.event.EventHandlers;
import com.google.cloud.tools.jib.hash.Digests;
import com.google.cloud.tools.jib.http.FailoverHttpClient;
import com.google.cloud.tools.jib.image.json.V22ManifestListTemplate;
import com.google.cloud.tools.jib.image.json.V22ManifestListTemplate.ManifestDescriptorTemplate;
import com.google.cloud.tools.jib.image.json.V22ManifestTemplate;
import com.google.cloud.tools.jib.registry.ManifestAndDigest;
import com.google.cloud.tools.jib.registry.RegistryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.client.BaseAuthRestTemplateBuilder;
import skynet.boot.security.config.SkynetAuthClientProperties;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.domain.ServerLoginParam;
import skynet.platform.common.shell.Shell;
import skynet.platform.manager.admin.config.ManagerProperties;

import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;

/**
 * skynet 包部署
 */
@Slf4j
public class KubernetesPackageDeployer implements AutoCloseable {

    private final Shell shell;
    private final String ip;
    private final String skynetHome;
    private final ServerLoginParam serverLoginParam;
    private final BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder;
    private final ManagerEncryptor managerEncryptor;
    private final ManagerProperties managerProperties;

    public KubernetesPackageDeployer(ServerLoginParam serverLoginParam, String skynetHome, BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder, ManagerEncryptor managerEncryptor, ManagerProperties managerProperties) throws Exception {
        this.managerProperties = managerProperties;
        this.shell = Shell.build();
        this.ip = serverLoginParam.getIp();
        this.skynetHome = skynetHome;
        this.serverLoginParam = serverLoginParam;
        this.baseAuthRestTemplateBuilder = baseAuthRestTemplateBuilder;
        this.managerEncryptor = managerEncryptor;
    }

    /**
     * 部署，将 Skynet Agent 相关的镜像推送到镜像仓库
     */
    public void deploy(String localVersion) throws Exception {
        log.info("开始部署 skynet, ip={} skynetHome={} ...", ip, skynetHome);

        String k8sImagePath = String.format("%s/runtime/k8s/images", skynetHome);
        List<String> platforms = getAllPlatforms(k8sImagePath);

        if (managerProperties.isManifestPushStyleWithCmd()) {
            // 通过命令行登录docker
            dockerLoginWithShell();
        }

        // system images
        pushImage(k8sImagePath, platforms, "skynet-openjdk-8.tar", "openjdk", "8");
        pushImage(k8sImagePath, platforms, "skynet-centos-7.tar", "centos", "7");
        pushImage(k8sImagePath, platforms, "kube-rbac-proxy-v0.11.0.tar", "kubebuilder/kube-rbac-proxy", "v0.11.0");
        pushImage(k8sImagePath, platforms, "skynet-alpine-3.13.tar", "alpine", "3.13");
        pushImage(k8sImagePath, platforms, "metrics-server-v0.6.1.tar", "metrics-server", "v0.6.1");

        // skynet images
        pushImage(k8sImagePath, platforms, "skynet-operator.tar", "operator", localVersion);
        pushImage(k8sImagePath, platforms, "skynet-agent.tar", "agent", localVersion);
        pushImage(k8sImagePath, platforms, "skynet-init.tar", "init", localVersion);
        pushImage(k8sImagePath, platforms, "skynet-mesh.tar", "mesh", localVersion);

        log.info("Skynet包分发部署完成.");
    }

    private List<String> getAllPlatforms(String k8sImagePath) {
        File k8sImageDir = new File(k8sImagePath);
        return Stream.of(Objects.requireNonNull(k8sImageDir.listFiles()))
                .map(File::getName)
                .toList();
    }

    /**
     * 将镜像文件推送到镜像仓库
     *
     * @param k8sImagePath 镜像目录
     * @param platforms    支持的平台（os-arch）
     * @param tarFileName  镜像的 tar 文件名
     * @param imageName    镜像名称
     * @param imageTag     镜像标签
     */
    private void pushImage(String k8sImagePath, List<String> platforms, String tarFileName, String imageName, String imageTag) throws Exception {
        System.setProperty("sendCredentialsOverHttp", "true");
        log.info("push image registryContextPath={}, imageName={}, tag={}, platforms={}", managerProperties.getRegistryContextPath(), imageName, imageTag, platforms);
        if (StringUtils.isNotBlank(managerProperties.getRegistryContextPath())) {
            imageName = String.format("%s/%s", managerProperties.getRegistryContextPath().trim(), imageName);
        }
        // 首先依次推送每个平台的镜像
        for (String platform : platforms) {
            String tarFilePath = String.format("%s/%s/%s", k8sImagePath, platform, tarFileName);
            String imageReference = String.format("%s/%s:%s-%s", serverLoginParam.getRegistryUrl(), imageName, imageTag, platform);
            log.info("Push tar file {} to {}", tarFilePath, imageReference);
            Jib.from(TarImage.at(Paths.get(tarFilePath)))
                    .containerize(
                            Containerizer.to(
                                    RegistryImage.named(imageReference)
                                            .addCredential(serverLoginParam.getRegistryUsername(), managerEncryptor.decrypt(serverLoginParam.getRegistryPassword()))
                            ).setAllowInsecureRegistries(true)
                    );
        }

        // 最后推送合并后的 manifest list
        try {
            if (managerProperties.isManifestPushStyleWithCmd()) {
                // 通过命令行推送多架构镜像manifest
                pushManifestWithShell(platforms, imageName, imageTag);
            } else {
                pushManifest(platforms, imageName, imageTag);
            }
        } catch (Exception e) {
            log.error("imageName {}, imageTag {}, push manifest failed", imageName, imageTag, e);
        }

    }

    /**
     * 通过命令行 推送镜像
     *
     * @param platforms platforms
     * @param imageName imageName
     * @param imageTag  imageTag
     */
    private void pushManifestWithShell(List<String> platforms, String imageName, String imageTag) throws Exception {
        String imageNameRegistry = String.format("%s/%s:%s", serverLoginParam.getRegistryUrl(), imageName, imageTag);
        deleteManifestWithShell(imageNameRegistry);

        List<String> imageQualifiers = new ArrayList<>(0);
        for (String platform : platforms) {
            String imageQualifier = String.format("%s-%s", imageNameRegistry, platform);
            imageQualifiers.add(imageQualifier);
        }
        String experimentalCmd = "export DOCKER_CLI_EXPERIMENTAL=enabled";
        String imageQualifiersCmd = String.format("docker manifest create %s %s --amend", imageNameRegistry, StringUtils.join(imageQualifiers, " "));
        String manifestCreate = this.shell.execCmd(experimentalCmd, imageQualifiersCmd);
        log.info("{} && {}, result {} ", experimentalCmd, imageQualifiersCmd, manifestCreate);
        String manifestPush = String.format("docker manifest push %s", imageNameRegistry);
        String manifestPushResult = this.shell.execCmd(experimentalCmd, manifestPush);
        log.info("{} && {}, result{}", experimentalCmd, manifestPush, manifestPushResult);
        // 执行结束删除 Manifest
        deleteManifestWithShell(imageNameRegistry);
    }

    /**
     * docker 登录通过命令行登录
     */
    private void dockerLoginWithShell() throws Exception {
        //开启docker 实现模式
        String dockerLogin = this.shell.execCmd(String.format("docker login -u %s -p %s %s", serverLoginParam.getRegistryUsername(),
                managerEncryptor.decrypt(serverLoginParam.getRegistryPassword()), serverLoginParam.getRegistryUrl()));
        log.info("docker login registry url  {}, username {}, login message {}", serverLoginParam.getRegistryUrl(), serverLoginParam.getRegistryUsername(), dockerLogin);
    }

    /**
     * 删除镜像 Manifest
     *
     * @param imageNameRegistry imageNameRegistry
     */
    private void deleteManifestWithShell(String imageNameRegistry) {
        try {
            String experimentalCmd = "export DOCKER_CLI_EXPERIMENTAL=enabled";
            String manifestDeleteCmd = String.format("docker manifest rm %s", imageNameRegistry);
            this.shell.execCmd(experimentalCmd, manifestDeleteCmd);
            log.info("{} && {}. success", experimentalCmd, manifestDeleteCmd);
        } catch (Exception e) {
            log.info("delete manifest {} error", imageNameRegistry, e);
        }
    }

    /**
     * 将多个镜像信息合并成 manifest list，支持多平台镜像
     */
    private void pushManifest(List<String> platforms, String imageName, String imageTag) throws Exception {

        // 初始化 RegistryClient
        FailoverHttpClient httpClient = new FailoverHttpClient(true, true, ignored -> {
        });
        RegistryClient registryClient =
                RegistryClient.factory(
                                EventHandlers.NONE, serverLoginParam.getRegistryUrl(), imageName, httpClient)
                        .setCredential(Credential.from(serverLoginParam.getRegistryUsername(), managerEncryptor.decrypt(serverLoginParam.getRegistryPassword())))
                        .newRegistryClient();
        registryClient.configureBasicAuth();

        // 获取每个平台镜像的 manifest 信息，合并到 manifest list 中
        V22ManifestListTemplate v22ManifestList = new V22ManifestListTemplate();
        for (String platform : platforms) {
            String os = platform.split("-")[0];
            String arch = platform.split("-")[1];
            String imageQualifier = String.format("%s-%s", imageTag, platform);
            ManifestAndDigest<V22ManifestTemplate> mad = registryClient.pullManifest(imageQualifier, V22ManifestTemplate.class);
            log.info("Get {}:{}'s' manifest: {}", imageName, imageQualifier, mad.getDigest().toString());
            ManifestDescriptorTemplate mdt = new ManifestDescriptorTemplate();
            mdt.setMediaType(mad.getManifest().getManifestMediaType());
            mdt.setSize(Digests.computeDigest(mad.getManifest()).getSize());
            mdt.setDigest(mad.getDigest().toString());
            mdt.setPlatform(arch, os);
            v22ManifestList.addManifest(mdt);
        }
        DescriptorDigest digest = registryClient.pushManifest(v22ManifestList, imageTag);
        log.info("Push manifest list {} successfully.", digest.toString());
    }

    /**
     * 获取版本号
     */
    public String getVersion() {
        log.debug("Fetch the remote server version.[ip={}]", ip);
        List<String> tags = new ArrayList<>(0);
        if (managerProperties.isManifestPushStyleWithCmd()) {
            try {
                String tagsCmd = "docker images | grep skynet/agent | awk '{print $2}'";
                String tagsCmdResult = this.shell.execCmd(tagsCmd);
                log.debug("fetch skynet/agent version={}, response={}", tagsCmd, tagsCmdResult);
                tags = Arrays.stream(tagsCmdResult.split("\\n")).toList();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            String url = String.format("http://%s/v2/%s/tags/list", serverLoginParam.getRegistryUrl(), "skynet/agent");

            log.info("Get url={}", url);
            RestTemplate restTemplate = buildRestTemplate();
            String response = restTemplate.getForObject(url, String.class);
            log.debug("Get url={}, response={}", url, response);

            if (StringUtils.isNotBlank(response)) {
                JSONObject json = JSON.parseObject(response);
                tags = json.getList("tags", String.class);
            }
        }
        if (!CollectionUtils.isEmpty(tags)) {
            return tags.stream().min(Comparator.reverseOrder()).get();
        }
        return null;
    }

    /**
     * 如果镜像仓库需要认证，构造一个带认证的 RestTemplate
     */
    private RestTemplate buildRestTemplate() {

        if (StringUtils.isBlank(serverLoginParam.getRegistryUsername()) ||
                StringUtils.isBlank(serverLoginParam.getRegistryPassword())) {
            return new RestTemplate();
        }

        return baseAuthRestTemplateBuilder.build(
                new SkynetAuthClientProperties()
                        .setUser(serverLoginParam.getRegistryUsername())
                        .setPassword(managerEncryptor.decrypt(serverLoginParam.getRegistryPassword()))
        );
    }

    @Override
    public void close() throws Exception {
        if (shell != null) {
            shell.close();
        }
    }
}
