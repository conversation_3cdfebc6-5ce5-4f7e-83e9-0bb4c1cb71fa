package skynet.platform.manager.admin.v3.controller;


import com.alibaba.fastjson2.JSON;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.PluginNotExistException;
import skynet.platform.feign.model.ActionDefinitionDto;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.model.NodeDescriptionDto;
import skynet.platform.feign.service.V3ActionDefinition;
import skynet.platform.manager.admin.service.PluginZkTextService;
import skynet.platform.manager.admin.v3.service.V3ActionDefinitionService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3ActionDefinitionController implements V3ActionDefinition {

    private final V3ActionDefinitionService v3ActionDefinitionService;
    private final PluginZkTextService pluginZkTextService;

    public V3ActionDefinitionController(V3ActionDefinitionService v3ActionDefinitionService, PluginZkTextService pluginZkTextService) {
        this.v3ActionDefinitionService = v3ActionDefinitionService;
        this.pluginZkTextService = pluginZkTextService;
    }

    @Override
    public GetDefinitionsResponse getDefinitions(@Parameter(description = "过滤条件：系统编码")
                                                 @RequestParam(required = false, name = "pluginCode") String pluginCode,
                                                 @Parameter(description = "过滤条件：功能标签编码")
                                                 @RequestParam(required = false, name = "actionLabelCode") String actionLabelCode) throws Exception {
        log.debug("getDefinitions pluginCode={}", pluginCode);
        GetDefinitionsResponse response = new GetDefinitionsResponse();
        response.setData(v3ActionDefinitionService.list(pluginCode, actionLabelCode));
        log.debug("getDefinitions response={}", response);
        return response;
    }


    @Override
    public GetDefinitionResponse getDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint) throws IOException {
        log.debug("getDefinition actionPoint={}", actionPoint);
        GetDefinitionResponse response = new GetDefinitionResponse();
        response.setData(v3ActionDefinitionService.get(actionPoint));

        log.debug("getDefinition response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "服务定义", operation = "新增服务定义", message = "createDefinition param=#{#request}")
    public NoDataResponse createDefinition(@RequestBody ActionDefinitionDto request) {
        log.debug("createDefinition param={}", request);
        NoDataResponse response = new NoDataResponse();
        try {
            v3ActionDefinitionService.create(request);
        } catch (Exception e) {
            log.error("createDefinition error. request={}", request, e);
            response.setException(e);
        }
        log.debug("createDefinition response={}", response);
        return response;
    }

    @Override
    public NoDataResponse deleteDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint) {
        log.info("deleteDefinition actionPoint = {}", actionPoint);
        NoDataResponse response = new NoDataResponse();
        try {
            v3ActionDefinitionService.delete(actionPoint);
        } catch (Exception e) {
            log.error("deleteDefinition error. actionPoint:{}", actionPoint, e);
            response.setException(e);
        }
        log.info("deleteDefinition response:{}", response);
        return response;
    }


    @Override
    @AuditLog(module = "服务定义", operation = "归档服务定义", message = "actionPoint=#{#actionPoint};Detail=#{#request}")
    public NoDataResponse archiveDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint) {
        log.info("archiveDefinition actionPoint = {}", actionPoint);
        NoDataResponse response = new NoDataResponse();
        try {
            v3ActionDefinitionService.archive(actionPoint);
        } catch (Exception e) {
            log.error("archiveDefinition error. actionPoint:{}", actionPoint, e);
            response.setException(e);
        }
        log.info("archiveDefinition response:{}", response);
        return response;
    }


    @Override
    @AuditLog(module = "服务定义", operation = "恢复服务定义", message = "actionPoint=#{#actionPoint};Detail=#{#request}")
    public NoDataResponse restoreDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint) {
        log.info("restoreDefinition actionPoint = {}", actionPoint);
        NoDataResponse response = new NoDataResponse();
        try {
            v3ActionDefinitionService.restore(actionPoint);
        } catch (Exception e) {
            log.error("restoreDefinition error. actionPoint:{}", actionPoint, e);
            response.setException(e);
        }
        log.info("restoreDefinition response:{}", response);
        return response;
    }


    @Override
    @AuditLog(module = "服务定义", operation = "更新服务定义", message = "actionPoint=#{#actionPoint};Detail=#{#request}")
    public NoDataResponse updateDefinition(@Parameter(description = "服务坐标") @PathVariable String actionPoint,
                                           @RequestBody ActionDefinitionDto request) {
        log.debug("updateDefinition actionPoint:{}, param:{}", actionPoint, request);
        NoDataResponse response = new NoDataResponse();
        try {
            v3ActionDefinitionService.update(actionPoint, request);
        } catch (Exception e) {
            log.error("updateDefinition error. actionPoint:{}, param:{}", actionPoint, request, e);
            response.setException(e);
        }
        log.debug("updateDefinition response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "服务定义", operation = "导入定义", message = "导入服务定义")
    public NoDataResponse importDefinition(@RequestParam("file") MultipartFile file) {
        log.trace("import definition begin");
        NoDataResponse response = new NoDataResponse();
        try {
            List<String> zkTxtLines = IOUtils.readLines(file.getInputStream(), StandardCharsets.UTF_8);
            this.pluginZkTextService.importZkConfig(zkTxtLines);
        } catch (Exception e) {
            log.error("fail to import definition: ", e);
            response.setException(e);
        }
        log.trace("import definition finish");
        return response;
    }

    /**
     * 服务定义导出(ZkUI兼容的文本文件)
     *
     * @param codes 编码列表,支持系统编码和服务编码，后台智能判断
     * @return
     */
    @Override
    public ResponseEntity<StreamingResponseBody> exportDefs(@RequestParam String[] codes) {
        if (codes == null || codes.length == 0) {
            return ResponseEntity.badRequest().build();
        }
        List<String> contents = new ArrayList<>();
        try {
            contents.add(pluginZkTextService.exportZkConfig(Arrays.asList(codes)));
        } catch (PluginNotExistException e) {
            String msg = String.format("system [%s] does not exist", e.getPlugin());
            StreamingResponseBody body = outputStream -> outputStream.write(msg.getBytes(StandardCharsets.UTF_8));
            return ResponseEntity.badRequest().body(body);
        }

        StreamingResponseBody body = outputStream -> {
            for (String content : contents) {
                outputStream.write(content.getBytes(StandardCharsets.UTF_8));
            }
        };
        String filename = (codes.length > 1) ? "export" : codes[0];
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s.zk.config\"", filename));
        return new ResponseEntity<>(body, headers, HttpStatus.OK);
    }


    /**
     * 获取Action列表描述
     *
     * @param actionList action列表
     * @return
     */
    @Override
    public GetActionDescResponse fetchActionDesc(@RequestBody List<String> actionList) {
        log.debug("fetchActionDesc actionList:{}", actionList);
        GetActionDescResponse response = new GetActionDescResponse();
        try {
            List<NodeDescription> nodeDescriptionList = v3ActionDefinitionService.fetchActionDescList(actionList);
            response.setData(JSON.parseArray(JSON.toJSONString(nodeDescriptionList), NodeDescriptionDto.class));
        } catch (Exception e) {
            log.error("fetchActionDesc error. actionList:{} ", actionList, e);
            response.setException(e);
        }
        log.debug("fetchActionDesc response:{}", response);
        return response;
    }
}
