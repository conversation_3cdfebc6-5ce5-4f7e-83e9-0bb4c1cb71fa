package skynet.platform.manager.admin.v3.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.manager.admin.v3.service.V3ActionTagService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.util.List;

/**
 * <AUTHOR> by jianwu6 on 2020/8/18 17:02
 */
@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3ActionTagServiceImpl extends V3BaseServiceImpl implements V3ActionTagService {


    @Override
    public List<String> getTags() {
        String data = this.antConfigService.getData(getActionTagPath());
        return JSON.parseObject(data, new TypeReference<List<String>>() {
        });
    }

    @Override
    public void updateTags(List<String> tags) {
        if (tags == null) {
            throw new ApiRequestException(ApiRequestErrorCode.MISSING_PARAMETER);
        }
        List<String> tagList = tags.stream().distinct().toList();
        this.antConfigService.setData(getActionTagPath(), JSON.toJSONString(tagList));
    }

    private String getActionTagPath() {
        return String.format("%s/tags", this.antConfigService.getSkynetPluginPath());
    }
}
