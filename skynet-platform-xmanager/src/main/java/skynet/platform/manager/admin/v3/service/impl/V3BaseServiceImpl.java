package skynet.platform.manager.admin.v3.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ActionNotExistException;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.exception.PluginNotExistException;
import skynet.platform.feign.model.PluginDto;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.v3.DataTypeConverter;
import skynet.platform.manager.admin.v3.service.V3BaseService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3BaseServiceImpl implements V3BaseService {

    @Autowired
    protected IAntConfigService antConfigService;

    @Autowired
    protected ManagerProperties managerProperties;

    @Override
    public NodeDescription checkAction(String plugin, String action) {
        if (StringUtils.isBlank(plugin)) {
            throw new ApiRequestException(ApiRequestErrorCode.PLUGIN_BLANK);
        }
        if (StringUtils.isBlank(action)) {
            throw new ApiRequestException(ApiRequestErrorCode.ACTION_BLANK);
        }
        plugin = plugin.trim();
        action = action.trim();
        NodeDescription actionNodeDescription = this.antConfigService.getAction(plugin, action);
        if (actionNodeDescription == null) {
            log.error("the action [{}@{}] not exist", action, plugin);
            throw new ActionNotExistException(new ActionNameContract(plugin, action).getPoint());
        }
        return actionNodeDescription;
    }

    @Override
    public PluginDto checkPlugin(String plugin) {
        if (StringUtils.isBlank(plugin)) {
            throw new ApiRequestException(ApiRequestErrorCode.PLUGIN_BLANK);
        }
        plugin = plugin.trim();
        NodeDescription pluginNode = this.antConfigService.getPlugin(plugin);
        if (pluginNode == null) {
            log.error("the plugin [{}] not exist.", plugin);
            throw new PluginNotExistException(plugin);
        }
        return DataTypeConverter.toPluginDto(pluginNode);
    }
}
