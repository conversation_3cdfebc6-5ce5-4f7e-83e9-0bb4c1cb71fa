package skynet.platform.manager.admin.v3.service;


import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.model.ActionDefinitionDto;

import java.io.IOException;
import java.util.List;

/**
 * 获取服务定义
 *
 * <AUTHOR>
 */
public interface V3ActionDefinitionService extends V3BaseService {

    /**
     * 获取系统服务定义列表
     *
     * @param pluginCode      为空，将是所有服务
     * @param actionLabelCode
     * @return
     * @throws Exception
     */
    List<ActionDefinitionDto> list(String pluginCode, String actionLabelCode) throws Exception;

    /**
     * 获取某个action服务定义
     *
     * @param actionPoint
     * @return
     */
    ActionDefinitionDto get(String actionPoint) throws IOException;

    /**
     * 添加服务定义
     *
     * @param dto
     */
    void create(ActionDefinitionDto dto);

    /**
     * 删除服务定义
     *
     * @param actionPoint
     */
    void delete(String actionPoint);

    /**
     * 更新服务定义
     *
     * @param actionPoint
     * @param dto
     */
    void update(String actionPoint, ActionDefinitionDto dto);


    List<NodeDescription> fetchActionDescList(List<String> actionList);


    /**
     * 归档服务定义
     *
     * @param actionPoint
     */
    void archive(String actionPoint) throws Exception;

    /**
     * 恢复服务定义
     *
     * @param actionPoint
     */
    void restore(String actionPoint) throws Exception;

//    /**
//     * 导入服务定义
//     * @param file
//     */
//    void importDefinition(MultipartFile file) throws IOException, InterruptedException;
//
//    String exportByPluginCode(String pluginCode) throws PluginNotExistException;
}
