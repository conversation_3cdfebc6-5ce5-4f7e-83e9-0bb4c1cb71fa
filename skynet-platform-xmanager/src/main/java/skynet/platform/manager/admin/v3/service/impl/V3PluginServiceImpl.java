package skynet.platform.manager.admin.v3.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.exception.PluginAlreadyExistException;
import skynet.platform.feign.model.PluginDto;
import skynet.platform.manager.admin.v3.service.V3PluginService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.io.IOException;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3PluginServiceImpl extends V3BaseServiceImpl implements V3PluginService {

    private final IAntConfigService antConfigService;
    private final SkynetSettingManager skynetSettingManager;

    public V3PluginServiceImpl(IAntConfigService antConfigService, SkynetSettingManager skynetSettingManager) {
        this.antConfigService = antConfigService;
        this.skynetSettingManager = skynetSettingManager;
    }

    @Override
    public void createPlugin(PluginDto pluginDto) {
        NodeDescription pluginNode = this.antConfigService.getPlugin(pluginDto.getCode());
        if (pluginNode != null) {
            log.error("the plugin [{}] exist.", pluginDto.getCode());
            throw new PluginAlreadyExistException(pluginDto.getCode());
        }
        this.savePlugin(pluginDto);
    }


    @Override
    public void savePlugin(PluginDto pd) {
        log.info("savePlugin pd={}", pd);

        if (!pd.isValid()) {
            throw new ApiRequestException(ApiRequestErrorCode.MISSING_PARAMETER);
        }

        boolean isOk = Pattern.matches("^[0-9a-zA-Z-_]+$", pd.getCode());
        if (!isOk) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ACTION_CODE_ILLEGAL);
        }

        // 添加系统根节点 Node
        String path = String.format("%s/%s", antConfigService.getSkynetPluginPath(), pd.getCode());
        log.info(String.format("add system path[%s]", path));
        if (!antConfigService.getZkConfigService().exists(path)) {
            antConfigService.getZkConfigService().putNode(antConfigService.getSkynetPluginPath(), pd.getCode());
            antConfigService.setData(String.format("%s/action/_desc", path),
                    "Action Specifies the naming rule for the service definition list： [mq|rest|rpc|ant]-{action}-v{version number}[-{other identification}] " +
                            "example： mq-ist-v10-chin. ps：reference configuration items in setting{$ref@setting:setting_key}");
        }

        antConfigService.setData(String.format("%s/_name", path), pd.getName());
        antConfigService.setData(String.format("%s/_index", path), String.valueOf(pd.getIndex()));
        antConfigService.setData(String.format("%s/setting/_desc", path), String.format("[%s]_自定义配置", pd.getName()));
//        antConfigService.setData(String.format("%s/setting/_properties/_desc", path),  String.format("[%s]系统属性配置", pd.getName()));
//        antConfigService.setData(String.format("%s/setting/_logger/_desc", path),    String.format("[%s]日志级别配置", pd.getName()));

        if (StringUtils.isNotBlank(pd.getDescription())) {
            antConfigService.setData(String.format("%s/_desc", path), pd.getDescription());
        }

        if (StringUtils.isNotBlank(pd.getVersion())) {
            antConfigService.setData(String.format("%s/_version", path), pd.getVersion());
        }
    }

    /**
     * reorder plugin
     *
     * @param pluginList plugin code list
     */
    @Override
    public void reorderPlugin(List<String> pluginList) {
        log.debug("Set Plugin order，pluginList={}. begin..", pluginList);
        int index = 1;
        for (String plugin : pluginList) {
            antConfigService.setData(String.format("%s/%s/_index", antConfigService.getSkynetPluginPath(), plugin), String.valueOf((index++) * 10));
        }
        log.info("Set Plugin order OK.");
    }

    @Override
    public void updateProperties(String pluginCode, String value) throws IOException {
        log.trace("update plugin properties of {}", pluginCode);
        skynetSettingManager.getPropertiesService().setProps(pluginCode, value);
        log.trace("update plugin properties of {} finish.", pluginCode);
    }

    @Override
    public void updateLoggingLevels(String pluginCode, String value) throws IOException {
        log.trace("update plugin logging levels of {}", pluginCode);
        skynetSettingManager.getLoggerServices().setProps(pluginCode, value);
        log.trace("update plugin logging levels of {} finish.", pluginCode);
    }

    @Override
    public String getProperties(String pluginCode) throws IOException {
        return skynetSettingManager.getPropertiesService().getProps(pluginCode);
    }

    @Override
    public String getLoggingLevels(String pluginCode) throws IOException {
        return skynetSettingManager.getLoggerServices().getProps(pluginCode);
    }
}
