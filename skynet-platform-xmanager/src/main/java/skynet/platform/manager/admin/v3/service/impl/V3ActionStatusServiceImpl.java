package skynet.platform.manager.admin.v3.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.AntActionRegist;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.ActionStatusDto;
import skynet.platform.feign.model.ActionStatusUpdateDto;
import skynet.platform.manager.admin.domain.BootServerView;
import skynet.platform.manager.admin.service.ActionStatusService;
import skynet.platform.manager.admin.service.ServerService;
import skynet.platform.manager.admin.v3.service.V3ActionStatusService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by jianwu6 on 2020/7/28 11:06
 */
@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3ActionStatusServiceImpl extends V3BaseServiceImpl implements V3ActionStatusService {

    private final IAntConfigService antConfigService;
    private final ActionStatusService actionStatusService;
    private final ServerService serverService;

    public V3ActionStatusServiceImpl(IAntConfigService antConfigService, ActionStatusService actionStatusService, ServerService serverService) {
        this.antConfigService = antConfigService;
        this.actionStatusService = actionStatusService;
        this.serverService = serverService;
    }

    @Override
    public List<ActionStatusDto> getAllStatus(String ip) throws Exception {
        return getStatus(null, ip);
    }

    @Override
    public List<ActionStatusDto> getStatus(String actionPoint, String ip) throws Exception {
        // 查询server
        List<AntServerParam> servers;
        if (StringUtils.isBlank(ip)) {
            servers = this.antConfigService.getServers();
        } else {
            AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
            if (antServerParam == null) {
                log.error("server [{}] is not registered", ip);
                throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
            }
            servers = new ArrayList<>(1);
            servers.add(antServerParam);
        }

        List<ActionStatusDto> statusDtoList = new ArrayList<>();
        //遍历每个agent 关联的 action
        for (AntServerParam server : servers) {
            if (server == null || server.getActions() == null) {
                continue;
            }
            int agentPort = server.getSsh() == null ? 0 : server.getSsh().getPort();

            for (AntActionRegist action : server.getActions()) {
                if (StringUtils.isNotBlank(actionPoint) && !actionPoint.equals(action.getPoint())) {
                    // 指定的 actionPoint 不存在
                    continue;
                }
                ActionStatusDto statusDto = new ActionStatusDto();
                statusDto.setAgentPort(agentPort);
                statusDto.setIp(server.getIp());
//                statusDto.setStatus(); todo 状态
                statusDto.setEnabled(action.isEnable());

                // 基本信息
                AntActionParam actionParam = this.antConfigService.getActionParam(action.getPlugin(), action.getCode());
                NodeDescription nodeDescription = this.antConfigService.getPlugin(action.getPlugin());
                statusDto.setActionPoint(action.getPoint());
                statusDto.setActionID(action.getPoint());
                statusDto.setActionName(action.getName());
                statusDto.setPluginCode(action.getPlugin());
                statusDto.setStartupOrder(action.getIndex());
                statusDto.setPluginName(nodeDescription.getName());
                statusDto.setHomePageURL(actionParam.getBootParam().getIndexPageUrl());

                // 运行状态详情
                Map<String, AntActionStatus> onlineActionNodeStatus = this.antConfigService.getOnlineActionNodeStatus(action.getPoint(), null);
                if (!CollectionUtils.isEmpty(onlineActionNodeStatus)) {
                    for (AntActionStatus actionStatus : onlineActionNodeStatus.values()) {
                        if (actionStatus != null && actionStatus.getIp().equals(server.getIp())) {
                            statusDto.setPid(Integer.parseInt(String.valueOf(actionStatus.getPid())));
                            statusDto.setPort(actionStatus.getPort());
                            statusDto.setUpTime(DateFormatUtils.format(actionStatus.getReport(), "yyyy-MM-dd HH:mm:ss"));
                            statusDto.setStartTime(DateFormatUtils.format(actionStatus.getStart(), "yyyy-MM-dd HH:mm:ss"));
                        }
                    }
                }
                statusDtoList.add(statusDto);
            }
        }
        return statusDtoList;
    }

    @Override
    public synchronized void update(String ip, List<ActionStatusUpdateDto> dtoList) {
        if (StringUtils.isBlank(ip)) {
            throw new ApiRequestException(ApiRequestErrorCode.MISSING_PARAMETER);
        }
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            log.error("server [{}] is not registered", ip);
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
        }

        boolean exist = false;
        for (ActionStatusUpdateDto dto : dtoList) {
            ActionNameContract actionNameContract = new ActionNameContract(dto.getActionPoint());
            checkAction(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
            for (AntActionRegist action : antServerParam.getActions()) {
                if (action.getPoint().equalsIgnoreCase(dto.getActionPoint())) {
                    action.setEnable(dto.isEnabled());
                    exist = true;
                    break;
                }
            }
        }
        if (exist) {
            this.antConfigService.setServerParam(antServerParam);
        } else {
            log.error("server [{}] is not allocated on the server[{}]上", JSON.toJSONString(dtoList), ip);
            throw new ApiRequestException(ApiRequestErrorCode.ACTION_NOT_IN_AGENT);
        }
    }

//    /**
//     * @param dto
//     */
//    @Override
//    public void update(ActionStatusUpdateDto dto) {
//        this.update(dto.getIp(), Collections.singletonList(dto));
//    }

    /**
     * 重启 action
     *
     * @param ip
     * @param actionIdList
     */
    @Override
    public void reboot(String ip, List<String> actionIdList) throws Exception {
        log.debug("RebootAction the action[IP={};{}] ..", ip, actionIdList);
        Assert.hasText(ip, "The ip is blank.");
        Assert.notEmpty(actionIdList, "The actionIdList is empty.");
        serverService.rebootAction(ip.trim(), actionIdList);
    }


    /**
     * 重启 action 通过服务坐标
     *
     * @param ip              为空，就是所有服务节点
     * @param actionPointList
     */
    @Override
    public void rebootByActionPoint(String ip, List<String> actionPointList) throws Exception {
        log.debug("RebootAction the action[IP={};{}] ..", ip, actionPointList);
        Assert.notEmpty(actionPointList, "The actionPointList is empty.");

        List<BootServerView> bootServerViewList = new ArrayList<>();
        if (StringUtils.isNoneBlank(ip)) {
            bootServerViewList.add(actionStatusService.getServerStatus(ip));
        } else {
            bootServerViewList = actionStatusService.getServerStatus();
        }

        //筛选 传入坐标列表的 服务Id
        List<String> actionIdList = new ArrayList<>();
        for (BootServerView bootServerView : bootServerViewList) {
            if (bootServerView != null) {
                bootServerView.getWorkers().stream().filter(x -> actionPointList.contains(x.getAction().getFullName())).forEach(x -> actionIdList.add(x.getAid()));
            }
            if (!actionIdList.isEmpty()) {
                serverService.rebootAction(bootServerView.getIp(), actionIdList);
            } else {
                log.warn("The ip={} actionPointList={} not online.", bootServerView.getIp(), actionIdList);
            }
        }
    }


    /**
     * 获取所有在线 agent 信息 (仅在线状态)
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<BootServerView> getOnlineServerStatus() throws Exception {
        return actionStatusService.getServerStatus();
    }

    /**
     * 获取指定 agent 信息
     *
     * @return
     * @throws Exception
     */
    @Override
    public BootServerView getOnlineServerStatus(String ip) throws Exception {
        return actionStatusService.getServerStatus(ip);
    }
}
