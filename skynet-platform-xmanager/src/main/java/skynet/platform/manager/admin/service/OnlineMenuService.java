package skynet.platform.manager.admin.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntMenuView;
import skynet.platform.feign.model.MenuView;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OnlineMenuService implements AutoCloseable {

    private final IAntConfigService antConfigService;
    private final List<String> reportedMenuPathList = new ArrayList<>();

    public OnlineMenuService(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    private String getOnlinePath() {
        return antConfigService.getSkynetOnlinePath() + "/menu";
    }

    public List<MenuView> getMenusList() {
        List<AntMenuView> antMenuViewList = antConfigService.getMenusList();
        return JSON.parseObject(JSON.toJSONString(antMenuViewList), new TypeReference<List<MenuView>>() {
        });
    }

    public String report(MenuView menuView) {

        AntMenuView antMenuView = JSON.parseObject(menuView.toJson(), AntMenuView.class);
        String path = antConfigService.reportMenu(antMenuView);
        reportedMenuPathList.add(path);
        return path;
    }


    @Override
    public void close() throws Exception {
        if (antConfigService != null) {
            for (String path : reportedMenuPathList) {
                antConfigService.delData(path);
            }
        }
    }
}
