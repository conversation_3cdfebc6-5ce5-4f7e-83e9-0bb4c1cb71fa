package skynet.platform.manager.admin.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.boot.AppContext;
import skynet.boot.common.utils.MD5Util;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.domain.RepoFileDo;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributeView;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Slf4j
@Service
public class RepoService {
    public static final ThreadLocal<DateFormat> DATETIME_FMT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static final String DEFAULT_DATETIME = "1970-01-01 00:00:00";
    private static final String MD5_SUFFIX = ".md5";

    private final AppContext appContext;
    private final ManagerProperties managerProperties;

    public RepoService(AppContext appContext, ManagerProperties managerProperties) {
        this.appContext = appContext;
        this.managerProperties = managerProperties;
    }

    // region 路径相关
    // 如：/iflytek/server/skynet/repo
    public String getRepoPath() {
        return StringUtils.isBlank(managerProperties.getRepoPath()) ?
                String.format("%s/repo", appContext.getSkynetHome()) : managerProperties.getRepoPath();
    }

    public String getPluginRepoPath(String plugin) {
        return Paths.get(getRepoPath(), plugin).toString();
    }

    //归档文件夹 root 路径：
    //如： /iflytek/server/skynet/repo/ant-mon/_archived
    public String getArchiveRepoPath(String plugin) {
        return Paths.get(getPluginRepoPath(plugin), "_archived").toString();
    }

    // endregion

    // region 文件操作
    public void inputFile(String plugin, String filePath, InputStream in) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        String absPath = Paths.get(getPluginRepoPath(plugin), filePath).toString();
        log.info("input={}", absPath);
        writeFile(absPath, in);
        generateMd5sum(absPath);
    }

    public void outputFile(String plugin, String filePath, boolean isArchived, OutputStream os) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        String absPath = Paths.get(isArchived ? getArchiveRepoPath(plugin) : getPluginRepoPath(plugin), filePath).toString();
        readFile(absPath, os);
    }

    public void outputFile(String plugin, String filePath, final ZipOutputStream zos, String zipPath) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        zos.putNextEntry(new ZipEntry(zipPath));
        String absPath = Paths.get(getPluginRepoPath(plugin), filePath).toString();
        readFile(absPath, zos);
        zos.closeEntry();
    }

    public void outputFile(String plugin, String filePath, boolean isArchived, long offset, int length, OutputStream os) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        String absPath = Paths.get(isArchived ? getArchiveRepoPath(plugin) : getPluginRepoPath(plugin), filePath).toString();
        try (RandomAccessFile raf = new RandomAccessFile(absPath, "r")) {
            raf.seek(offset);
            byte[] buf = new byte[4096];
            int remain = length;
            int readOnce;
            while (remain > 0 && (readOnce = raf.read(buf, 0, Math.min(remain, buf.length))) != -1) {
                os.write(buf, 0, readOnce);
                remain -= readOnce;
            }
        }
    }

    public void archiveFile(String plugin, String filePath) {
        Assert.hasText(plugin, "the plugin is blank.");
        log.info("archiveFile={}/{}", plugin, filePath);
        moveFileWithMd5(Paths.get(getPluginRepoPath(plugin), filePath).toString(),
                Paths.get(getArchiveRepoPath(plugin), filePath).toString());
    }

    public void restoreFile(String plugin, String filePath) {
        Assert.hasText(plugin, "the plugin is blank.");
        log.info("restoreFile={}/{}", plugin, filePath);
        moveFileWithMd5(Paths.get(getArchiveRepoPath(plugin), filePath).toString(),
                Paths.get(getPluginRepoPath(plugin), filePath).toString());
    }

    public void deleteFile(String plugin, String filePath, boolean isArchived) {
        Assert.hasText(plugin, "the plugin is blank.");
        String basePath = isArchived ? getArchiveRepoPath(plugin) : getPluginRepoPath(plugin);
        deleteFileWithMd5(Paths.get(basePath, filePath).toString());
    }
    // endregion

    // region 文件信息与列表
    public RepoFileDo getFileInfo(String plugin, String filePath, boolean isArchived) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        Assert.hasText(filePath, "the filePath is blank.");
        log.debug("getFileInfo plugin={}, filePath={}, isArchived={}", plugin, filePath, isArchived);
        String pluginDirPath = isArchived ? getArchiveRepoPath(plugin) : getPluginRepoPath(plugin);
        String absPath = Paths.get(pluginDirPath, filePath).toString();
        File f = new File(absPath);
        File md5file = new File(absPath + MD5_SUFFIX);
        if (!md5file.exists()) {
            generateMd5sum(absPath);
        }
        String md5sum = FileUtils.readFileToString(md5file, StandardCharsets.UTF_8);
        String relativePath = getRelativePath(absPath, pluginDirPath);
        FileTimeAttributes timeAttr = getFileTimeAttributes(absPath);
        RepoFileDo repoFileDo = new RepoFileDo();
        repoFileDo.setArchived(isArchived);
        repoFileDo.setFileName(f.getName());
        repoFileDo.setFilePath(relativePath);
        repoFileDo.setDirectory(f.isDirectory());
        repoFileDo.setCreateTime(timeAttr.createTime);
        repoFileDo.setLastUpdateTime(timeAttr.updateTime);
        repoFileDo.setFileSize(f.length());
        repoFileDo.setPlugin(plugin);
        repoFileDo.setMd5sum(md5sum);
        return repoFileDo;
    }

    public List<RepoFileDo> list(String plugin) throws IOException {
        return list(plugin, null);
    }

    public List<RepoFileDo> list(String plugin, String regex) throws IOException {
        Assert.hasText(plugin, "the plugin is blank.");
        Pattern pattern = StringUtils.isNotBlank(regex) ? Pattern.compile(regex) : null;
        String pluginDirPath = getPluginRepoPath(plugin);
        List<RepoFileDo> ret = getFiles(new File(pluginDirPath), plugin, pattern, false);
        String archiveDirPath = getArchiveRepoPath(plugin);
        ret.addAll(getFiles(new File(archiveDirPath), plugin, pattern, true));
        return sortFiles(ret);
    }

    private List<RepoFileDo> getFiles(File pluginDir, String plugin, Pattern pattern, boolean isArchived) throws IOException {
        List<RepoFileDo> ret = new ArrayList<>();
        if (!pluginDir.exists()) return ret;
        File[] files = pluginDir.listFiles();
        if (files != null) {
            for (File f : files) {
                if (f.isDirectory()) continue;
                //排除隐藏文件
                if (f.getName().startsWith(".")) continue;
                if (pattern != null && !pattern.matcher(f.getName()).matches()) continue;
                FileTimeAttributes timeAttr = getFileTimeAttributes(f.getAbsolutePath());
                String md5 = tryGetMD5(f.getAbsolutePath());
                RepoFileDo repoFileDo = new RepoFileDo();
                repoFileDo.setFileName(f.getName());
                repoFileDo.setFilePath(f.getName());
                repoFileDo.setDirectory(f.isDirectory());
                repoFileDo.setCreateTime(timeAttr.createTime);
                repoFileDo.setLastUpdateTime(timeAttr.updateTime);
                repoFileDo.setFileSize(f.length());
                repoFileDo.setPlugin(plugin);
                repoFileDo.setMd5sum(md5);
                repoFileDo.setArchived(isArchived);
                ret.add(repoFileDo);
            }
        }
        return sortFiles(ret);
    }
    // endregion

    // region 工具方法
    private static String getRelativePath(String absPath, String relativeParent) {
        if (!relativeParent.endsWith(File.separator)) relativeParent += File.separator;
        int idx = absPath.indexOf(relativeParent);
        return idx < 0 ? "" : absPath.substring(idx + relativeParent.length());
    }

    private static void generateMd5sum(String fileAbsPath) throws IOException {
        File file = new File(fileAbsPath);
        File md5file = new File(fileAbsPath + MD5_SUFFIX);
        String md5 = MD5Util.getFileMd5String(file);
        FileUtils.writeStringToFile(md5file, md5, StandardCharsets.UTF_8);
        log.info("{}:{}", md5file.getName(), md5);
    }

    private static FileTimeAttributes getFileTimeAttributes(String absPath) throws IOException {
        FileTimeAttributes ret = new FileTimeAttributes();
        Path path = Paths.get(absPath);
        BasicFileAttributeView basicview = Files.getFileAttributeView(path, BasicFileAttributeView.class, LinkOption.NOFOLLOW_LINKS);
        BasicFileAttributes attr = basicview.readAttributes();
        Date createTime = new Date(attr.creationTime().toMillis());
        Date updateTime = new Date(attr.lastModifiedTime().toMillis());
        ret.createTime = DATETIME_FMT.get().format(createTime);
        ret.updateTime = DATETIME_FMT.get().format(updateTime);
        return ret;
    }

    private static String tryGetMD5(String absPath) throws IOException {
        File md5file = new File(absPath + MD5_SUFFIX);
        return md5file.exists() ? FileUtils.readFileToString(md5file, StandardCharsets.UTF_8) : null;
    }

    private static void readFile(String absPath, OutputStream os) throws IOException {
        try (FileInputStream fis = FileUtils.openInputStream(new File(absPath))) {
            IOUtils.copyLarge(fis, os);
        }
    }

    private static void writeFile(String absPath, InputStream in) throws IOException {
        try (FileOutputStream fos = FileUtils.openOutputStream(new File(absPath))) {
            IOUtils.copyLarge(in, fos);
        }
    }

    private static void moveFileWithMd5(String src, String dest) {
        log.debug("moveFileWithMd5: {} -> {}", src, dest);
        File srcFile = new File(src);
        File destFile = new File(dest);
        if (srcFile.exists()) {
            try {
                log.info("{} => {}", src, dest);
                FileUtils.moveFile(srcFile, destFile);
            } catch (IOException e) {
                log.error("moveFileWithMd5: 移动文件失败: {} -> {}", srcFile, destFile, e);
            }
        }
        File srcMd5 = new File(src + MD5_SUFFIX);
        File destMd5 = new File(dest + MD5_SUFFIX);
        if (srcMd5.exists()) {
            try {
                log.info("{} => {}", src, dest);
                FileUtils.moveFile(srcMd5, destMd5);
            } catch (IOException e) {
                log.error("moveFileWithMd5: 移动MD5文件失败: {} -> {}", srcMd5, destMd5, e);
            }
        }
    }

    private static void deleteFileWithMd5(String absPath) {
        File file = new File(absPath);
        if (file.exists()) file.delete();
        File md5File = new File(absPath + MD5_SUFFIX);
        if (md5File.exists()) md5File.delete();
    }
    // endregion

    // region 排序
    private List<RepoFileDo> sortFiles(List<RepoFileDo> files) {
        TreeMap<String, RepoFileDo> sortMap = new TreeMap<>();
        List<RepoFileDo> defFiles = new ArrayList<>(1);
        for (RepoFileDo file : files) {
            if (file.getFileName().contains("zk.config")) {
                defFiles.add(file);
            } else {
                sortMap.put(file.getFileName(), file);
            }
        }
        List<RepoFileDo> ret = new ArrayList<>(defFiles);
        ret.addAll(sortMap.values());
        return ret;
    }
    // endregion

    // region 内部类
    private static class FileTimeAttributes {
        public String createTime = DEFAULT_DATETIME;
        public String updateTime = DEFAULT_DATETIME;
    }
    // endregion
}


/**
 * // 用来做非递归遍历的栈
 * Deque<File> stack = new ArrayDeque<>();
 * stack.push(pluginDir);
 * while (!stack.isEmpty()) {
 * File dir = stack.pop();
 * File[] files = dir.listFiles();
 * if (files == null) {
 * continue;
 * }
 * for (File f : files) {
 * if (f.isDirectory()) {
 * stack.push(f);
 * } else {
 * if (pattern != null) {
 * Matcher m = pattern.matcher(f.getName());
 * if (!m.matches()) {
 * continue;
 * }
 * }
 * RepoFileDo repoFileDo = new RepoFileDo();
 * String absPath = f.getAbsolutePath();
 * String relativePath = getRelativePath(absPath, pluginDirPath);
 * FileTimeAttributes timeAttr = getFileTimeAttributes(absPath);
 * String md5 = tryGetMD5(absPath);
 * repoFileDo.setFileName(f.getName());
 * repoFileDo.setFilePath(relativePath);
 * repoFileDo.setDirectory(f.isDirectory());
 * repoFileDo.setCreateTime(timeAttr.createTime);
 * repoFileDo.setLastUpdateTime(timeAttr.updateTime);
 * repoFileDo.setFileSize(f.length());
 * repoFileDo.setPlugin(plugin);
 * repoFileDo.setMd5sum(md5);
 * ret.add(repoFileDo);
 * }
 * }
 * }
 * return sortFiles(ret);
 */