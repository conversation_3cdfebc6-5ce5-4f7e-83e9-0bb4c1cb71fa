package skynet.platform.manager.admin.domain;

import lombok.Getter;
import skynet.platform.manager.exception.BadRequestException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/11/9 15:10
 */
@Getter
public class FileRange {
    private static final Pattern pattern = Pattern.compile("bytes=(.+)-(.+)");
    private final long offset;
    private final int length;

    public FileRange(long offset, int length) {
        this.offset = offset;
        this.length = length;
    }

    public static FileRange parse(String header) throws BadRequestException {
        FileRange range = null;
        Matcher matcher = pattern.matcher(header);
        if (matcher.matches() && matcher.groupCount() == 2) {
            String startString = matcher.group(1);
            String endString = matcher.group(2);
            try {
                long start = Long.parseLong(startString);
                long end = Long.parseLong(endString);
                int len = (int) (end - start + 1);
                range = new FileRange(start, len);
            } catch (Exception ignored) {

            }
        }
        if (range == null) {
            throw new BadRequestException();
        }
        return range;
    }

    @Override
    public String toString() {
        return String.format("bytes=%d-%d", this.offset, (this.offset + this.length - 1));
    }
}
