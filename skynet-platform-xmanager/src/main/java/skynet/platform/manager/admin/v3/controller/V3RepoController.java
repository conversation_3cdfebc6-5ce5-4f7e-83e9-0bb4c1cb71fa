package skynet.platform.manager.admin.v3.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.exception.PluginNotExistException;
import skynet.platform.feign.model.RepoFileDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Repo;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.domain.FileRange;
import skynet.platform.manager.admin.service.PluginZkTextService;
import skynet.platform.manager.admin.v3.service.V3RepoService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.exception.BadRequestException;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 资源仓库控制器
 *
 * <AUTHOR> 2018年9月26日 下午5:42:02
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3RepoController implements V3Repo {

    private final V3RepoService v3RepoService;
    private final ManagerProperties managerProperties;
    private final PluginZkTextService pluginZkTextService;

    public V3RepoController(V3RepoService v3RepoService, ManagerProperties managerProperties, PluginZkTextService pluginZkTextService) {
        this.v3RepoService = v3RepoService;
        this.managerProperties = managerProperties;
        this.pluginZkTextService = pluginZkTextService;
    }

    /**
     * 列出资源仓库目录下的文件
     *
     * @param plugin
     * @return
     */
    @Override
    public SkynetApiResponse<List<RepoFileDto>> list(@PathVariable String plugin,
                                                     @RequestParam(required = false, defaultValue = "") String regex) {
        log.debug("plugin={}, regex={}", plugin, regex);
        SkynetApiResponse<List<RepoFileDto>> resp;
        try {
            List<RepoFileDto> data = v3RepoService.list(plugin, regex);
            resp = SkynetApiResponse.success(data);
        } catch (Exception e) {
            log.error("list exception", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("list response={}", resp);
        return resp;
    }

    /**
     * 下载指定plugin关联的资源文件
     *
     * @param plugin
     * @param fileName 相对plugin资源目录的相对路径
     * @return
     * @throws IOException
     */
    @Override
    public void download(@PathVariable String plugin, @PathVariable String fileName,
                         @RequestHeader(required = false, name = "Range") String rangeHeader,
                         @RequestParam(required = false, defaultValue = "false") boolean isArchived,
                         HttpServletResponse resp) throws IOException {
        log.debug("plugin={}, fileName={}, isArchived={}, rangeHeader={}", plugin, fileName, isArchived, rangeHeader);

        Assert.hasText(plugin, "the plugin is blank.");
        Assert.hasText(fileName, "the fileName is blank.");

        FileRange range = null;
        if (!StringUtils.isBlank(rangeHeader)) {
            try {
                range = FileRange.parse(rangeHeader);
                log.debug("range={}", range);
            } catch (BadRequestException e) {
                log.error("parse range exception", e);
                resp.setStatus(HttpStatus.BAD_REQUEST.value());
                resp.flushBuffer();
                return;
            }
        }

        resp.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        String filename = Paths.get(fileName).getFileName().toString();
        String encodedFileName = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        resp.addHeader(HttpHeaders.CONTENT_DISPOSITION,
                String.format("attachment; filename=\"%s\"", encodedFileName));
        resp.addHeader("FileName", encodedFileName);

        try {
            RepoFileDto repoFileDto = v3RepoService.getFileInfo(plugin, fileName, isArchived);

            resp.addHeader(HttpHeaders.LAST_MODIFIED, repoFileDto.getLastUpdateTime());
            resp.addHeader("FileSize", String.valueOf(repoFileDto.getFileSize()));
            resp.addHeader("MD5", repoFileDto.getMd5sum());
            String contentLength = "" + ((range != null) ? range.getLength() : repoFileDto.getFileSize());
            resp.setHeader(HttpHeaders.CONTENT_LENGTH, contentLength);

            OutputStream out = resp.getOutputStream();
            if (range == null) {
                v3RepoService.outputFile(plugin, fileName, repoFileDto.isArchived(), out);
            } else {
                v3RepoService.outputFile(plugin, fileName, repoFileDto.isArchived(), range.getOffset(), range.getLength(), out);
            }
            resp.flushBuffer();
        } catch (FileNotFoundException e) {
            log.error("Download repo plugin:[{}]; file: [{}]. error. {}", plugin, fileName, e.getMessage());
            resp.sendError(500, e.getMessage());
        }
        log.debug("download response ok.");
    }

    /**
     * 上传资源文件
     *
     * @param plugin
     * @param files
     * @return
     */
    @Override
    @AuditLog(module = "服务关联文件管理", operation = "上传资源文件", message = "plugin=#{#plugin}")
    public SkynetApiResponse<Void> upload(@PathVariable String plugin, @RequestParam("files") MultipartFile[] files) {
        Assert.hasText(plugin, "the plugin is blank.");

        if (files == null || files.length == 0) {
            return SkynetApiResponse.success();
        }

        if (log.isDebugEnabled()) {
            List<MultipartFile> fileList = Arrays.asList(files);
            String fileNames = fileList.stream().map(MultipartFile::getOriginalFilename)
                    .collect(Collectors.joining(","));
            log.debug("plugin={},fileNames={}", plugin, fileNames);
        }

        SkynetApiResponse<Void> resp = SkynetApiResponse.success();

        for (MultipartFile file : files) {
            try {
                String filename = file.getOriginalFilename();
                log.info("[upload plugin:{}; file:{}.]", plugin, filename);
                this.v3RepoService.inputFile(plugin, filename, file.getInputStream());
            } catch (Exception e) {
                log.error("", e);
                resp = SkynetApiResponse.fail(e);
                break;
            }
        }
        log.debug("upload response={}", resp);
        return resp;
    }

    /**
     * 删除plugin关联资源仓库下的指定文件
     *
     * @param plugin
     * @param fileName 文件名称
     * @return
     */
    @Override
    @AuditLog(module = "服务关联文件管理", operation = "删除plugin关联资源仓库下的指定文件", message = "plugin=#{#plugin},fileName=#{#fileName}")
    public SkynetApiResponse<Void> delete(@PathVariable String plugin, @PathVariable String fileName,
                                          @RequestParam(required = false, defaultValue = "false") boolean isArchived) {
        Assert.hasText(plugin, "the plugin is blank.");
        Assert.hasText(fileName, "the fileName is blank.");
        log.debug("plugin={}, fileName={}", plugin, fileName);

        SkynetApiResponse<Void> resp = SkynetApiResponse.success();
        try {
            v3RepoService.deleteFile(plugin, fileName, isArchived);
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("delete response={}", resp);
        return resp;
    }

    /**
     * 删除plugin关联资源仓库下的指定文件
     *
     * @param plugin
     * @param fileNames 文件名称列表
     * @return
     */
    @Override
    @AuditLog(module = "服务关联文件管理", operation = "批量删除plugin关联资源仓库下的指定文件", message = "plugin=#{#plugin},fileNames=#{#fileNames}")
    public SkynetApiResponse<Void> batchDelete(@RequestParam(required = true) String plugin, @RequestBody List<String> fileNames) {
        Assert.hasText(plugin, "the plugin is blank.");
        Assert.isTrue(fileNames != null && !fileNames.isEmpty(), "the fileNames is blank.");
        log.debug("batchDelete : plugin={}, fileName={}", plugin, fileNames);

        SkynetApiResponse<Void> resp = SkynetApiResponse.success();
        for (String filename : fileNames) {
            try {
                v3RepoService.deleteFile(plugin, filename, false);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        log.debug("batchDelete response={}", resp);

        return resp;
    }

    /**
     * 将plugin关联的资源仓库目录打包下载
     *
     * @param plugin
     * @return
     * @throws IOException
     */
    @Override
    public void downloadZip(@PathVariable String plugin, HttpServletResponse resp) throws IOException, PluginNotExistException {
        Assert.hasText(plugin, "the plugin is blank.");
        log.info("Download zip, plugin = {}", plugin);

        // 设置header
        resp.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        String encodedFileName = URLEncoder.encode(String.format("%s.%s", plugin, managerProperties.isTarFormat() ? "tar" : "zip"), StandardCharsets.UTF_8);
        resp.setHeader(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s\"", encodedFileName));

        //把zk配置导出到对应的 repo 目录下
        String fullPath = getRepoFullPath(plugin);
        log.debug("export zk config to {}.", fullPath);
        String zkConfigText = pluginZkTextService.exportZkConfig(plugin);
        FileUtils.write(new File(fullPath, plugin + ".zk.config"), zkConfigText, StandardCharsets.UTF_8);

        List<RepoFileDto> repoFiles = v3RepoService.list(plugin);
        if (managerProperties.isTarFormat()) {
            //tar 格式
            try (TarArchiveOutputStream taos = new TarArchiveOutputStream(resp.getOutputStream())) {
                for (RepoFileDto repoFile : repoFiles) {
                    String path = Paths.get(v3RepoService.getRepoPath(), plugin, repoFile.getFilePath()).toString();
                    File off = new File(path);
                    if (off.isDirectory()) {
                        continue;
                    }
                    try (FileInputStream fis = new FileInputStream(off)) {
                        TarArchiveEntry tae = new TarArchiveEntry(off, repoFile.getFileName());
                        tae.setSize(off.length());
                        taos.putArchiveEntry(tae);
                        org.apache.commons.io.IOUtils.copyLarge(fis, taos);
                        taos.flush();
                        taos.closeArchiveEntry();
                    }
                }
                resp.flushBuffer();
            }
        } else {
            //zip 格式
            try (ZipOutputStream zos = new ZipOutputStream(resp.getOutputStream())) {
                zos.putNextEntry(new ZipEntry(plugin + "/"));
                zos.closeEntry();
                if (repoFiles != null) {
                    for (RepoFileDto repoFile : repoFiles) {
                        String zipFilePath = Paths.get(plugin, repoFile.getFilePath()).toString();
                        v3RepoService.outputFile(plugin, repoFile.getFilePath(), zos, zipFilePath);
                    }
                }
                resp.flushBuffer();
            }
        }
        log.info("[download plugin zip:{}]end...", plugin);
    }


    /**
     * 获取资源仓库路径
     *
     * @param plugin
     * @return
     */
    @Override
    public SkynetApiResponse<String> getPath(@RequestParam(name = "plugin", required = false) String plugin) {
        log.debug("getPath by plugin={}", plugin);
        SkynetApiResponse<String> resp;
        try {
            String fullPath = getRepoFullPath(plugin);
            resp = SkynetApiResponse.success(fullPath);
        } catch (Exception e) {
            log.error("getPath error", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("getPath response={}", resp);
        return resp;
    }

    private String getRepoFullPath(String plugin) {
        String path = v3RepoService.getRepoPath();
        return StringUtils.isBlank(plugin) ? path : String.format("%s/%s", path, plugin);
    }
}




