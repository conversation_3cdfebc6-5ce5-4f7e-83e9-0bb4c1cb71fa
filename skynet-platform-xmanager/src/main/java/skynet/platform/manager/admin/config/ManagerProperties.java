package skynet.platform.manager.admin.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class ManagerProperties {

    @Value("${skynet.fetch.server.status.timeout.seconds:8}")
    private int timeout = 8;

    @Value("${skynet.xmanager.ssh.timeout.seconds:8}")
    private int sshTimeout = 8;

    @Value("${skynet.xmanager.repo.path:}")
    private String repoPath;

    @Value("${skynet.xagent.server.port:6230}")
    private int agentServerPort;

    @Value("${skynet.xagent.k8s.port:32630}")
    private int agentK8sPort;

    @Value("${skynet.k8s.namespace:default}")
    private String k8sNamespace;

    @Value("${skynet.k8s.registry.context-path:skynet}")
    private String registryContextPath;

    //docker镜像推送方式，默认使用docker api, 或者 命令行
    @Value("${skynet.manifest.push.use-cmd:false}")
    private boolean manifestPushStyleWithCmd;

    @Value("${skynet.grafana.actionPoint:grafana-server-v6@ant-mon}")
    private String grafanaActionPoint;

    @Value("${skynet.prometheus.nodeExporter.actionPoint:node-exporter@ant-mon}")
    private String nodeExporterActionPoint;

    @Value("${skynet.xmanager.repo.download.tar.format:true}")
    private boolean tarFormat = true;

    @Value("${skynet.xmanager.repo.download.with.zkconfig:true}")
    private boolean withZkConfig = true;


}
