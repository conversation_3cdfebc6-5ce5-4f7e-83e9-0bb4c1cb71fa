package skynet.platform.manager.admin.v3.service;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.cli.Commandline;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.boot.AppContext;
import skynet.boot.exception.SkynetException;
import skynet.boot.security.SkynetEncryption;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.ExtConfigItem;
import skynet.platform.common.domain.UpdateParam;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.feign.model.ActionDefinitionDto;
import skynet.platform.feign.model.ActionDefinitionDto.ExtConfigItemDto;
import skynet.platform.feign.model.RepoFileDto;
import skynet.platform.feign.service.V3K8sAdapter;
import skynet.platform.manager.admin.v3.service.k8s.boot.BaseBoot;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class K8sDependResourceService {

    private final IAntConfigService antConfigService;
    private final AppContext appContext;
    private final SkynetEncryption skynetEncryption;
    private final V3RepoService v3RepoService;

    private final V3ActionDefinitionService v3ActionDefinitionService;

    public K8sDependResourceService(IAntConfigService antConfigService, AppContext appContext, SkynetEncryption skynetEncryption, V3RepoService v3RepoService, V3ActionDefinitionService v3ActionDefinitionService) {
        this.antConfigService = antConfigService;
        this.appContext = appContext;
        this.skynetEncryption = skynetEncryption;
        this.v3RepoService = v3RepoService;
        this.v3ActionDefinitionService = v3ActionDefinitionService;
    }

    public ActionDefinitionDto fetchDefinition(V3K8sAdapter.K8sDependResourceRequest k8sDependResourceRequest) throws Exception {
        BaseBoot baseBoot = getBaseBoot(k8sDependResourceRequest);
        return convert(baseBoot);
    }

    public V3K8sAdapter.K8sDependResourceResponse fetchResource(V3K8sAdapter.K8sDependResourceRequest k8sDependResourceRequest) throws Exception {

        V3K8sAdapter.K8sDependResourceResponse k8sDependResourceResponse = new V3K8sAdapter.K8sDependResourceResponse();
        k8sDependResourceResponse.setSkynetHome(appContext.getSkynetHome());
        //TODO: zone
        BaseBoot baseBoot = getBaseBoot(k8sDependResourceRequest);

        //1.属性定义的配置文件
        log.debug("build config props.");
        File appConfigFile = baseBoot.getAppConfigFile();
        V3K8sAdapter.DependConfigItem dependConfigItem = new V3K8sAdapter.DependConfigItem();
        dependConfigItem.setTargetDir(appConfigFile.getParent());
        dependConfigItem.setText(configProps(baseBoot.getAppEnvironment()));
        dependConfigItem.setFileName(appConfigFile.getName());
        k8sDependResourceResponse.getConfigItems().add(dependConfigItem);

        //2.扩展配置文件
        log.debug("build ext config file.");
        k8sDependResourceResponse.getConfigItems().addAll(buildExitConfigs(baseBoot.getAid(), baseBoot.getAntActionParam().getBootParam().getExtConfigItems(), baseBoot.getAppEnvironment()));

        //获取 文件 skynet/conf 配置下的所有文件
        k8sDependResourceResponse.getConfigItems().addAll(getSkynetConfig());

        //3.关联的依赖文件
        log.debug("get depend file info.");
        for (UpdateParam updateParam : baseBoot.getAntActionParam().getBootParam().getUpdateParams()) {
            File targetDir = new File(baseBoot.getAppEnvironment().replacePlaceholder(updateParam.getTargetDir()));
            V3K8sAdapter.DependFileItem item = buildDependFileItem(baseBoot.getAntActionParam().getPlugin(), updateParam);
            assert item != null;
            item.setTargetDir(targetDir.toString());
            item.setMode(updateParam.getMode());
            item.setOwner(updateParam.getOwner());
            k8sDependResourceResponse.getFileItems().add(item);
        }

        //4.获取启动脚本
        log.debug("build target script.");
        k8sDependResourceResponse.setTargetScript(composeCommand(baseBoot));

        //服务定义本身描述
        k8sDependResourceResponse.setDefinition(convert(baseBoot));
        return k8sDependResourceResponse;
    }

    /**
     * 配置文件
     *
     * @param bootEnvironment
     * @return
     */
    private String configProps(BootEnvironment bootEnvironment) {
        for (Map.Entry<String, Object> item : bootEnvironment.entrySet()) {
            if (item.getValue() != null) {
                if (item.getValue().toString().startsWith("{cipher}")) {
                    try {
                        item.setValue(skynetEncryption.decrypt(item.getValue().toString().replace("{cipher}", "")));
                    } catch (Exception e) {
                        log.error("Decrypt error:{}", e.getMessage());
                    }
                }
                item.setValue(convertToUnicode(item.getValue().toString()));
            }
        }
        return bootEnvironment.toPropLines();
    }

    // 将配置中的中文转换为 unicode 编码，防止中文乱码
    private static String convertToUnicode(String line) {
        StringBuilder unicodeLine = new StringBuilder();
        for (char c : line.toCharArray()) {
            if (c > 127) {
                unicodeLine.append("\\u").append(String.format("%04x", (int) c));
            } else {
                unicodeLine.append(c);
            }
        }
        return unicodeLine.toString();
    }

    private List<V3K8sAdapter.DependConfigItem> getSkynetConfig() throws Exception {

        log.debug("getSkynetConfig begin ..");
        List<V3K8sAdapter.DependConfigItem> objList = new ArrayList<>();
        File configDir = new File(appContext.getSkynetHome(), "conf");
        File[] files = configDir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    String text = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
                    V3K8sAdapter.DependConfigItem dependConfigItem = new V3K8sAdapter.DependConfigItem();
                    dependConfigItem.setEncoding(StandardCharsets.UTF_8.toString());
                    dependConfigItem.setFileName(file.getName());
                    dependConfigItem.setTargetDir(file.getParent());
                    dependConfigItem.setText(text);
                    objList.add(dependConfigItem);
                }
            }
        }
        log.debug("getSkynetConfig end ..");
        return objList;
    }

    private List<V3K8sAdapter.DependConfigItem> buildExitConfigs(String aid, List<ExtConfigItem> extConfigItems, BootEnvironment bootEnvironment) throws Exception {

        List<V3K8sAdapter.DependConfigItem> objList = new ArrayList<>(extConfigItems.size());
        log.debug("Create ExtConfig Local File [extConfigItems.size={}] begin ..", extConfigItems.size());
        for (ExtConfigItem extConfigItem : extConfigItems) {
            log.debug("Local ExtConfig File={} ..", extConfigItem.getTargetFile());
            try {
                File targetFile = new File(bootEnvironment.replacePlaceholder(extConfigItem.getTargetFile()));
                //没有配置目录，默认放置到 skynet home 的 tmp 当前aid的 目录中
                if (StringUtils.isBlank(targetFile.getParent())) {
                    targetFile = new File(String.format("%s/tmp/%s", appContext.getSkynetHome(), aid), targetFile.toString());
                }
                String text = bootEnvironment.replacePlaceholder(extConfigItem.getText());
                String encoding = StringUtils.isBlank(extConfigItem.getEncoding()) ? StandardCharsets.UTF_8.toString() : extConfigItem.getEncoding().trim();
                log.info("Local ExtConfig File={} encoding={}", targetFile, encoding);
                log.trace("Local ExtConfig File={} encoding={} text:{} {}", targetFile, encoding, System.lineSeparator(), text);

                V3K8sAdapter.DependConfigItem dependConfigItem = new V3K8sAdapter.DependConfigItem();
                dependConfigItem.setEncoding(encoding);
                dependConfigItem.setFileName(targetFile.getName());
                dependConfigItem.setTargetDir(targetFile.getParent());
                dependConfigItem.setText(text);
                dependConfigItem.setOwner(extConfigItem.getOwner());
                dependConfigItem.setMode(extConfigItem.getMode());
                objList.add(dependConfigItem);
            } catch (Exception e) {
                log.error("Create local config file error. extConfigItem={} Error:\t{}", extConfigItem, e.getMessage());
            }
        }
        log.debug("Create ExtConfig Local File [extConfigItems.size={}] end.", extConfigItems.size());
        return objList;
    }

    private String composeCommand(BaseBoot baseBoot) throws Exception {

        StringBuilder cmdLines = new StringBuilder();

        for (Map.Entry<String, String> item : baseBoot.getWorkEnvs().entrySet()) {
            cmdLines.append(String.format("export %s=%s%s", item.getKey(), item.getValue(), System.lineSeparator()));
        }

        // 构建命令行 // 执行命令行
        Commandline commandline = baseBoot.getCommand();
        cmdLines.append(commandline.toString().replace("/bin/sh -c", ""));
        return cmdLines.toString();
    }

    /**
     * 解析资源文件的uri，负责转换私有协议
     *
     * @param pluginCode
     * @param updateParam
     * @return
     * @throws URISyntaxException
     */
    private V3K8sAdapter.DependFileItem buildDependFileItem(String pluginCode, UpdateParam updateParam) throws URISyntaxException, IOException {

        String uriString = updateParam.getFileUrl();
        if (StringUtils.isBlank(uriString) || StringUtils.isBlank(uriString.replace("skynet:", "")) || StringUtils.isBlank(uriString.replace("skynet:null", ""))) {
            log.warn("The uriString invalidity。{}", uriString);
            return null;
        }
        V3K8sAdapter.DependFileItem item = new V3K8sAdapter.DependFileItem();

        URI uri = new URI(uriString);
        item.setUrl(uri.toString());

        String scheme = uri.getScheme();
        if ("skynet".equals(scheme)) {
            // 将skynet私有协议地址转换为http地址
            String fileRelativePath = uri.getSchemeSpecificPart();
            Assert.hasText(fileRelativePath, "invalid uri: " + uriString);
            try {
                RepoFileDto repoFileDto = v3RepoService.getFileInfo(pluginCode, fileRelativePath,false);
                item.setFileName(repoFileDto.getFileName());
                item.setTargetDir(updateParam.getTargetDir());
                item.setFileSize(repoFileDto.getFileSize());
                item.setMd5sum(repoFileDto.getMd5sum());
                item.setLastUpdateTime(repoFileDto.getLastUpdateTime());
                item.setUrl(String.format("%s/repo/download?plugin=%s&fileName=%s", appContext.getUri(), pluginCode, fileRelativePath));
            } catch (IOException ioException) {
                log.error(ioException.getMessage(), ioException);
                throw new SkynetException(-1, "getDependFile error=%s", ioException.getMessage());
            }
        } else {
            if (scheme == null || !scheme.toLowerCase().startsWith("http")) {
                throw new RuntimeException(String.format("Invalid uri:%s,Pleas Set skynet:|http:|https Scheme.", uriString));
            }
        }
        return item;
    }


    private BaseBoot getBaseBoot(V3K8sAdapter.K8sDependResourceRequest k8sDependResourceRequest) throws Exception {
        ActionNameContract actionNameContract = new ActionNameContract(k8sDependResourceRequest.getActionPoint());
        AntActionParam antActionParam = this.antConfigService.getActionParam(actionNameContract.getPluginCode(), actionNameContract.getActionCode());

        // 根据 getBootType 获取 baseBoot
        String beanName = String.format("boot.%s", antActionParam.getBootType()).toLowerCase();
        BaseBoot baseBoot = this.appContext.getSpringContext().getBean(beanName, BaseBoot.class);
        baseBoot.init(antActionParam, k8sDependResourceRequest);
        return baseBoot;
    }

    private ActionDefinitionDto convert(BaseBoot baseBoot) throws IOException, IllegalAccessException {
        ActionDefinitionDto actionDefinitionDto = v3ActionDefinitionService.get(baseBoot.getAntActionParam().getActionPoint());
        // 优先替换 ExtConfig，避免出现 JSON 格式破坏（ 比如如果有 xx = "yy" 这样的属性，引号会导致 JSON 格式破坏 ）
        actionDefinitionDto.setProperties(baseBoot.getAppEnvironment().replacePlaceholder(actionDefinitionDto.getProperties()));
        if (actionDefinitionDto.getExtConfigItems() != null) {
            for (ExtConfigItemDto item : actionDefinitionDto.getExtConfigItems()) {
                item.setText(baseBoot.getAppEnvironment().replacePlaceholder(item.getText()));
            }
        }
        // 全局占位符号替换
        String dtoJson = baseBoot.getAppEnvironment().replacePlaceholder(actionDefinitionDto.toString());
        return JSON.parseObject(dtoJson, ActionDefinitionDto.class);
    }
}
