package skynet.platform.manager.admin.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * 系统参数设置
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BootSysView extends Jsonable {

    @JSONField(ordinal = 20)
    private String host;

    @JSONField(ordinal = 30)
    private String os;

    @JSONField(ordinal = 40)
    private int cpu;

    @JSONField(ordinal = 50)
    private int mem;

    @JSONField(ordinal = 55)
    private String gpu;

    @JSONField(ordinal = 60)
    private String desc;

    @Override
    public String toString() {
        return super.toString();
    }
}
