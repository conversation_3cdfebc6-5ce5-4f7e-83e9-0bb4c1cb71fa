package skynet.platform.manager.admin.v3.service;

import skynet.platform.feign.model.RepoFileDto;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.zip.ZipOutputStream;

/**
 * 资源仓库服务
 *
 * <AUTHOR> 2018年9月26日 下午5:44:37
 */
public interface V3RepoService {

    /**
     * 从资源仓库目录输出文件到指定输出流
     *
     * @param plugin   插件名
     * @param filePath 相对路径
     * @param os       输出流
     * @throws IOException
     */
    void outputFile(String plugin, String filePath, boolean isArchived, OutputStream os) throws IOException;

    void outputFile(String plugin, String filePath, boolean isArchived, long offset, int length, OutputStream os) throws IOException;

    /**
     * 从资源仓库目录输出文件到指定的压缩输出流
     *
     * @param plugin   插件名
     * @param filePath 文件相对路径
     * @param os       压缩输出流
     * @param zipPath  文件在压缩文件中的路径
     * @throws IOException
     */
    void outputFile(String plugin, String filePath, ZipOutputStream os, String zipPath) throws IOException;

    /**
     * 从指定输入流中输入文件到资源仓库目录
     *
     * @param plugin   插件名
     * @param filePath 文件相对路径
     * @param in       输入流
     * @throws IOException
     */
    void inputFile(String plugin, String filePath, InputStream in) throws IOException;

    /**
     * 列出服务相关资源文件
     *
     * @param plugin 插件名
     * @return
     * @throws IOException
     */
    List<RepoFileDto> list(String plugin) throws IOException;

    /**
     * 列出服务相关资源文件
     *
     * @param plugin 插件名
     * @param regex  正则表达式
     * @return
     * @throws IOException
     */
    List<RepoFileDto> list(String plugin, String regex) throws IOException;

    /**
     * 删除指定服务的指定资源文件
     *
     * @param plugin   插件名
     * @param filePath 文件相对路径
     */
    void deleteFile(String plugin, String filePath, boolean isArchived);

    String getRepoPath();

    RepoFileDto getFileInfo(String plugin, String filePath, boolean isArchived) throws IOException;
}
