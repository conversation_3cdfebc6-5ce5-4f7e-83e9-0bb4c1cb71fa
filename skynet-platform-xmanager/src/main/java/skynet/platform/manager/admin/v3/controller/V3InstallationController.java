package skynet.platform.manager.admin.v3.controller;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Installation;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.exception.AgentConnectionException;

import java.util.List;

/**
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3InstallationController implements V3Installation {

    private final V3AgentService v3AgentService;

    public V3InstallationController(V3AgentService v3AgentService) {
        this.v3AgentService = v3AgentService;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "在指定服务器上安装Agent服务", message = "IP=#{#ip},dockerEnabled=#{#dockerEnabled},isForce=#{#isForce}")
    public SkynetApiResponse<Void> installAgent(@PathVariable String ip,
                                                @Parameter(description = "是否部署docker") @RequestParam(required = false, defaultValue = "false") boolean dockerEnabled,
                                                @Parameter(description = "是否强制部署") @RequestParam(required = false, defaultValue = "false") boolean isForce) {
        log.debug("installAgent ip={}", ip);
        SkynetApiResponse<Void> resp;
        try {
            Assert.hasText(ip, "ip is blank");
            String ret = v3AgentService.deployAgent(ip, dockerEnabled, isForce);
            resp = SkynetApiResponse.success(ret, null);
        } catch (Exception e) {
            log.error("installAgent err", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("installAgent response = {}", resp);
        return resp;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "在指定一批服务器上安装Agent", message = "IP List=#{#ipList},dockerEnabled=#{#dockerEnabled},isForce=#{#isForce}")
    public SkynetApiResponse<Void> installAgent(@Parameter(description = "是否部署docker") @RequestParam("dockerEnabled") boolean dockerEnabled,
                                                @Parameter(description = "是否强制部署") @RequestParam("isForce") boolean isForce,
                                                @Parameter(description = "ip 地址列表") @RequestBody List<String> ipList) {
        SkynetApiResponse<Void> resp;
        try {
            for (String ip : ipList) {
                v3AgentService.deployAgent(ip, dockerEnabled, isForce);
            }
            resp = SkynetApiResponse.success(null);
        } catch (Exception e) {
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("installAgent response={}", resp);
        return resp;
    }

    @Override
    public SkynetApiResponse<Void> connectTest(@RequestBody AgentDto dto) {
        SkynetApiResponse<Void> resp;
        try {
            v3AgentService.testAgentConnection(dto);
            resp = SkynetApiResponse.success(null);
        } catch (AgentConnectionException agentConnectEx) {
            resp = SkynetApiResponse.fail(ApiRequestErrorCode.AGENT_SSH_ERROR);
        } catch (Exception e) {
            resp = SkynetApiResponse.fail(e);
        }

        log.debug("ConnectTest response={}", resp);
        return resp;
    }
}
