package skynet.platform.manager.admin.service;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.AppContext;
import skynet.boot.common.concurrent.ParallelService;
import skynet.boot.security.auth.AuthUtils;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.domain.*;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.common.shell.Shell;
import skynet.platform.feign.model.AgentType;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.domain.BootServerView;
import skynet.platform.manager.admin.domain.BootWorkerView;
import skynet.platform.manager.exception.AgentConnectionException;
import skynet.platform.manager.exception.AgentNotRegisterException;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 服务器服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ServerService extends BaseService {

    public static final String AGENT_ACTION_POINT = AppBootEnvironment.AGENT_ACTION_POINT;

    private final ManagerProperties managerProperties;
    private final IAntConfigService antConfigService;
    private final RestTemplate authRestTemplate;
    private final AppContext appContext;
    private final ManagerEncryptor managerEncryptor;

    /**
     * 构造函数
     *
     * @param managerProperties
     * @param antConfigService
     * @param authRestTemplate
     * @param appContext
     * @param managerEncryptor
     */
    public ServerService(ManagerProperties managerProperties, IAntConfigService antConfigService,
                         @Qualifier("authRestTemplate") RestTemplate authRestTemplate,
                         AppContext appContext, ManagerEncryptor managerEncryptor) {
        super(managerProperties, antConfigService);
        this.managerProperties = managerProperties;
        this.antConfigService = antConfigService;
        this.authRestTemplate = authRestTemplate;
        this.appContext = appContext;
        this.managerEncryptor = managerEncryptor;
    }

    public void startServer() throws Exception {
        List<String> ipList = this.antConfigService.getServers().stream().map(AntServerParam::getIp).toList();
        startServerByIpList(ipList);
    }

    /**
     * 启动服务器
     *
     * @param ipList
     * @throws Exception
     */
    public void startServerByIpList(List<String> ipList) throws Exception {
        Assert.isTrue(ipList != null && !ipList.isEmpty(), "[start server] the ipList is empty");
        log.info("start servers，ips={}. begin..", ipList);
        List<ServerLoginParam> serverList = new ArrayList<>();
        for (String ip : ipList) {
            ServerLoginParam serverLoginParam = getServerLoginParam(ip);
            serverList.add(serverLoginParam);
        }
        this.startServer(serverList);
        log.info("start servers，ips={}. end.", ipList);
    }


    /**
     * 启动server服务器
     *
     * <pre>
     * 注： serverLoginParam中的 密码是明文
     * </pre>
     *
     * @param serverLoginParamList
     * @throws Exception
     */
    public void startServer(List<ServerLoginParam> serverLoginParamList) throws Exception {
        log.info("startServers servers size={}. begin..", serverLoginParamList.size());
        // 通过RpcClient 直接连接 到在线的Server 获取 Server 和Worker的状态
        if (serverLoginParamList.size() == 1) {
            log.debug("startServer serverList.size() == 1;ip={}", serverLoginParamList.getFirst());
            startServer(serverLoginParamList.getFirst());
            log.debug("startServer serverList.size() == 1;ip={}.end", serverLoginParamList.getFirst());
        } else {
            int nThreads = Math.min(16, serverLoginParamList.size());
            log.debug("Parallel start server. threads={}. begin..", nThreads);
            try (ParallelService<ServerLoginParam, Boolean> parallelService = new ParallelService<>(nThreads)) {
                parallelService.submit(serverLoginParamList, this::startServer);
                parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
                log.debug("Parallel start server. threads={}. end..", nThreads);
            }
        }
        log.info("StartServer size={}. end.", serverLoginParamList.size());
    }

    private boolean startServer(ServerLoginParam serverLoginParam) throws Exception {

        log.info("startServer ip={}. begin..", serverLoginParam.getIp());
        Map<String, AntActionStatus> nodes = this.antConfigService.getOnlineActionNodeStatus(AGENT_ACTION_POINT, null);
        List<String> onlineIpList = nodes.values().stream().map(AntActionStatus::getIp).toList();
        if (onlineIpList.contains(serverLoginParam.getIp())) {
            log.info("the server [{}] is online", serverLoginParam.getIp());
            return true;
        }

        log.info("Remote start the server={}:{} user={}", serverLoginParam.getIp(), serverLoginParam.getPort(), serverLoginParam.getUser());

        try (Shell shell = Shell.build(serverLoginParam.getIp(), serverLoginParam.getPort(), serverLoginParam.getUser(), serverLoginParam.getPwd(), serverLoginParam.getTimeout())) {
            log.info("Start begin:[{}]", serverLoginParam.getIp());
            String[] shellCmd = getCommands("daemon", serverLoginParam.getIp());
            log.info(JSON.toJSONString(shellCmd));
            String result = shell.execCmd(shellCmd);
            log.info("Exec result:[{}]", result);
            return true;
        } catch (SocketTimeoutException e) {
            log.error("Start server error." + e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Start server error." + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取命令，根据系统判断
     *
     * @param opt 操作
     * @param ip  ip
     * @return 结果
     */
    private String[] getCommands(String opt, String ip) {
        return new String[]{String.format("cd %s/bin/", appContext.getSkynetHome()), String.format("./ant-xagent.sh %s %s %d", opt, ip, managerProperties.getAgentServerPort())};
    }

    private String[] getStopCommand() {
        //绝杀
        return new String[]{String.format("cd %s/bin/", appContext.getSkynetHome()),
                "./ant-xagent.sh stop", "sleep 3",
                String.format("jcmd | grep server.port=%d |awk '{printf $1}'|cut -d/ -f1 | xargs kill -9", managerProperties.getAgentServerPort())};
    }

    public void stopServer(boolean stopAction) throws Exception {
        List<String> ipList = this.antConfigService.getServers().stream().map(AntServerParam::getIp).toList();
        stopServerByIpList(ipList, stopAction);
    }

    /**
     * 停止服务器
     *
     * @param ipList
     * @throws Exception
     */
    public void stopServerByIpList(List<String> ipList, boolean stopAction) throws Exception {
        Assert.isTrue(ipList != null && !ipList.isEmpty(), "[stop server] the ipList is empty");

        log.debug("Stop the server according to the node information. The node array is ={}", ipList);

        List<ServerLoginParam> serverLoginParamList = new ArrayList<>();
        for (String ip : ipList) {
            ServerLoginParam serverLoginParam = this.getServerLoginParam(ip);
            if (serverLoginParam == null) {
                throw new AntException(String.format("server [%s] does not register", ip));
            }
            serverLoginParamList.add(serverLoginParam);
        }
        stopServer(serverLoginParamList, stopAction);
    }

    /**
     * 停止服务器
     *
     * @param serverLoginParamList 停止服务器
     * @throws Exception 错误
     */
    public void stopServer(List<ServerLoginParam> serverLoginParamList, boolean stopAction) throws Exception {
        if (serverLoginParamList.size() == 1) {
            log.debug("StopServer serverList.size() == 1;ip={}", serverLoginParamList.getFirst());
            StopServer(serverLoginParamList.getFirst(), stopAction);
            log.debug("StopServer serverList.size() == 1;ip={}.end", serverLoginParamList.getFirst());
        } else {
            int nThreads = Math.min(16, serverLoginParamList.size());
            log.debug("Parallel stop server. threads={}. begin..", nThreads);
            try (ParallelService<ServerLoginParam, Boolean> parallelService = new ParallelService<>()) {
                parallelService.submit(serverLoginParamList, input -> StopServer(input, stopAction));
                parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
                log.debug("Parallel stop server. threads={}. end..", nThreads);
            }
        }
    }


    private Boolean StopServer(ServerLoginParam serverLoginParam, boolean stopAction) {
        String httpUri = String.format("http://%s:%d/skynet/agent/ctrl/%s?stopAction=%s", serverLoginParam.getIp(), managerProperties.getAgentServerPort(),
                AntServerControlType.STOP_SERVER, stopAction).replaceAll("\"", "");
        try {
            String response = authRestTemplate.postForObject(httpUri, null, String.class);
            log.debug("Stop the server[{}] response={}", serverLoginParam.getIp(), response);
            return true;
        } catch (HttpClientErrorException e) {
            assert e.getResponseHeaders() != null;
            log.warn("{}={};Error={}", AuthUtils.SKYNET_AUTH_ERR_MESSAGE, e.getResponseHeaders().getFirst(AuthUtils.SKYNET_AUTH_ERR_MESSAGE), e.getMessage());
            //如果是 鉴权错误，直接通过SSH停止
            if (e.getStatusCode().value() == HttpStatus.FORBIDDEN.value()) {
                stopServerBySsh(serverLoginParam);
            }
        } catch (Exception e) {
            log.error(String.format("Stop server [ip:%s] vim [%s] error:%s", serverLoginParam.getIp(), httpUri, e.getMessage()));
        }
        return false;
    }

    private boolean stopServerBySsh(ServerLoginParam serverLoginParam) {
        try (Shell shell = Shell.build(serverLoginParam.getIp(), serverLoginParam.getPort(), serverLoginParam.getUser(),
                serverLoginParam.getPwd(), serverLoginParam.getTimeout())) {
            log.debug("StopServerBySsh ...");
            shell.execCmd(getStopCommand());
            log.debug("StopServerBySsh OK.");
            return true;
        } catch (Exception e) {
            log.error("StopServerBySsh error." + e.getMessage());
            throw new AgentConnectionException(serverLoginParam.getIp(), serverLoginParam.getUser());
        }
    }

    /**
     * 重启action
     *
     * @param actionIdList ActionId 列表
     * @throws Exception 错误
     */
    public void rebootAction(String ip, List<String> actionIdList) throws Exception {
        Assert.hasText(ip, "The ip is blank.");
        Assert.isTrue(actionIdList != null && !actionIdList.isEmpty(), "The actionIdList is blank.");

        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AntException(String.format("server [%s] does not register", ip));
        }
        int agentPort = AgentType.KUBERNETES.equals(antServerParam.getType()) ? managerProperties.getAgentK8sPort() : managerProperties.getAgentServerPort();

        log.info("RebootAction the action[IP={}]actionIdList={} ..", ip, actionIdList);
        String httpUri = String.format("http://%s:%d/skynet/agent/ctrl/%s", ip.trim(), agentPort,
                AntServerControlType.REBOOT_WORKER).replaceAll("\"", "");
        try {
            String response = authRestTemplate.postForObject(httpUri, actionIdList, String.class);
            log.debug("RebootAction the action[IP={};{}] response={}", ip, actionIdList, response);
        } catch (Exception e) {
            log.error("RebootAction the action[IP={};{}] error={}", ip, actionIdList, e.getMessage());
        }
    }

    /**
     * 判断服务器是否连接成功
     * <p>
     * 密码是明码
     *
     * @param serverLoginParam 服务器登录信息
     * @return 返回连接结果
     * @throws Exception
     */
    public boolean testConnectServer(ServerLoginParam serverLoginParam) throws AgentConnectionException {
        String pwd = serverLoginParam.getPwd();
        if (StringUtils.isBlank(pwd)) {
            //如果没有传密码，则使用原密码
            AntServerParam antServerParam = this.antConfigService.getServerParam(serverLoginParam.getIp());
            String originPwd = antServerParam.getSsh().getPwd();
            pwd = (StringUtils.isBlank(originPwd)) ? "" : managerEncryptor.decrypt(originPwd);
        }

        log.info("start the connection test:[{}]", serverLoginParam.getIp());
        try (Shell shell = Shell.build(serverLoginParam.getIp(), serverLoginParam.getPort(), serverLoginParam.getUser(),
                pwd, serverLoginParam.getTimeout())) {
            log.info("connect successfully");
            return true;
        } catch (Exception e) {
            log.error("connect server error." + e.getMessage());
            throw new AgentConnectionException(serverLoginParam.getIp(), serverLoginParam.getUser());
        }
    }

    /**
     * 获取所有服务器
     *
     * @return
     */
    public List<BootServerView> getAllServer() {

        // 获取所有服务器节点信息
        List<AntServerParam> serverList = this.antConfigService.getServers();
        List<BootServerView> serverViewList = new ArrayList<>();

        for (AntServerParam antServerParam : serverList) {
            BootServerView bootServerView = getServer(antServerParam);
            serverViewList.add(bootServerView);
        }
        return serverViewList;
    }

    /**
     * 获取指定服务器
     *
     * @return
     */
    public BootServerView getServer(String ip) {

        // 获取指定服务器节点信息
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        return getServer(antServerParam);
    }

    /**
     * 获取指定服务器
     *
     * @return
     */
    private BootServerView getServer(AntServerParam antServerParam) {

        // 获取两者共有属性
        BootServerView bootServerView = JSON.parseObject(JSON.toJSONString(antServerParam), BootServerView.class);
        bootServerView.setIndex(antServerParam.getIndex());
        bootServerView.setPort(0);
        if (org.springframework.util.StringUtils.hasText(antServerParam.getType())) {
            bootServerView.setAgentType(antServerParam.getType());
        }

        int index = 0;
        bootServerView.getWorkers().clear();
        for (AntActionRegist antActionRegist : antServerParam.getActions()) {
            for (BootAction bootAction : antActionRegist.toBootActionList(antServerParam.getIp())) {
                BootWorkerView bootWorkerView = new BootWorkerView();
                bootWorkerView.setIndex(index++);
                bootWorkerView.setAid(bootAction.getAid());
                bootWorkerView.setAction(bootAction);
                bootServerView.getWorkers().add(bootWorkerView);
            }
        }
        return bootServerView;
    }

    /**
     * @param ip
     * @return 密码 是明码
     */
    public ServerLoginParam getServerLoginParam(String ip) {
        Assert.hasText(ip, "The ip is blank.");
        log.debug("obtain server registration information;ip={}", ip);

        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        ServerLoginParam loginParam = antServerParam.getSsh();
        // 解密
        if (loginParam != null && StringUtils.isNoneBlank(loginParam.getPwd())) {
            loginParam.setPwd(managerEncryptor.decrypt(loginParam.getPwd()));
        }
        return loginParam;
    }
}

