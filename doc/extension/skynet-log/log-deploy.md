<h1>'基础平台-日志'系统部署</h1>
<!-- TOC -->

- [1. 概述](#1-概述)
- [2. 系统要求](#2-系统要求)
- [3. 部署](#3-部署)
  - [3.1. 导入服务定义和资源仓库](#31-导入服务定义和资源仓库)
  - [3.2. 修改配置](#32-修改配置)
  - [3.3. 服务分配](#33-服务分配)

<!-- /TOC -->

# 1. 概述

系统编码：ant-log

系统名称: 基础平台-日志

系统功能：采集、存储、展示、搜索日志



包含服务组件:  Elasticsearch, Logstash, kibana, Kafka

# 2. 系统要求

RedHat7/centos7/ubuntu16.04 及以上版本

* 64位系统
* linux内核版本3.10及以上
* iptables 1.4及以上版本（iptables --version查看）
* XZ Utils 4.9及以上版本 (xz --version查看)

# 3. 部署

## 3.1. 导入服务定义和资源仓库

进入xManager， 左边导航菜单选择"服务管理”，点击“注册服务器”，填写必要信息同时勾选 `“启用docker”`  ( 如果此服务器已经安装过docker环境，则不要勾选 ) , 保存。

[1] 下载  ant-log.tar.gz（地址见下方链接），拷贝到ant-xmanager所在服务器的 `'${SKYNET_HOME}/repo'` 目录（如`'/iflytek/server/skynet/repo'`）下解压。

> 下载链接：
> 
> 1）X86_64平台：  
> ant-log.tar.gz : http://************/skynet/2.1.1012/repo-ext/ant-log.tar.gz  
>
> 
> 2）ARM64v8平台  
> ant-log.tar.gz : http://************/skynet/2.1.1012.aarch64/repo-ext/ant-log.tar.gz 

[2] 下载 ant-log.zk.config（地址见下方链接）  
> 下载链接：
> 
> 1）X86_64平台：  
> ant-log.zk.config:  http://************/skynet/2.1.1012/repo-ext/ant-log/ant-log.zk.config
> 
> 2）ARM64v8平台  
> ant-log.zk.config:  http://************/skynet/2.1.1012.aarch64/repo-ext/ant-log/ant-log.zk.aarch64.config

点击`"服务定义 → 导入-系统定义"` 

![alt 导入服务定义](res/import-def.png)

点击"浏览文件"，选择之前保存在本地的 ant-log.zk.config 进行导入。

[3] 到准备部署Elasticsearch的服务器上执行

```shell script
#内存映射设置
sysctl -w vm.max_map_count=262144
```

## 3.2. 修改配置

修改`"服务定义 → 集群级属性配置"`

```properties

skynet.logging.es.hosts.http=*************:19200,************:19200,************:19200
skynet.logging.es.hosts.tcp=*************:19300,************:19300,************:19300

# 注意修改后，需要重启 xagent

```



|配置|说明|
|-|-|
|skynet.logging.es.hosts.http|elasticsearch 基于http端口的服务器列表|
|skynet.logging.es.hosts.tcp|elasticsearch 基于tcp端口的服务器列表|
|skynet.logging.kafka.enabled|开启日志输出到kafka队列功能( true / false )|
|skynet.logging.kafka.brokerList|kafka队列broker列表，多个节点用逗号隔开|
|skynet.skywalking.oap.host|`'追踪-指标收集'` 服务的IP地址和端口, 保持默认值127.0.0.1:11800就可以|

## 3.3. 服务分配
点击”服务管理 → 操作 → 服务分配"，进行服务分配。

![alt](res/service-allocate.png)

上图中Elasticsearch是单节点部署，只分配node1即可。

如果Elasticsearch需要多节点部署，请分别在不同节点分配`'Skynet日志-ES集群-node{序号}'`服务, 分配的节点IP须与skynet.logging.es.hosts.http， skynet.logging.es.hosts.tcp 保持一致。目前Skynet提供的Elasticsearch服务定义只支持到最大三节点集群。


