<h1>SpringBoot Demo服务开发</h1>

<!-- TOC -->

- [1. 功能](#1-功能)
- [2. 源码](#2-源码)
- [3. 关键部分解析 - 基于SpringBoot 1.x](#3-关键部分解析---基于springboot-1x)
  - [3.1. pom.xml配置](#31-pomxml配置)
  - [3.2. application.properties配置](#32-applicationproperties配置)
  - [3.3. Counter的使用](#33-counter的使用)
  - [3.4. Timer的使用](#34-timer的使用)
  - [3.5. 条件注解的使用](#35-条件注解的使用)
- [3. 关键部分解析 - 基于SpringBoot 2.x](#3-关键部分解析---基于springboot-2x)
  - [3.1. pom.xml配置](#31-pomxml配置-1)
  - [3.2. application.properties配置](#32-applicationproperties配置-1)
  - [3.3 其他](#33-其他)

<!-- /TOC -->


该项目用于演示如何开发一个基于SpringBoot的web项目，由skynet对其进行托管, 并对外提供监控指标数据。

# 1. 功能

该项目提供了三个接口
* /api/foo1 : 演示Counter的用法。该接口随机耗时N秒，然后随机返回业务返回码。该接口提供了按返回码分类的耗时统计和请求次数统计。
* /api/foo2 ：演示Timer的用法。该接口功能代码和/api/foo1类似。
* /api-v2/hello ：演示使用skynet的属性配置

# 2. 源码

项目源码下载地址 ：

http://************/other/skynet-doc-demo/doc-demo-springboot1.x.zip

http://************/other/skynet-doc-demo/doc-demo-springboot2.x.zip


# 3. 关键部分解析 - 基于SpringBoot 1.x

## 3.1. pom.xml配置

为了提供prometheus格式的监控数据，需要添加以下依赖

```xml
<parent>
  <groupId>org.springframework.boot</groupId>
	<artifactId>spring-boot-starter-parent</artifactId>
	<version>1.5.22.RELEASE</version>
</parent>

<dependencies>
	<dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-actuator</artifactId>
	</dependency>
 	<dependency>
		<groupId>io.micrometer</groupId>
		<artifactId>micrometer-registry-prometheus</artifactId>
		<version>1.3.1</version>
	</dependency>
	<dependency>
		<groupId>io.micrometer</groupId>
		<artifactId>micrometer-spring-legacy</artifactId>
		<version>1.3.1</version>
	</dependency>
	<dependency>
		<groupId>org.springframework.cloud</groupId>
		<artifactId>spring-cloud-starter-config</artifactId>
	</dependency>
  ...
</dependencies>
```
io.micrometer提供了生成监控数据的统一编程接口门面，屏蔽了不同后端监控系统的差异，相当于监控领域的slf4j。对于SpringBoot1.x, 需要添加依赖micrometer-spring-legacy，这是micrometer提供的与springboot集成的jar。



## 3.2. application.properties配置

```bash
management.security.enabled=false
management.metrics.export.prometheus.enabled=true

#禁用springboot自带的metrics
management.metrics.enable.jvm=false
management.metrics.enable.tomcat=false
management.metrics.enable.system=false
management.metrics.enable.logback=false
management.metrics.enable.process=false
```

我们需要设置`'management.metrics.export.prometheus.enabled=true'`,开启prometheus的exporter。

springboot默认提供了一些metrics，我们可以根据自己的需要使用management.metrics.enable.{metric_prefix}配置屏蔽掉不想要的指标。

## 3.3. Counter的使用

prometheus提供了四种指标类型：Counter,Gauge,Histogram,Summary，最常用的类型是Counter和Gauge。Demo项目演示了Counter的用法，关于另外三种类型的用法，请参考io.micrometer的文档，这里不再详细讲述。

首先，我们需要注入一个MeterRegistry Bean，该Bean由SpringBoot生成（org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration）。

![alt](res/src-1.png)

我们使用Counter统计请求的总次数和总耗时，指标名称分别为'foo1.request.mytimes'和'foo1.request.mycost'。我们使用名为"code"的标签对指标进行了区分。

![alt](res/src-2.png)


## 3.4. Timer的使用

对于耗时类Counter指标的统计，io.micrometer提供了更易用的一个类: io.micrometer.core.instrument.Timer。

下面代码要完成的功能和其实1.3.2实现的Counter一样。

![alt](res/src-3.png)

## 3.5. 条件注解的使用

我们使用了一个条件注解，用来演示skynet的属性配置。如下图

![alt](res/src-4.png)

我们可以配置服务定义的demo.api.v2.enable属性为true或者false来决定是否开启这个api接口


# 3. 关键部分解析 - 基于SpringBoot 2.x

## 3.1. pom.xml配置

为了提供prometheus格式的监控数据，需要添加以下依赖

```xml
<dependencies>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.2.RELEASE</version>
	</parent>
  ...
	<dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-actuator</artifactId>
	</dependency>
	<dependency>
		<groupId>io.micrometer</groupId>
		<artifactId>micrometer-registry-prometheus</artifactId>
	</dependency>
	<dependency>
		<groupId>org.springframework.cloud</groupId>
		<artifactId>spring-cloud-starter-config</artifactId>
	</dependency>
  ...
</dependencies>
```

对于SpringBoot2.x, spring-boot-starter-actuator集成了io.micrometer，进一步简化了业务服务集成prometheus的开发步骤。

## 3.2. application.properties配置

```bash
management.endpoints.web.exposure.include=*
management.endpoints.enabled-by-default=true

#禁用springboot自带的metrics
management.metrics.enable.jvm=false
management.metrics.enable.tomcat=false
management.metrics.enable.system=false
management.metrics.enable.logback=false
management.metrics.enable.process=false
```

## 3.3 其他 
关于io.micrometer的api使用，集成2.x和1.x没有太大差异，所以java源码没有任何变化。
