package reporter

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/go-zookeeper/zk"
	"github.com/spf13/viper"
	"iflytek.com/skynet/agent/controller"
)

var preOnlineWorkers []controller.WorkersState = nil
var reportChannel chan struct{} = make(chan struct{}, 1024)

// 定时扫描在线的 SkynetApp，将 Action 状态汇报到 Zookeeper
func ReportActionStatus(conn *zk.Conn) {

	go func() {
		for range reportChannel {
			for i := 1; i <= 3; i++ {
				time.Sleep(5 * time.Second)
				reportWorkersStatus(conn)
			}
		}
	}()

	// agent 启动后立即汇报
	reportWorkersStatus(conn)

	// 之后每隔 1 分钟汇报一次
	ticker := time.NewTicker(1 * time.Minute)
	for range ticker.C {
		reportWorkersStatus(conn)
	}
}

// 立即汇报服务状态
// 由于不确定服务什么时候启动好，所以延迟 5 秒再汇报，重试 3 次
func ReportActionStatusImmediately(conn *zk.Conn) {
	reportChannel <- struct{}{}
}

// 汇报在线服务，摘除离线服务
func reportWorkersStatus(conn *zk.Conn) {

	fmt.Printf("Report workers state start...\n")

	// 获取所有在线服务
	workers, err := getOnlineWorkers()
	if err != nil {
		fmt.Printf("Get online workers state err %s\n", err.Error())
		return
	}

	// 汇报服务状态
	added, deleted, changed, old := diffOnlineWorkers(workers)
	for _, worker := range added {
		fmt.Printf("Report added worker %s.\n", worker.Aid)
		actionStatus := convertActionStatus(worker)
		reportActionStatus(conn, actionStatus)
	}
	for _, worker := range deleted {
		fmt.Printf("Delete worker %s.\n", worker.Aid)
		actionStatus := convertActionStatus(worker)
		deleteActionStatus(conn, actionStatus)
	}
	for i, worker := range changed {
		fmt.Printf("Report changed worker %s.\n", worker.Aid)
		actionStatus := convertActionStatus(worker)
		reportActionStatus(conn, actionStatus)
		oldActionStatus := convertActionStatus(old[i])
		deleteActionStatus(conn, oldActionStatus)
	}
	preOnlineWorkers = workers

	fmt.Printf("Report workers state done.\n")
}

// 对比在线服务，返回所有 新增的，删除的，更新的 服务
func diffOnlineWorkers(workers []controller.WorkersState) (
	[]controller.WorkersState, []controller.WorkersState, []controller.WorkersState, []controller.WorkersState) {

	added := getAddedWorkers(workers)
	deleted := getDeletedWorkers(workers)
	changed, old := getChangedWorkers(workers)

	return added, deleted, changed, old
}

// 返回新增的服务
func getAddedWorkers(workers []controller.WorkersState) []controller.WorkersState {
	if preOnlineWorkers == nil {
		return workers
	}
	added := []controller.WorkersState{}
	for _, c := range workers {
		// 当前服务不在历史记录中，说明是新增的
		if exists, _ := containsWorker(preOnlineWorkers, c); !exists {
			added = append(added, c)
		}
	}
	return added
}

// 返回删除的服务
func getDeletedWorkers(workers []controller.WorkersState) []controller.WorkersState {
	deleted := []controller.WorkersState{}
	if preOnlineWorkers == nil {
		return deleted
	}
	for _, p := range preOnlineWorkers {
		// 历史记录不在当前服务列表中，说明是删除的
		if exists, _ := containsWorker(workers, p); !exists {
			deleted = append(deleted, p)
		}
	}
	return deleted
}

// 返回更新的服务
func getChangedWorkers(workers []controller.WorkersState) ([]controller.WorkersState, []controller.WorkersState) {
	changed := []controller.WorkersState{}
	old := []controller.WorkersState{}
	if preOnlineWorkers == nil {
		return changed, old
	}
	for _, c := range workers {
		// 当前服务在历史记录中，则判断是否发生变动
		if exists, p := containsWorker(preOnlineWorkers, c); exists {
			if !reflect.DeepEqual(c, *p) {
				changed = append(changed, c)
				old = append(old, *p)
			}
		}
	}
	return changed, old
}

// 判断服务是否存在
func containsWorker(workers []controller.WorkersState, worker controller.WorkersState) (bool, *controller.WorkersState) {
	for _, w := range workers {
		if w.Aid == worker.Aid {
			return true, &w
		}
	}
	return false, nil
}

// 获取所有在线服务
func getOnlineWorkers() ([]controller.WorkersState, error) {
	workersStatus, err := controller.GetWorkersState()
	if err != nil {
		return nil, err
	}
	online := []controller.WorkersState{}
	for _, worker := range workersStatus {
		if worker.State.Up == "UP" {
			online = append(online, worker)
		}
	}
	return online, nil
}

// 将 WorkersState 转换为 ServerStatus
func convertActionStatus(workersStatus controller.WorkersState) controller.ServerStatus {
	actionStatus := controller.ServerStatus{
		Action:   workersStatus.Action.FullName,
		Protocol: "http",
		Type:     "kubernetes",
		Ip:       viper.GetString("server.ip"),
		Port:     workersStatus.Action.AppPort,
		Ports:    workersStatus.Action.ExtPorts,
		Pid:      0,
		Start:    "",
		Report:   "",
		Up:       "UP",
	}
	return actionStatus
}

// 汇报当个服务状态到 ZK，汇报路径为 /skynet/cluster/online/action/xxx@yyy
func reportActionStatus(conn *zk.Conn, actionStatus controller.ServerStatus) {
	jsonData, err := json.Marshal(actionStatus)
	if err != nil {
		fmt.Printf("JSON Marshal action status err %s\n", err.Error())
		return
	}

	// 确保 zkPath 已存在，如果不存在，则创建对应的 zkPath
	zkPath := fmt.Sprintf("/%s/cluster/online/action/%s",
		viper.GetString("zookeeper.cluster-name"), actionStatus.Action)
	err = makeZkPath(conn, zkPath)
	if err != nil {
		fmt.Printf("Make zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		return
	}

	// 汇报服务状态
	zkPath = fmt.Sprintf("%s/%s:%d", zkPath, actionStatus.Ip, actionStatus.Port)
	exist, _, err := conn.Exists(zkPath)
	if err != nil {
		fmt.Printf("Exists zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		return
	}
	if !exist {
		_, err = conn.Create(zkPath, []byte(jsonData), zk.FlagEphemeral, zk.WorldACL(zk.PermAll))
		if err != nil {
			fmt.Printf("Create zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		}
	} else {
		_, err = conn.Set(zkPath, []byte(jsonData), -1)
		if err != nil {
			fmt.Printf("Set zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		}
	}
}

// 删除服务状态
func deleteActionStatus(conn *zk.Conn, actionStatus controller.ServerStatus) {

	zkPath := fmt.Sprintf("/%s/cluster/online/action/%s",
		viper.GetString("zookeeper.cluster-name"), actionStatus.Action)
	zkPath = fmt.Sprintf("%s/%s:%d", zkPath, actionStatus.Ip, actionStatus.Port)
	exist, _, err := conn.Exists(zkPath)
	if err != nil {
		fmt.Printf("Exists zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		return
	}
	if exist {
		err = conn.Delete(zkPath, -1)
		if err != nil {
			fmt.Printf("Delete zkPath err %s, zkPath=%s\n", err.Error(), zkPath)
		}
	}
}

// 确保 zkPath 已存在，如果不存在，则创建对应的 zkPath
func makeZkPath(conn *zk.Conn, zkPath string) error {
	currPath := ""
	paths := strings.Split(zkPath[1:], "/")
	for _, path := range paths {
		currPath = currPath + "/" + path
		exist, _, err := conn.Exists(currPath)
		if err != nil {
			return err
		}
		if !exist {
			_, err = conn.Create(currPath, []byte(""), 0, zk.WorldACL(zk.PermAll))
			if err != nil {
				return err
			}
		}
	}
	return nil
}
