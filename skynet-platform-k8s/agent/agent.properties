server.ip=*************
server.port=32630

agent.project-version=3.4.2
agent.build-sid=1011
agent.build-number=cfb3c2bf
agent.build-time=20221125
agent.build-branch=kubernetes
agent.skynet-boot-version=4.0.5

builder.auto-fix-port=false

zookeeper.server-list=************:2181
zookeeper.cluster-name=skynet

skynet.manager.url=http://************:2230
skynet.home=/iflytek/server/skynet
skynet.k8s.namespace=default
skynet.auth.api-key=skynet
skynet.auth.api-secret=SKYNET_API_SECRET_PLACEHOLDER

registry.url=*************:5000
