package controller

type ActionInfo struct {
	Index        int               `json:"index"`
	Code         string            `json:"code"`
	Name         string            `json:"name"`
	Num          int               `json:"num"`
	Replicas     int               `json:"replicas"`
	NodeSelector map[string]string `json:"nodeSelector"`
	Plugin       string            `json:"plugin"`
	Enable       bool              `json:"enable"`
}

type TopologyInfo struct {
	Index   int          `json:"index"`
	Ip      string       `json:"ip"`
	Actions []ActionInfo `json:"actions"`
}

type ServerStatus struct {
	Action   string      `json:"action"`
	Protocol string      `json:"protocol"`
	Type     string      `json:"type"`
	Ip       string      `json:"ip"`
	Port     int         `json:"port"`
	Ports    []int       `json:"ports"`
	Pid      int         `json:"pid"`
	Up       string      `json:"up"`
	Start    string      `json:"start"`
	Report   string      `json:"report"`
	Metadata interface{} `json:"metadata"`
}

type WorkersState struct {
	Index  int          `json:"index"`
	Aid    string       `json:"aid"`
	Action ActionDetail `json:"action"`
	State  StateDetail  `json:"state"`
}

type ActionDetail struct {
	Ip           string `json:"ip"`
	Port         int    `json:"port"`
	AppPort      int    `json:"appPort"`
	ExtPorts     []int  `json:"extPorts"`
	Pid          int    `json:"pid"`
	Name         string `json:"name"`
	Up           string `json:"up"`
	WorkloadType string `json:"workloadType"`
	Index        int    `json:"index"`
	SubIndex     int    `json:"subIndex"`
	Code         string `json:"code"`
	Plugin       string `json:"plugin"`
	Enable       bool   `json:"enable"`
	BootType     string `json:"bootType"`
	FullName     string `json:"param"`
	Aid          string `json:"aid"`
	Replicas     int32  `json:"replicas"`
}

type State struct {
	Code    int    `json:"code"`
	Success bool   `json:"success"`
	Ok      int    `json:"ok"`
	Failed  string `json:"failed"`
}

type StateDetail struct {
	WorkloadType string `json:"workloadType"`
	StartTime    string `json:"startTime"`
	Up           string `json:"up"`
	UpTime       string `json:"upTime"`
	State        State  `json:"state"`
	Replicas     int32  `json:"replicas"`
}

type NodeStatus struct {
	StartTime string `json:"startTime"`
	UpTime    string `json:"UpTime"`
	Up        string `json:"up"`
	Protocol  string `json:"protocol"`
	ZkServers string `json:"zkServers"`
}

type BootStatus struct {
	Aid    string       `json:"aid"`
	Cmd    string       `json:"cmd"`
	Status State        `json:"status"`
	Action ActionDetail `json:"action"`
	State  StateDetail  `json:"state"`
}
