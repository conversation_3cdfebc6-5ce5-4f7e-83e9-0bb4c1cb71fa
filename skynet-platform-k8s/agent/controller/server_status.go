package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

func FetchServerStatus(c *gin.Context) {
	serverStatus := GetServerStatus()
	c.<PERSON>(http.StatusOK, serverStatus)
}

func GetServerStatus() ServerStatus {
	serverStatus := ServerStatus{
		Action:   "ant-xagent@ant",
		Protocol: "http",
		Type:     "kubernetes",
		Ip:       viper.GetString("server.ip"),
		Port:     viper.GetInt("server.port"),
		Pid:      0,
		Start:    "",
		Report:   "",
		Up:       "UP",
		Metadata: map[string]string{
			"project-version":     viper.GetString("agent.project-version"),
			"build-sid":           viper.GetString("agent.build-sid"),
			"build-number":        viper.GetString("agent.build-number"),
			"build-time":          viper.GetString("agent.build-time"),
			"build-branch":        viper.GetString("agent.build-branch"),
			"skynet-boot-version": viper.GetString("agent.skynet-boot-version"),
		},
	}
	return serverStatus
}
