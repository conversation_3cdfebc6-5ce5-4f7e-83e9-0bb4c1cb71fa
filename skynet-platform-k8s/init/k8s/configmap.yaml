apiVersion: v1
kind: ConfigMap
metadata:
  name: skynet-init-config
data:
  init.properties: |
    skynet_manager_url=http://************:2230
    skynet_action-point=
    skynet_action_ip=*************
    skynet_action_port=0
    skynet_action_index=0
    skynet_auth_api-key=skynet
    skynet_auth_api-secret=SKYNET_API_SECRET_PLACEHOLDER
    zookeeper.server-list=************:2181
    zookeeper.cluster-name=skynet
    k8s_docker_hub_host=*************:5000
    k8s_docker_hub_username=admin
    k8s_docker_hub_password=passw0rd
