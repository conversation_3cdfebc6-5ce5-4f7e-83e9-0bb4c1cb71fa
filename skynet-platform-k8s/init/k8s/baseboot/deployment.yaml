apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: turing-cloud-lb-beehive-paas
  name: turing-cloud-lb-beehive-paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: turing-cloud-lb-beehive-paas
  strategy: {}
  template:
    metadata:
      labels:
        app: turing-cloud-lb-beehive-paas
    spec:
      initContainers:
      - image: sealos.hub:5000/skynet/init:3.4.2-1011
        name: skynet-init
        command: ['/init', '--action-code', 'turing-cloud-lb', '--plugin-code', 'beehive-paas', '--index', '0', '--port', '30301']
        volumeMounts:
          - name: skynet-init-config
            mountPath: /init.properties
            subPath: init.properties
          - name: turing-cloud-lb-beehive-paas-resources
            mountPath: ./resources
      containers:
      - image: sealos.hub:5000/skynet/centos:7
        name: turing-cloud-lb-beehive-paas
        command: ['/bin/bash', '-c', '/iflytek/server/skynet/target.sh']
        # command: ['tail', '-f', '/dev/null']
        volumeMounts:
          - name: turing-cloud-lb-beehive-paas-resources
            mountPath: /iflytek/server/skynet
            subPath: ./iflytek/server/skynet
      volumes:
        - name: skynet-init-config
          configMap:
            name: skynet-init-config
        - name: turing-cloud-lb-beehive-paas-resources
          emptyDir: {}
