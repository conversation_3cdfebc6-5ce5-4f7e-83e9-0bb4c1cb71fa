apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: skyline-mysql-skyline
  name: skyline-mysql-skyline
spec:
  replicas: 1
  selector:
    matchLabels:
      app: skyline-mysql-skyline
  strategy: {}
  template:
    metadata:
      labels:
        app: skyline-mysql-skyline
    spec:
      initContainers:
      - image: sealos.hub:5000/skynet/init:3.4.2-1011
        name: skynet-init
        command: ['/init', '--action-code', 'skyline-mysql', '--plugin-code', 'skyline', '--index', '0', '--port', '30306']
        volumeMounts:
          - name: skynet-init-config
            mountPath: /init.properties
            subPath: init.properties
          - name: skyline-mysql-skyline-resources
            mountPath: ./resources
      containers:
      - image: sealos.hub:5000/mark-engine/mysql:latest
        name: skyline-mysql-skyline
        env:
          - name: MYSQL_ROOT_PASSWORD
            value: '123456'
        # command: ['tail', '-f', '/dev/null']
        volumeMounts:
          # - name: skyline-mysql-skyline-resources
          #   mountPath: /var/lib/mysql
          #   subPath: ./iflytek/volume/turing/skyline/mysql/data
          # - name: skyline-mysql-skyline-resources
          #   mountPath: /var/log/mysql
          #   subPath: ./iflytek/volume/turing/skyline/mysql/log
          - name: skyline-mysql-skyline-resources
            mountPath: /etc/mysql/my.cnf
            subPath: ./iflytek/server/skynet/tmp/skyline-mysql@skyline/my.cnf
          - name: skyline-mysql-skyline-resources
            mountPath: /docker-entrypoint-initdb.d/skyline.sql
            subPath: ./iflytek/server/skynet/tmp/skyline-mysql@skyline/skyline.sql
      volumes:
        - name: skynet-init-config
          configMap:
            name: skynet-init-config
        - name: skyline-mysql-skyline-resources
          emptyDir: {}
