package skynet_client

//{"code":0,"message":"成功","data":{"token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTY2MjUzODA1OCwiY3JlYXRlZCI6MTY2MjUzNzQ1ODE1NCwiYXV0aG9yaXRpZXMiOltdfQ.iN4-n3dBlxiI56xkxqME-tuT4QprRsCO-_SciWDP43PnB3GJZoeG-z8coGBtdpKdbXfIKHohDDiL7PAkz-ewpA","tokenHeader":"Bearer"}}

type SkynetApiResponse[D interface{}] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    D      `json:"data"`
}

type AccessToken struct {
	Token       string `json:"token"`
	TokenHeader string `json:"tokenHeader"`
}

// K8sDependResourceRequest 服务依赖资源请求参数
type K8sDependResourceRequest struct {
	ActionPoint string `json:"actionPoint"`
	NodeIP      string `json:"nodeIp"`
	IP          string `json:"ip"`
	Port        int    `json:"port"`
	Ports       []int  `json:"ports"`
	Index       int    `json:"index"`
}

type DependFileItem struct {
	FileName       string `json:"fileName"`
	TargetDir      string `json:"targetDir"`
	Url            string `json:"url"`
	FileSize       int    `json:"fileSize"`
	LastUpdateTime string `json:"lastUpdateTime"`
	Md5sum         string `json:"md5sum"`
	Owner          string `json:"owner"`
	Mode           string `json:"mode"`
}

type DependConfigItem struct {
	FileName  string `json:"fileName"`
	TargetDir string `json:"targetDir"`
	Encoding  string `json:"encoding"`
	Text      string `json:"text"`
	Owner     string `json:"owner"`
	Mode      string `json:"mode"`
}

// K8sDependResourceResponse 服务依赖资源请求参数
type K8sDependResourceResponse struct {
	//skynetHome 目录
	SkynetHome string `json:"skynetHome"`

	//服务定义
	Definition interface{} `json:"definition"`

	//依赖配置列表
	ConfigItems []DependConfigItem `json:"configItems"`

	//依赖文件列表
	FileItems []DependFileItem `json:"fileItems"`

	//启动脚本
	TargetScript string `json:"targetScript"`
}

type ActionDefinitionDto struct {
	//actionCode 服务编码
	ActionPoint string `json:"actionPoint"`

	//服务类型
	Type string `json:"type"`

	StartupConfig StartupConfigDto `json:"startupConfig"`

	IntegrationConfig IntegrationConfig `json:"integrationConfig"`

	MeshConfigText string `json:"meshConfigText"`
}

type StartupConfigDto struct {
	//actionCode 服务编码
	RunnableJar string `json:"runnableJar"`
	WorkingDir  string `json:"workingDir"`
}

type IntegrationConfig struct {
	LogbackLogCollection bool `json:"logbackLogCollection"`
	MeshEnabled          bool `json:"meshEnabled"`
}
