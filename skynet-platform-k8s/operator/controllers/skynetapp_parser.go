package controllers

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 判断 Skynet 是否发生变动
func isSkynetAppChanged(skynetApp *skynetv1alpha1.SkynetApp) bool {

	if skynetApp.Annotations == nil {
		return true
	}

	newspec, _ := json.Marshal(skynetApp.Spec)
	oldspec := skynetApp.Annotations["spec"]
	return string(newspec) != oldspec
}

// 从 SkynetApp 中解析出所有的 K8S 对象
func getObjectsFromSkynetApp(skynetApp *skynetv1alpha1.SkynetApp) []client.Object {
	if skynetApp.Spec.BootType == "K8sBoot" {
		return getObjectsFromYaml(skynetApp)
	} else {
		return getObjectsFromNormal(skynetApp)
	}
}

// 从 SkynetApp 中解析出所有的 K8S 对象
// 其中 SkynetApp 是一个 K8sBoot 应用，需要从 YAML 中解析，YAML 中必须包含一个工作负载
// 比如 Deployment、DaemonSet 或 StatefulSet，其他类型的对象将作为依赖资源被创建
func getObjectsFromYaml(skynetApp *skynetv1alpha1.SkynetApp) []client.Object {
	objects := []client.Object{}
	yaml := skynetApp.Spec.BootOption.Yaml
	blocks := strings.Split(yaml, "---")
	for _, block := range blocks {
		decode := scheme.Codecs.UniversalDeserializer().Decode
		obj, _, err := decode([]byte(block), nil, nil)
		if err != nil {
			fmt.Printf("Error while decoding YAML object [%s] error %s\n", skynetApp.Name, err)
			continue
		}
		switch o := obj.(type) {
		case *appsv1.Deployment:
			fmt.Printf("o is a Deployment [%s]", o.Name)
			deployment := fillYamlDeployment(skynetApp, o)
			objects = append(objects, deployment)
			service := newYamlService(skynetApp, deployment.Spec.Template.Spec.Containers)
			if service != nil {
				objects = append(objects, service)
			}
		case *appsv1.StatefulSet:
			fmt.Printf("o is a StatefulSet [%s]", o.Name)
			statefulset := fillYamlStatefulSet(skynetApp, o)
			objects = append(objects, statefulset)
			service := newYamlService(skynetApp, statefulset.Spec.Template.Spec.Containers)
			if service != nil {
				objects = append(objects, service)
			}
		case *appsv1.DaemonSet:
			fmt.Printf("o is a DaemonSet [%s]", o.Name)
			daemonset := fillYamlDaemonSet(skynetApp, o)
			objects = append(objects, daemonset)
			service := newYamlService(skynetApp, daemonset.Spec.Template.Spec.Containers)
			if service != nil {
				objects = append(objects, service)
			}
		case *corev1.Service:
			fmt.Printf("o is a Service [%s]", o.Name)
			service := fillYamlObject(skynetApp, o)
			objects = append(objects, service)
		case *corev1.ConfigMap:
			fmt.Printf("o is a ConfigMap [%s]", o.Name)
			configMap := fillYamlObject(skynetApp, o)
			objects = append(objects, configMap)
		case *corev1.ServiceAccount:
			fmt.Printf("o is a ServiceAccount [%s]", o.Name)
			serviceAccount := fillYamlObject(skynetApp, o)
			objects = append(objects, serviceAccount)
		case *rbacv1.ClusterRole:
			fmt.Printf("o is a ClusterRole [%s]", o.Name)
			clusterRole := fillYamlObject(skynetApp, o)
			objects = append(objects, clusterRole)
		case *rbacv1.ClusterRoleBinding:
			fmt.Printf("o is a ClusterRoleBinding [%s]", o.Name)
			clusterRoleBinding := fillYamlObject(skynetApp, o)
			objects = append(objects, clusterRoleBinding)
		case *rbacv1.Role:
			fmt.Printf("o is a Role [%s]", o.Name)
			role := fillYamlObject(skynetApp, o)
			objects = append(objects, role)
		case *rbacv1.RoleBinding:
			fmt.Printf("o is a RoleBinding [%s]", o.Name)
			roleBinding := fillYamlObject(skynetApp, o)
			objects = append(objects, roleBinding)
		default:
			fmt.Printf("o is not supported [%s]", o)
		}
	}
	return objects
}

// 填充一些公共字段，比如 namespace 等
func fillYamlObject(skynetApp *skynetv1alpha1.SkynetApp, object client.Object) client.Object {
	object.SetNamespace(skynetApp.Namespace)
	return object
}

// 从 SkynetApp 中解析出所有的 K8S 对象
// 其中 SkynetApp 是一个普通的 Skynet 应用，直接将 SkynetApp 转换为 Deployment 和 Service
func getObjectsFromNormal(skynetApp *skynetv1alpha1.SkynetApp) []client.Object {
	objects := []client.Object{}
	objects = append(objects, newNormalDeployment(skynetApp))
	if !skynetApp.Spec.BootOption.HostNetwork {
		objects = append(objects, newNormalService(skynetApp))
	}
	return objects
}

/**
1. ObjectMeta.Name 和 Namespace 会覆盖
2. Spec.Template.ObjectMeta.Labels 会覆盖
3. XXX-resources 卷会自动挂到 containers 中
4. 自动加 InitContainers
5. 自动加 skynet-init-config 和 XXX-resources 卷
6. NodeSelector 会覆盖
*/
// 在 YAML 创建的 Deployment 中注入初始化容器和卷
func fillYamlDeployment(skynetApp *skynetv1alpha1.SkynetApp, deployment *appsv1.Deployment) *appsv1.Deployment {
	deployment.ObjectMeta.Name = skynetApp.Name
	deployment.ObjectMeta.Namespace = skynetApp.Namespace
	deployment.Spec.Template.ObjectMeta.Labels = fillLabels(skynetApp, deployment.Spec.Template.ObjectMeta.Labels)
	deployment.Spec.Template.Spec.Containers = fillContainer(skynetApp, deployment.Spec.Template.Spec.Containers)
	deployment.Spec.Template.Spec.InitContainers = fillInitContainer(skynetApp, deployment.Spec.Template.Spec.InitContainers)
	deployment.Spec.Template.Spec.Volumes = fillVolumes(skynetApp, deployment.Spec.Template.Spec.Volumes)
	deployment.Spec.Template.Spec.NodeSelector = skynetApp.Spec.NodeSelector
	deployment.Spec.Selector = fillLabelSelector(skynetApp, deployment.Spec.Selector)
	return deployment
}

// 在 YAML 创建的 StatefulSet 中注入初始化容器和卷
func fillYamlStatefulSet(skynetApp *skynetv1alpha1.SkynetApp, statefulset *appsv1.StatefulSet) *appsv1.StatefulSet {
	statefulset.ObjectMeta.Name = skynetApp.Name
	statefulset.ObjectMeta.Namespace = skynetApp.Namespace
	statefulset.Spec.Template.ObjectMeta.Labels = fillLabels(skynetApp, statefulset.Spec.Template.ObjectMeta.Labels)
	statefulset.Spec.Template.Spec.Containers = fillContainer(skynetApp, statefulset.Spec.Template.Spec.Containers)
	statefulset.Spec.Template.Spec.InitContainers = fillInitContainer(skynetApp, statefulset.Spec.Template.Spec.InitContainers)
	statefulset.Spec.Template.Spec.Volumes = fillVolumes(skynetApp, statefulset.Spec.Template.Spec.Volumes)
	statefulset.Spec.Template.Spec.NodeSelector = skynetApp.Spec.NodeSelector
	statefulset.Spec.Selector = fillLabelSelector(skynetApp, statefulset.Spec.Selector)
	return statefulset
}

// 在 YAML 创建的 DaemonSet 中注入初始化容器和卷
func fillYamlDaemonSet(skynetApp *skynetv1alpha1.SkynetApp, daemonSet *appsv1.DaemonSet) *appsv1.DaemonSet {
	daemonSet.ObjectMeta.Name = skynetApp.Name
	daemonSet.ObjectMeta.Namespace = skynetApp.Namespace
	daemonSet.Spec.Template.ObjectMeta.Labels = fillLabels(skynetApp, daemonSet.Spec.Template.ObjectMeta.Labels)
	daemonSet.Spec.Template.Spec.Containers = fillContainer(skynetApp, daemonSet.Spec.Template.Spec.Containers)
	daemonSet.Spec.Template.Spec.InitContainers = fillInitContainer(skynetApp, daemonSet.Spec.Template.Spec.InitContainers)
	daemonSet.Spec.Template.Spec.Volumes = fillVolumes(skynetApp, daemonSet.Spec.Template.Spec.Volumes)
	daemonSet.Spec.Template.Spec.NodeSelector = skynetApp.Spec.NodeSelector
	daemonSet.Spec.Selector = fillLabelSelector(skynetApp, daemonSet.Spec.Selector)
	return daemonSet
}

// 填充标签
func fillLabels(skynetApp *skynetv1alpha1.SkynetApp, labels map[string]string) map[string]string {
	results := map[string]string{"app": skynetApp.Name}
	for key, value := range labels {
		results[key] = value
	}
	return results
}

// 填充标签选择器
func fillLabelSelector(skynetApp *skynetv1alpha1.SkynetApp, labelSelector *metav1.LabelSelector) *metav1.LabelSelector {
	labels := map[string]string{"app": skynetApp.Name}
	for key, value := range labelSelector.MatchLabels {
		labels[key] = value
	}
	result := metav1.LabelSelector{MatchLabels: labels, MatchExpressions: labelSelector.MatchExpressions}
	return &result
}

// 将 SkynetApp 转换为 Deployment
func newNormalDeployment(skynetApp *skynetv1alpha1.SkynetApp) *appsv1.Deployment {
	labels := map[string]string{"app": skynetApp.Name}
	selector := &metav1.LabelSelector{MatchLabels: labels}
	return &appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "apps/v1",
			Kind:       "Deployment",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      skynetApp.Name,
			Namespace: skynetApp.Namespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: skynetApp.Spec.Replicas,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      labels,
					Annotations: newAnnotations(skynetApp),
				},
				Spec: corev1.PodSpec{
					Containers:         newContainer(skynetApp),
					InitContainers:     newInitContainer(skynetApp),
					Volumes:            newVolumes(skynetApp),
					NodeSelector:       skynetApp.Spec.NodeSelector,
					HostNetwork:        skynetApp.Spec.BootOption.HostNetwork,
					ServiceAccountName: "skynet-service-account",
					// Affinity: &corev1.Affinity{
					// 	PodAntiAffinity: &corev1.PodAntiAffinity{
					// 		RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
					// 			{
					// 				TopologyKey: "kubernetes.io/hostname",
					// 			},
					// 		},
					// 	},
					// },
				},
			},
			Selector: selector,
		},
	}
}

// 生成注解，比如 Prometheus 相关配置
func newAnnotations(skynetApp *skynetv1alpha1.SkynetApp) map[string]string {
	annotations := make(map[string]string)
	if skynetApp.Spec.Props != nil && skynetApp.Spec.Props["prometheusTarget"] != "" {
		annotations["prometheus.io/scrape"] = "true"
		annotations["prometheus.io/port"] = strconv.Itoa(int(skynetApp.Spec.BootOption.Ports[0].NodePort))
		annotations["prometheus.io/scheme"] = "http"
		// 解析 PrometheusTarget 生成注解，PrometheusTarget 格式如下：
		// - metrics:15s 不带认证信息
		// - xxx@/metrics:15s 带认证信息，其中 xxx 是加密的用户名密码
		parts := strings.Split(skynetApp.Spec.Props["prometheusTarget"], "@")
		if len(parts) == 1 {
			// 无认证
			annotations["prometheus.io/path"] = strings.Split(parts[0], ":")[0]
		} else if len(parts) == 2 {
			// 有认证
			annotations["prometheus.io/path"] = strings.Split(parts[1], ":")[0]
			annotations["prometheus.io/auth"] = parts[0]
		}
	}
	return annotations
}

// 填充卷
func fillVolumes(skynetApp *skynetv1alpha1.SkynetApp, volumes []corev1.Volume) []corev1.Volume {
	results := newVolumes(skynetApp)
	results = append(results, volumes...)
	return results
}

// 从 SkynetApp 中获取卷
func newVolumes(skynetApp *skynetv1alpha1.SkynetApp) []corev1.Volume {

	// Every skynetapp has these two volumes: skynet-init-config and the resources dir
	volumes := []corev1.Volume{
		{
			Name: "skynet-init-config",
			VolumeSource: corev1.VolumeSource{
				ConfigMap: &corev1.ConfigMapVolumeSource{
					LocalObjectReference: corev1.LocalObjectReference{
						Name: "skynet-init-config",
					},
				},
			},
		},
		{
			Name: fmt.Sprintf("%s-resources", skynetApp.Name),
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		},
	}

	// hostPath volumes
	pathTypeMap := map[string]corev1.HostPathType{
		"FileOrCreate":      corev1.HostPathFileOrCreate,
		"DirectoryOrCreate": corev1.HostPathDirectoryOrCreate,
		"Socket":            corev1.HostPathSocket,
	}
	for _, v := range skynetApp.Spec.BootOption.Mounts {
		if v.MountType == "hostPath" {
			pathType := pathTypeMap[v.PathType]
			volumes = append(volumes, corev1.Volume{
				Name: v.Name,
				VolumeSource: corev1.VolumeSource{
					HostPath: &corev1.HostPathVolumeSource{
						Path: v.Path,
						Type: &pathType,
					},
				},
			})
		}
	}
	return volumes
}

// 从 SkynetApp 中获取挂载
func newVolumeMounts(skynetApp *skynetv1alpha1.SkynetApp, bootOption *skynetv1alpha1.BootOption) []corev1.VolumeMount {

	volumeMounts := []corev1.VolumeMount{}
	if len(bootOption.Mounts) > 0 {
		for _, v := range bootOption.Mounts {
			mountPropagation := "None"
			if v.MountPropagation != "" {
				mountPropagation = v.MountPropagation
			}
			if v.MountType == "hostPath" {
				volumeMounts = append(volumeMounts, corev1.VolumeMount{
					Name:             v.Name,
					MountPath:        v.MountPath,
					MountPropagation: (*corev1.MountPropagationMode)(&mountPropagation),
				})
			} else {
				volumeMounts = append(volumeMounts, corev1.VolumeMount{
					Name:             v.Name,
					MountPath:        v.MountPath,
					SubPath:          v.SubPath,
					MountPropagation: (*corev1.MountPropagationMode)(&mountPropagation),
				})
			}
		}
	} else {
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      fmt.Sprintf("%s-resources", skynetApp.Name),
			MountPath: skynetApp.Spec.SkynetHome,
			SubPath:   fmt.Sprintf("./%s", skynetApp.Spec.SkynetHome),
		})
	}
	return volumeMounts
}

// 将 Skynet 卷挂载到容器中
func fillContainer(skynetApp *skynetv1alpha1.SkynetApp, containers []corev1.Container) []corev1.Container {

	volumeMounts := newVolumeMounts(skynetApp, &skynetApp.Spec.BootOption)
	results := []corev1.Container{}
	for _, container := range containers {
		container.VolumeMounts = append(container.VolumeMounts, volumeMounts...)
		results = append(results, container)
	}
	return results
}

// 从 SkynetApp 中获取容器列表
func newContainer(skynetApp *skynetv1alpha1.SkynetApp) []corev1.Container {

	containers := []corev1.Container{}
	containers = append(containers, newBootContainer(skynetApp, &skynetApp.Spec.BootOption, skynetApp.Name))
	if skynetApp.Spec.MeshOption != nil {
		containers = append(containers, newBootContainer(skynetApp, skynetApp.Spec.MeshOption, fmt.Sprintf("%s-mesh", skynetApp.Name)))
	}

	return containers
}

// 从 SkynetApp 中获取容器列表
func newBootContainer(skynetApp *skynetv1alpha1.SkynetApp, bootOption *skynetv1alpha1.BootOption, containerName string) corev1.Container {

	volumeMounts := newVolumeMounts(skynetApp, bootOption)

	envs := []corev1.EnvVar{}
	if len(bootOption.Envs) > 0 {
		for _, env := range bootOption.Envs {
			envs = append(envs, corev1.EnvVar{
				Name:  env.Name,
				Value: env.Value,
			})
		}
	}

	// 支持设置 GPU 选项
	resources := corev1.ResourceRequirements{}
	if bootOption.Gpus > 0 {
		resourceList := corev1.ResourceList{
			"nvidia.com/gpu": resource.MustParse(strconv.Itoa(bootOption.Gpus)),
		}
		resources.Limits = resourceList
		resources.Requests = resourceList
	}

	return corev1.Container{
		Name:            containerName,
		Image:           bootOption.Image,
		Command:         bootOption.Command,
		Args:            bootOption.Args,
		Env:             envs,
		VolumeMounts:    volumeMounts,
		ImagePullPolicy: corev1.PullIfNotPresent,
		SecurityContext: &corev1.SecurityContext{
			Privileged: bootOption.Privileged,
		},
		Resources: resources,
	}
}

// 从 SkynetApp 中获取初始化容器列表
func fillInitContainer(skynetApp *skynetv1alpha1.SkynetApp, containers []corev1.Container) []corev1.Container {
	results := newInitContainer(skynetApp)
	results = append(results, containers...)
	return results
}

// 从 SkynetApp 中获取初始化容器列表
func newInitContainer(skynetApp *skynetv1alpha1.SkynetApp) []corev1.Container {
	return []corev1.Container{
		{
			Name:    fmt.Sprintf("%s-init", skynetApp.Name),
			Image:   skynetApp.Spec.InitOption.Image,
			Command: skynetApp.Spec.InitOption.Command,
			Env: []corev1.EnvVar{
				// 向初始化容器中注入 K8S_NODE_IP 环境变量
				{
					Name: "K8S_NODE_IP",
					ValueFrom: &corev1.EnvVarSource{
						FieldRef: &corev1.ObjectFieldSelector{
							FieldPath: "status.hostIP",
						},
					},
				},
			},
			VolumeMounts: []corev1.VolumeMount{
				{
					Name:      "skynet-init-config",
					MountPath: "/init.properties",
					SubPath:   "init.properties",
				},
				{
					Name:      fmt.Sprintf("%s-resources", skynetApp.Name),
					MountPath: "./resources",
				},
			},
			ImagePullPolicy: corev1.PullIfNotPresent,
		},
	}
}

// 将 YAML 定义转化为 Service
func newYamlService(skynetApp *skynetv1alpha1.SkynetApp, containers []corev1.Container) *corev1.Service {

	// Multiple ports for a service
	ports := []corev1.ServicePort{}
	for i, p := range skynetApp.Spec.BootOption.Ports {
		// K8sBoot 的 containerPort 从 Yaml 中取，nodePort 从服务定义取
		// 两者需要一一对应
		containerPort := getContainerPort(containers, i)
		if containerPort == -1 {
			continue
		}
		ports = append(ports, corev1.ServicePort{
			Name:       fmt.Sprintf("%s-port-%d", skynetApp.Name, i),
			Port:       containerPort,
			TargetPort: intstr.FromInt(int(containerPort)),
			Protocol:   "TCP",
			NodePort:   p.NodePort,
		})
	}

	if len(ports) == 0 {
		return nil
	}

	return &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "Service",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      skynetApp.Name,
			Namespace: skynetApp.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Type:     corev1.ServiceTypeNodePort,
			Ports:    ports,
			Selector: map[string]string{"app": skynetApp.Name},
		},
	}
}

func getContainerPort(containers []corev1.Container, index int) int32 {
	ports := []int32{}
	for _, container := range containers {
		for _, port := range container.Ports {
			ports = append(ports, port.ContainerPort)
		}
	}
	if index >= len(ports) {
		return -1
	}
	return ports[index]
}

// 将 SkynetApp 定义转化为 Service
func newNormalService(skynetApp *skynetv1alpha1.SkynetApp) *corev1.Service {

	// Multiple ports for a service
	ports := []corev1.ServicePort{}
	for i, p := range skynetApp.Spec.BootOption.Ports {
		ports = append(ports, corev1.ServicePort{
			Name:       fmt.Sprintf("%s-port-%d", skynetApp.Name, i),
			Port:       p.ContainerPort,
			TargetPort: intstr.FromInt(int(p.ContainerPort)),
			Protocol:   "TCP",
			NodePort:   p.NodePort,
		})
	}

	return &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "Service",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      skynetApp.Name,
			Namespace: skynetApp.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Type:     corev1.ServiceTypeNodePort,
			Ports:    ports,
			Selector: map[string]string{"app": skynetApp.Name},
		},
	}
}
