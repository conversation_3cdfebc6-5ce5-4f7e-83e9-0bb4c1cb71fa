---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.0
  creationTimestamp: null
  name: skynetapps.skynet.iflytek.com
spec:
  group: skynet.iflytek.com
  names:
    kind: SkynetApp
    listKind: SkynetAppList
    plural: skynetapps
    singular: skynetapp
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: SkynetApp is the Schema for the skynetapps API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: SkynetAppSpec defines the desired state of SkynetApp
            properties:
              actionCode:
                type: string
              bootOption:
                properties:
                  args:
                    items:
                      type: string
                    type: array
                  command:
                    items:
                      type: string
                    type: array
                  envs:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                  hostNetwork:
                    type: boolean
                  image:
                    type: string
                  mounts:
                    items:
                      properties:
                        mountPath:
                          type: string
                        mountPropagation:
                          type: string
                        mountType:
                          description: emptyDir or hostPath
                          type: string
                        name:
                          type: string
                        path:
                          description: when mountType == "hostPath"
                          type: string
                        pathType:
                          type: string
                        subPath:
                          description: when mountType == "emptyDir"
                          type: string
                      required:
                      - mountPath
                      - mountType
                      - name
                      type: object
                    type: array
                  ports:
                    items:
                      properties:
                        containerPort:
                          format: int32
                          type: integer
                        nodePort:
                          format: int32
                          type: integer
                      required:
                      - containerPort
                      - nodePort
                      type: object
                    type: array
                  privileged:
                    type: boolean
                  yaml:
                    type: string
                type: object
              bootType:
                type: string
              index:
                format: int32
                type: integer
              initOption:
                properties:
                  command:
                    items:
                      type: string
                    type: array
                  image:
                    type: string
                required:
                - image
                type: object
              nodeSelector:
                additionalProperties:
                  type: string
                type: object
              pluginCode:
                type: string
              replicas:
                format: int32
                type: integer
              skynetHome:
                type: string
            required:
            - actionCode
            - bootOption
            - bootType
            - index
            - initOption
            - pluginCode
            - replicas
            - skynetHome
            type: object
          status:
            description: SkynetAppStatus defines the observed state of SkynetApp
            properties:
              resources:
                items:
                  properties:
                    message:
                      type: string
                    name:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
