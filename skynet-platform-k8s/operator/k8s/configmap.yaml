apiVersion: v1
data:
  controller_manager_config.yaml: "apiVersion: controller-runtime.sigs.k8s.io/v1alpha1\nkind: ControllerManagerConfig\nhealth:\n  healthProbeBindAddress: :8081\nmetrics:\n  bindAddress: 127.0.0.1:8080\nwebhook:\n  port: 9443\nleaderElection:\n  leaderElect: true\n  resourceName: 7c6be306.iflytek.com\n#   leaderElectionReleaseOnCancel defines if the leader should step down volume \n#   when the Manager ends. This requires the binary to immediately end when the\n#   Manager is stopped, otherwise, this setting is unsafe. Setting this significantly\n#   speeds up voluntary leader transitions as the new leader don't have to wait\n#   LeaseDuration time first.\n#   In the default scaffold provided, the program ends immediately after \n#   the manager stops, so would be fine to enable this option. However, \n#   if you are doing or is intended to do any operation such as perform cleanups \n#   after the manager stops then its usage might be unsafe.\n#   leaderElectionReleaseOnCancel: true\n"
kind: ConfigMap
metadata:
  name: skynet-operator-manager-config
  namespace: skynet-operator-system
