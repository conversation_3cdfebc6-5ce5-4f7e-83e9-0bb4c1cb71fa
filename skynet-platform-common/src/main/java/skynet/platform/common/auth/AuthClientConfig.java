package skynet.platform.common.auth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.client.AuthRestTemplateBuilder;
import skynet.boot.security.config.SkynetAuthClientAutoConfiguration;
import skynet.boot.security.config.SkynetAuthClientProperties;
import skynet.boot.security.config.SkynetBaseAuthProperties;

import java.time.Duration;

/**
 * <AUTHOR>
 */

@Slf4j
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(SkynetAuthClientAutoConfiguration.class)
@ConditionalOnBean(SkynetBaseAuthProperties.class)
public class AuthClientConfig {
    private final SkynetBaseAuthProperties.User user;

    public AuthClientConfig(SkynetBaseAuthProperties securityProperties) {
        this.user = securityProperties.getUser();
    }

    @Bean
    @ConfigurationProperties("skynet.auth")
    public AuthClientProperties authClientProperties() {
        return new AuthClientProperties();
    }


    @Bean
    @Scope("prototype")
    public AuthWsClient authWsClient(AuthClientProperties authClientProperties) {
        return new AuthWsClient(authClientProperties);
    }

    @Value("${skynet.fetch.server.status.timeout.seconds:8}")
    private int timeout;

    @Bean
    @ConditionalOnBean(AuthRestTemplateBuilder.class)
    public RestTemplate authRestTemplate(AuthRestTemplateBuilder authRestTemplateBuilder,
                                         AuthClientProperties authClientProperties) {
        SkynetAuthClientProperties properties = new SkynetAuthClientProperties();
        properties
                .setApiKey(authClientProperties.getApiKey())
                .setApiSecret(authClientProperties.getApiSecret())
                .setTimeout(getTimeout());
        properties.setUser(user.getName()).setPassword(user.getPassword());
        log.debug("SkynetAuthClientProperties={}", properties);
        return authRestTemplateBuilder.build(properties);
    }


    public Duration getTimeout() {
        return Duration.ofSeconds(timeout);
    }
}
