package skynet.platform.common.exception;

import java.io.Serial;

/**
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年10月30日 下午8:28:00]
 */
public class ActionNotExistException extends RuntimeException {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 133424423L;

    private String plugin;

    private String actionName;

    /**
     * Constructor
     */
    public ActionNotExistException() {

    }

    /**
     * @param plugin
     * @param actionName
     */
    public ActionNotExistException(String plugin, String actionName) {
        super(String.format("the action [%s@%s] is not exist", actionName, plugin));
        this.plugin = plugin;
        this.actionName = actionName;
    }

    /**
     * @return the plugin
     */
    public String getPlugin() {
        return plugin;
    }

    /**
     * @return the actionName
     */
    public String getActionName() {
        return actionName;
    }

    public String getActionPoint() {
        return String.format("%s@%s", actionName, plugin);
    }
}
