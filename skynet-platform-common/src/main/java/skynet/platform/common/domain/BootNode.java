package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.common.repository.domain.AntNodeUpType;

import java.util.List;

/**
 * BootNode
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2017年12月25日 下午1:44:54]
 */
@Getter
@Setter
public class BootNode extends Jsonable {

    public BootNode() {
    }

    @JSONField(ordinal = 10)
    private String ip;

    @JSONField(ordinal = 20)
    private int port;

    @JSONField(ordinal = 21)
    private int appPort;

    @JSONField(ordinal = 22)
    private List<Integer> extPorts;

    /**
     * server.context.path
     */
    @JSONField(ordinal = 25)
    private String path;

    @JSO<PERSON>ield(ordinal = 30)
    private int pid;

    @JSONField(ordinal = 40)
    private String name;

    @JSONField(ordinal = 1010)
    private AntNodeUpType up = AntNodeUpType.OFFLINE;

    @JSONField(ordinal = 1020)
    private String workloadType = "deployment";

    @Override
    public String toString() {
        return super.toString();
    }
}
