package skynet.platform.common.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * AntServer系统信息
 *
 * <AUTHOR> [2016年6月13日上午11:21:32]
 */
@Getter
@Setter
public class AntServerProperty {

    @JSONField(ordinal = 10)
    private String ip;
    @JSONField(ordinal = 20)
    private String host;
    @JSONField(ordinal = 24)
    private String os;
    @J<PERSON>NField(ordinal = 30)
    private int cpu;
    @JSONField(ordinal = 35)
    private String arch;
    @JSONField(ordinal = 40)
    private long mem;
    @JSONField(ordinal = 50)
    private String gpu;
    @JSONField(ordinal = 60)
    private List<GpuItem> gpus;

    public AntServerProperty() {
        this.gpus = new ArrayList<>(0);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, JSONWriter.Feature.BrowserCompatible);
    }
}