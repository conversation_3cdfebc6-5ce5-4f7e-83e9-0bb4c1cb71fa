package skynet.platform.common.domain;


import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.common.repository.domain.AntActionLabel;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 启动参数
 * </pre>
 * <p>
 * .
 *
 * <AUTHOR> [2018年7月5日 下午9:13:54]
 */
@Getter
@Setter
public class BootParam extends Jsonable {

    @JSONField(ordinal = 5)
    private boolean archived;

    /**
     * work_home": "工作目录{ACTION_HOME}",.
     */
    @JSONField(ordinal = 10)
    private String workHome;

    /**
     * 工作命令 eg.: java
     */
    @JSONField(ordinal = 20)
    private String workCmd;

    /**
     * 主Main 或 jar eg: xxx.jar or com.iflytek.Biz.Main
     */
    @JSONField(ordinal = 22)
    private String mainJar;

    /**
     * 日志文件.
     */
    @JSONField(ordinal = 23)
    private String logFile;

    /**
     * 工作参数 -Xms2G -Xmx2G -Dskynet.zookeeper.cluster_name=skynet -jar /iflytek/server/skynet/lib/skynet-cloud-2.0.0-SNAPSHOT.jar",
     */
    @JSONField(ordinal = 25)
    private List<String> workArgs = new ArrayList<>(0);

    /**
     * 协议类型.
     */
    @JSONField(ordinal = 26)
    private String actionProtocol;

    /**
     * 环境变量 "ENGINE_HOME=/xxxx;".
     */
    @JSONField(ordinal = 30)
    private Map<String, String> workEnvs = new LinkedHashMap<>(0);

    /**
     * 服务标签
     */
    private List<AntActionLabel> actionLabels;

    /**
     * The kill signal.
     */
    // ": " 退出信号{EXIST_SIGN}",
    @JSONField(ordinal = 40)
    private int killSignal = 15;

    /**
     * The is log collection.
     */
    @JSONField(ordinal = 50)
    private boolean isLogCollection = false;

    /**
     * 是否开启SkynetMesh
     */
    @JSONField(ordinal = 60)
    private boolean meshEnabled = false;

    /**
     * 显示首页URL，默认为空，就不在左边导航显示.
     */
    @JSONField(ordinal = 67)
    private String indexPageUrl;

    /**
     * 健康状态检测.
     */
    @JSONField(ordinal = 70)
    private HealthParam healthParam;

    /**
     * 关联文件.
     */
    @JSONField(ordinal = 80)
    private List<UpdateParam> updateParams = new ArrayList<>();


    @JSONField(ordinal = 90)
    private List<ExtConfigItem> extConfigItems = new ArrayList<>();

    /**
     * skynet mesh配置文本
     */
    @JSONField(ordinal = 92)
    private String meshConfigText;

    /**
     * 之前的 dockder 参数
     */
    @JSONField(ordinal = 95)
    private String oldDockerEnv;
    /**
     * dockder 参数
     */
    @JSONField(ordinal = 100)
    private String dockerEnv;

    /**
     * 扩展端口
     */
    @JSONField(ordinal = 110)
    private String ports;


    /**
     * K8sBoot yaml配置
     */
    @JSONField(ordinal = 120)
    private String yaml;


    @JSONField(ordinal = 130)
    private Integer replicas = 1;

    @JSONField(ordinal = 140)
    private Integer instances = 1;

    /**
     * 通过code 获取服务标签
     *
     * @param code
     * @return 如果不存在，将返回 null;
     */
    public AntActionLabel getActionLabelByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new IllegalArgumentException("code is blank");
        }
        if (actionLabels != null) {
            for (AntActionLabel antActionLabel : actionLabels) {
                if (code.equals(antActionLabel.getCode())) {
                    return antActionLabel;
                }
            }
        }
        return null;
    }

    /**
     * @since 3.4.10
     */
    private List<String> configBlockCodes = new ArrayList<>(0);

    /**
     * 依赖action列表
     *
     * @since 3.4.11
     */
    private List<DependAction> dependActions = new ArrayList<>(0);

}
