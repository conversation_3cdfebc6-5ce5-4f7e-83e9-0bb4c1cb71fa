package skynet.platform.common.domain;

/**
 * 服务控制类型
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2017年12月11日 下午3:47:20]
 */
public enum AntServerControlType {

    /**
     * 获取AntServer状态
     */
    FETCH_SERVER_STATE,

    /**
     * 获取AntWorker列表状态
     */
    FETCH_WORKERS_STATE,

    /**
     * 停止AntServer服务
     */
    STOP_SERVER,

    /**
     * 停止AntWorker(由于是守护，就是重启)
     */
    STOP_WORKER,

    /**
     * 重启
     */
    REBOOT_WORKER;
}