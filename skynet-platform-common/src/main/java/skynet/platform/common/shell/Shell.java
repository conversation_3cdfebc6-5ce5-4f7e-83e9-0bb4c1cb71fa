package skynet.platform.common.shell;

import skynet.boot.common.OsUtil;

import java.io.File;

/**
 * <AUTHOR>
 */
public interface Shell extends AutoCloseable {

    static Shell build(String ip, int port, String username, String password, int timeout) throws Exception {
        return OsUtil.isLocalIP(ip) ? new Shell4Local() : new Shell4Remote(ip, port, username, password, timeout);
    }

    static Shell build() throws Exception {
        return new Shell4Local();
    }

    /**
     * 通过 shell 执行命令
     *
     * @param commands command lines
     * @return the result of executing cmd
     */
    String execCmd(String... commands) throws Exception;

    /**
     * 把 srcFile 拷贝到 absoluteDestDir 下
     * 拷贝完成后 absoluteDestDir 是 srcFile 副本的父目录
     * absoluteDestDir 不存在则新建
     * absoluteDestDir 下已有 srcFile 则覆盖
     *
     * @param srcFile         源文件
     * @param absoluteDestDir 目的地址，绝对路径
     * @return success or fail
     * @throws Exception exception
     */
    boolean copy(File srcFile, String absoluteDestDir) throws Exception;

    /**
     * 检查文件是否存在
     *
     * @param absoluteFilePath absolute file path
     * @return if file exists return true , else return false
     * @throws Exception exception
     */
    boolean isFileExist(String absoluteFilePath) throws Exception;

    /**
     * 检查目录是否存在
     *
     * @param absoluteDirPath absolute directory path
     * @return if directory exists return true, else return false
     * @throws Exception exception
     */
    boolean isDirExist(String absoluteDirPath) throws Exception;

    /**
     * 创建文件夹，如果 absoluteDirPath 路径已经存在则不操作
     *
     * @param absoluteDirPath 文件夹绝对路径
     * @return success or fail
     * @throws Exception exception
     */
    boolean mkdir(String absoluteDirPath) throws Exception;

    /**
     * 读文件内容做字符串返回
     *
     * @param absoluteFilePath 文件绝对路径
     * @return 文件内容
     * @throws Exception exception
     */
    String readFile(String absoluteFilePath) throws Exception;
}
