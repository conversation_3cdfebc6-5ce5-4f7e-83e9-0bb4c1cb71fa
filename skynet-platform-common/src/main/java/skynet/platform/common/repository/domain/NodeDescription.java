package skynet.platform.common.repository.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class NodeDescription extends Jsonable {

    public NodeDescription() {

    }

    public NodeDescription(String code, String name, String desc) {
        this.setCode(code);
        this.setName(StringUtils.isBlank(name) ? code : name);
        this.setDesc(desc);
    }

    @JSONField(ordinal = 0)
    private int index;

    @JSONField(ordinal = 5)
    private String code = "";

    @JSONField(ordinal = 10)
    private String name = "";

    @JSONField(ordinal = 20)
    private String desc = "";

    @JSONField(ordinal = 30)
    private String version = "";

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isValidity() {
        return StringUtils.isNoneBlank(code) && StringUtils.isNoneBlank(name) && StringUtils.isNoneBlank(desc) && StringUtils.isNoneBlank(version);
    }
}
