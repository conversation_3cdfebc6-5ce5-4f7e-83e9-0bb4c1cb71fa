package skynet.platform.common.repository.config;


import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.common.repository.exception.InvalidActionPointException;

import java.util.List;

/**
 * Skynet 配置服务接口契约
 *
 * <AUTHOR>
 */
public interface AntConfigService4Plugin {

    NodeDescription getSkynet();

    /**
     * 获取 Plugin 路径
     *
     * @return
     */
    String getSkynetPluginPath();

    /**
     * 获取 action 路径
     *
     * @param plugin
     * @param action
     * @return
     */
    String getActionPath(String plugin, String action);

    String getActionPath(String actionId) throws InvalidActionPointException;

    /**
     * 获取插件 列表
     *
     * @return
     */
    List<NodeDescription> getPlugins();

    /**
     * 根据插件名称获取插件对象信息
     *
     * @param plugin
     * @return
     */
    NodeDescription getPlugin(String plugin);

    NodeDescription getPlugin();

    /**
     * 获取 某插件 下的所有Action列表
     *
     * @param plugin
     * @return
     */
    List<NodeDescription> getActions(String plugin);

    /**
     * <pre>
     * 如果不存将返回Null
     * </pre>
     *
     * @param plugin
     * @param action
     * @return
     */
    NodeDescription getAction(String plugin, String action);

    /**
     * <pre>
     * 如果不存将返回Null
     * </pre>
     *
     * @param actionPoint
     * @return
     */
    NodeDescription getAction(String actionPoint);

}
