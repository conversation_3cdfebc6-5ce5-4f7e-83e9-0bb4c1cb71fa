package skynet.platform.common.repository.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.AppContext;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.common.domain.SampleState;
import skynet.boot.common.utils.DateUtil;
import skynet.boot.zookeeper.SkynetZkProperties;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * online ant node
 *
 * <AUTHOR>
 */
@Getter
@Setter
public final class AntNodeState extends Jsonable {

    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private String uuid;
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private String clusterName;
    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    private String hostName;
    @JSONField(ordinal = 35)
    @JsonProperty(index = 30)
    private Map<String, Object> os;
    @JSONField(ordinal = 38)
    private boolean ssl;
    @JSONField(ordinal = 49)
    private String protocol = "http";
    @JSONField(ordinal = 40)
    private String ip;
    @JSONField(ordinal = 50)
    private int port;
    @JSONField(ordinal = 51)
    private int appPort;
    @JSONField(ordinal = 52)
    private List<Integer> extPorts;
    @JSONField(ordinal = 55)
    @JsonProperty(index = 55)
    private int jmxPort;
    @JSONField(ordinal = 70)
    private int pid;
    @JSONField(ordinal = 75)
    @JsonProperty(index = 75)
    private String antId;
    /**
     * ant-demo-v20@ant
     */
    @JSONField(ordinal = 76)
    @JsonProperty(index = 76)
    private String name;
    @JSONField(ordinal = 77)
    @JsonProperty(index = 77)
    private String serviceId;
    @JSONField(ordinal = 80)
    @JsonProperty(index = 85)
    private String actionTitle;
    @JSONField(ordinal = 82)
    @JsonProperty(index = 82)
    private String actionDesc;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss", ordinal = 90)
    @JsonProperty(index = 90)

    private Date startTime = new Date();
    @JSONField(ordinal = 100)
    @JsonProperty(index = 100)
    private String zkServers;
    @JSONField(ordinal = 108)
    private String skynetHome;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss", ordinal = 110)
    private Date reloadTime = new Date();
    @JSONField(ordinal = 130)
    private Object nodeParam;
    @JSONField(ordinal = 134)
    private String plugin;
    @JSONField(ordinal = 138)
    private List<String> projectVersion;

    @JSONField(ordinal = 139)
    private Map<String, Object> metadata = new HashMap<>();

    @JSONField(ordinal = 140)
    private SampleState state = new SampleState();
    @JSONField(ordinal = 145)
    private AntNodeUpType up = AntNodeUpType.LOADING;


    public AntNodeState() {
    }

    public AntNodeState(AppContext appContext, SkynetZkProperties skynetZkProperties) {

        if (appContext == null) {
            return;
        }

        this.uuid = appContext.getUuid();
        this.clusterName = skynetZkProperties.getClusterName();
        this.hostName = appContext.getHostName();
        this.os = appContext.getOs();
        this.ip = appContext.getIpAddress();
        this.pid = appContext.getPid();
        this.ssl = appContext.isSsl();
        this.port = appContext.getPort();
//        this.jmxPort = appContext.getJmxPort();

        this.antId = appContext.getAntId();
        this.name = appContext.getNodeName();

        this.startTime = appContext.getStartTime();
        this.zkServers = skynetZkProperties.getServerList();
        this.skynetHome = appContext.getSkynetHome();

        this.reloadTime = new Date();
        this.projectVersion = appContext.getProjectVersionList();

        // 获取plugin下的目录插件列表
        this.plugin = appContext.getSkynetProperties().getPlugin();
        this.actionTitle = appContext.getSkynetProperties().getActionTitle();
        this.actionDesc = appContext.getSkynetProperties().getActionDesc();
        this.serviceId = appContext.getSkynetProperties().getActionCode();
        this.state = new SampleState();
    }

    @JSONField(ordinal = 120)
    public String getUpTime() {
        return DateUtil.countDateTime(startTime, new Date());
    }

    @JSONField(serialize = false, deserialize = false)
    public String getPrefix() {
        return String.format("%s@%s@pid:%d", this.name, this.ip, this.pid);
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this, JSONWriter.Feature.BrowserCompatible, JSONWriter.Feature.PrettyFormat);
    }

}
