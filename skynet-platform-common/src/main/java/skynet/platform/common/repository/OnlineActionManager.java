package skynet.platform.common.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import skynet.boot.common.OsUtil;
import skynet.boot.metrics.SkynetMetricsService;
import skynet.boot.metrics.domain.MetricsLabel;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 在线服务
 *
 * <pre>
 * 服务上下线会自动更新
 * 实现原理：zk状态汇报 和 zk 心跳机制
 * </pre>
 *
 * <AUTHOR> [2016年12月8日上午9:11:30]
 */
@Slf4j
public class OnlineActionManager {

    private final IAntConfigService antConfigService;
    private final SkynetMetricsService skynetMetricsService;

    public OnlineActionManager(IAntConfigService antConfigService, SkynetMetricsService skynetMetricsService) {
        this.antConfigService = antConfigService;
        this.skynetMetricsService = skynetMetricsService;
    }

    /**
     * 获取指定服务坐标的 服务节点
     *
     * @param actionPoint
     * @return
     */
    public synchronized List<AntActionStatus> getAllNodes(String actionPoint) {

        Assert.hasText(actionPoint, "actionPoint is blank.");
        long cost = System.currentTimeMillis();
        log.debug("getOnlineActionNodeStatus actionPoint:{};", actionPoint);
        Map<String, AntActionStatus> onlineNodes = antConfigService.getOnlineActionNodeStatus(actionPoint, null);
        cost = (System.currentTimeMillis() - cost);
        log.debug("getOnlineActionNodeStatus actionPoint:{};size:{};cost:{}ms;", actionPoint, onlineNodes.size(), cost);

        skynetMetricsService.timer("fetch.online.action.cost.ms", cost);
        skynetMetricsService.counterIncrement("fetch.online.action", new MetricsLabel("action", actionPoint));

        return new ArrayList<>(onlineNodes.values());
    }

    /**
     * 随机获取Action节点
     *
     * @param actionPoint
     * @return
     */
    public synchronized AntActionStatus getNode(String actionPoint) throws Exception {
        Assert.hasText(actionPoint, "actionPoint is blank.");
        return getNode(actionPoint, false);
    }

    /**
     * 获取随机的一个代理服务
     *
     * @param actionPoint
     * @param isFirstLocalIP 是否本机优先
     * @return
     */
    public synchronized AntActionStatus getNode(String actionPoint, boolean isFirstLocalIP) {
        Assert.hasText(actionPoint, "actionPoint is blank.");

        List<AntActionStatus> nodeList = this.getAllNodes(actionPoint);
        // 优先本地IP服务
        List<AntActionStatus> localList = isFirstLocalIP ? nodeList.stream().filter(x -> OsUtil.isLocalIP(x.getIp())).toList() : nodeList;
        if (localList.isEmpty()) {
            localList = nodeList;
        }
        return (!localList.isEmpty()) ? localList.get(new Random().nextInt(localList.size())) : null;
    }
}
