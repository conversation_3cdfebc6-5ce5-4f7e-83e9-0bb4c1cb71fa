package skynet.platform.common.repository.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
@Accessors(chain = true)
public class SkynetVersion extends Jsonable {

    private String clusterName;
    private String version;
    private String title;

    @Override
    public String toString() {
        return super.toString();
    }
}
