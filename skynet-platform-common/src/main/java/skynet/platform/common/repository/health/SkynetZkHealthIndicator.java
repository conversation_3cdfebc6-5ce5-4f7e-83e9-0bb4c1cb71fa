package skynet.platform.common.repository.health;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.NodeDescription;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
public class SkynetZkHealthIndicator extends AbstractHealthIndicator {

    private final IAntConfigService antConfigService;
    private NodeDescription nodeDescription = null;


    public SkynetZkHealthIndicator(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    @Override
    protected void doHealthCheck(Health.Builder builder) throws Exception {

        log.debug("ZkHealthIndicator doHealthCheck..");

        // 采用异步线程去获取 健康状态数据，以防 获取超时
        CountDownLatch countDownLatch = new CountDownLatch(1);
        nodeDescription = null;
        new Thread(() -> {
            log.debug("ZkHealthIndicator doHealthCheck begin..");

            //通过过去skynet的版本信息，检测zk是否正常
            try {
                nodeDescription = antConfigService.getSkynet();
            } catch (Exception e) {
                e.printStackTrace();
            }

            log.debug("ZkHealthIndicator doHealthCheck end.");

            countDownLatch.countDown();
        }).start();

        // 等待5秒
        countDownLatch.await(5000, TimeUnit.MILLISECONDS);

        log.debug("skynet:{}", nodeDescription);

        try {
            if (nodeDescription != null) {
                builder.up();
            } else {
                builder.down();
            }

            SkynetZkProperties skynetZKProperties = antConfigService.getSkynetZKProperties();
            builder.withDetail("server_list", skynetZKProperties.getServerList());
            builder.withDetail("session_timeout_ms", skynetZKProperties.getSessionTimeout());
            builder.withDetail("connect_timeout_ms", skynetZKProperties.getConnectionTimeout());
            builder.withDetail("cluster_name", antConfigService.getClusterName());

        } catch (Exception e) {
            builder.down(e);
        }
    }
}
