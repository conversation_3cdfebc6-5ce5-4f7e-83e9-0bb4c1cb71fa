/**
 *
 */
package skynet.platform.common.repository.config.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.platform.common.domain.*;
import skynet.platform.common.exception.ActionNotExistException;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.observer.*;
import skynet.platform.common.repository.domain.*;
import skynet.platform.common.repository.exception.InvalidActionPointException;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Skynet Ant 在Zookeeper中配置接口 实现
 *
 * <AUTHOR>
 */
@Slf4j
public class AntConfigServiceImpl implements IAntConfigService {

    private static final String REF_PATTERN_STR = "\\{\\$ref@setting:([A-Za-z0-9_]+)\\}";
    private static final Pattern REF_PATTERN = Pattern.compile(REF_PATTERN_STR);

    /**
     * Action参数缓存 - 使用LoadingCache自动加载机制
     * 缓存策略：写入后2秒过期，最大100个条目，并发级别5
     */
    private final LoadingCache<String, AntActionParam> antActionParamCache;

    /**
     * 服务器参数缓存 - 使用LoadingCache自动加载机制
     * 缓存策略：访问后15秒过期，最大100个条目，并发级别5
     * 由于AntServerParam参数的修改都是集中在一起，所以此处缓存周期设置相对长一些
     */
    private final LoadingCache<String, AntServerParam> antServerParamCache;

    /**
     * /skynet
     */
    public final String SKYNET_ROOT_PATH;
    /**
     * /skynet/cluster
     */
    public final String SKYNET_CLUSTER_PATH;
    /**
     * /skynet/plugin
     */
    public final String SKYNET_PLUGIN_PATH;
    /**
     * /skynet/setting
     */
    public final String SKYNET_SETTINGS_PATH;
    /**
     * /skynet/cluster/online
     */
    public final String SKYNET_ONLINE_PATH;
    /**
     * /skynet/cluster/online/action
     */
    public final String SKYNET_ONLINE_ACTION_PATH;
    /**
     * /skynet/cluster/topology
     */
    public final String SKYNET_TOPOLOGY_PARAMS_PATH;

    public final String SKYNET_TAGS_PATH;


    private final String clusterName;

    private final String currentActionPlugin;
    private final String currentActionCode;
    private final SkynetZkProperties skynetZKProperties;
    private final ZkConfigService zkConfigService;

    public AntConfigServiceImpl(ZkConfigService zkConfigService, SkynetProperties skynetProperties) throws Exception {

        if (StringUtils.isBlank(skynetProperties.getActionPoint())) {
            throw new Exception("not config [" + SkynetProperties.SKYNET_ACTION_POINT_KEY + "]");
        }

        this.zkConfigService = zkConfigService;
        this.skynetZKProperties = zkConfigService.getZkProperties();

        this.clusterName = skynetZKProperties.getClusterName();

        log.debug("-----------------------------------------");
        log.debug("zookeeper_server:\t{}", skynetZKProperties.getServerList());
        log.debug("zookeeper_cluster:\t{}", clusterName);
        log.debug("-----------------------------------------\n");

        currentActionPlugin = skynetProperties.getPlugin();
        currentActionCode = skynetProperties.getActionCode();

        SKYNET_ROOT_PATH = "/" + clusterName;
        SKYNET_CLUSTER_PATH = SKYNET_ROOT_PATH + "/cluster";
        SKYNET_PLUGIN_PATH = SKYNET_ROOT_PATH + "/plugin";
        SKYNET_SETTINGS_PATH = SKYNET_ROOT_PATH + "/setting";
        SKYNET_ONLINE_PATH = SKYNET_CLUSTER_PATH + "/online";
        SKYNET_ONLINE_ACTION_PATH = SKYNET_ONLINE_PATH + "/action";
        SKYNET_TOPOLOGY_PARAMS_PATH = SKYNET_CLUSTER_PATH + "/topology";
        SKYNET_TAGS_PATH = SKYNET_CLUSTER_PATH + "/tags";

        // 初始化Action参数缓存，使用LoadingCache自动加载机制
        this.antActionParamCache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(2, TimeUnit.SECONDS)
                .concurrencyLevel(5)
                .recordStats()
                .build(new CacheLoader<String, AntActionParam>() {
                    @Override
                    public AntActionParam load(@NonNull String key) throws Exception {
                        log.debug("Loading AntActionParam from ZK for key: {}", key);
                        return loadActionParamFromZk(key);
                    }
                });

        // 初始化服务器参数缓存，使用LoadingCache自动加载机制
        // 由于AntServerParam参数的修改都是集中在一起，所以此处缓存周期设置相对长一些
        this.antServerParamCache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterAccess(15, TimeUnit.SECONDS)
                .concurrencyLevel(5)
                .recordStats()
                .build(new CacheLoader<String, AntServerParam>() {
                    @Override
                    public AntServerParam load(@NonNull String ip) throws Exception {
                        log.debug("Loading AntServerParam from ZK for IP: {}", ip);
                        return loadServerParamFromZk(ip);
                    }
                });


    }

    /**
     * @return the zkConfigService
     */
    @Override
    public ZkConfigService getZkConfigService() {
        return zkConfigService;
    }

    /**
     * XXX:===========================================================
     **/


    @Override
    public SkynetZkProperties getSkynetZKProperties() {
        return this.skynetZKProperties;
    }

    @Override
    public String getZookeeperServers() {
        return skynetZKProperties.getServerList();
    }

    @Override
    public String getClusterName() {
        return clusterName;
    }

    @Override
    public String getPluginName() {
        return currentActionPlugin;
    }

    @Override
    public String getActionName() {
        return currentActionCode;
    }

    @Override
    public String getTopologyPath() {
        return SKYNET_TOPOLOGY_PARAMS_PATH;
    }

    @Override
    public String getOnlineActionPath() {
        return SKYNET_ONLINE_ACTION_PATH;
    }

    /**
     * 获取版本信息
     *
     * @return
     */
    @Override
    public SkynetVersion getVersion() {
        String path = String.format("%s/version", SKYNET_ROOT_PATH);
        String v = this.getData(path);
        if (StringUtils.isBlank(v)) {
            return null;
        }
        path = String.format("%s/_name", SKYNET_ROOT_PATH);
        String title = this.getData(path);
        SkynetVersion version = new SkynetVersion();
        version.setClusterName(clusterName);
        version.setTitle(title);
        version.setVersion(v);
        return version;
    }

    @Override
    public String getData(String path) {
        return this.getData(path, null);
    }

    @Override
    public String getData(String path, Observer dataObserver) {
        return this.zkConfigService.exists(path) ? zkConfigService.getData(path, dataObserver) : null;
    }

    @Override
    public void setData(String path, String value) {
        this.zkConfigService.putNode(path, value);
    }

    /**
     * 删除节点数据
     *
     * @param path
     */
    @Override
    public void delData(String path) {
        this.zkConfigService.deleteNode(path);
    }

    /**
     * 得到子节点的数据 【子节点名称(包含完整路径)和数据】
     *
     * @param path         节点路径，如：/skynet
     * @param dataObserver 子节点个数或每个子节点数据观察器，响应节点的变化
     * @return 子节点名称(包含完整路径)和数据
     */
    @Override
    public Map<String, String> getChildrenWithData(String path, Observer dataObserver) {

        if (!zkConfigService.exists(path)) {
            return new HashMap<>(0);
        }

        return zkConfigService.getChildrenWithData(path, dataObserver);
    }

    /**
     * 得到子节点的数据 【子节点名称(包含简短路径)和数据】
     *
     * @param path         节点路径，如：/skynet
     * @param dataObserver 子节点个数或每个子节点数据观察器，响应节点的变化
     * @return 子节点名称(包含简短路径)和数据
     */
    @Override
    public Map<String, String> getChildrenWithData2(String path, Observer dataObserver) {

        Map<String, String> settings = this.getChildrenWithData(path, dataObserver);
        Map<String, String> resultMap = new TreeMap<>();

        for (Entry<String, String> item : settings.entrySet()) {
            String key = item.getKey().replace(path + "/", "");
            if (!key.startsWith("_")) {
                resultMap.put(key.trim(), item.getValue() != null ? item.getValue().trim() : item.getValue());
            }
        }
        return resultMap;
    }

    /** XXX:=========================================================== **/

    /**
     * 得到所有子节点的数据（递归获取所有的孩子节点数据）
     *
     * @param path 节点路径，如：/skynet
     * @return 子节点名称(包含简短路径)和数据
     */
    @Override
    public Map<String, String> getAllChildrenWithData2(String path) {

        Map<String, String> settings = getZkConfigService().exportData(path);
        Map<String, String> resultMap = new TreeMap<>();

        for (Entry<String, String> item : settings.entrySet()) {
            String key = item.getKey().substring(item.getKey().indexOf("=") + 1);
            if (!key.startsWith("_")) {
                resultMap.put(key.trim(), item.getValue() != null ? item.getValue().trim() : null);
            }
        }
        return resultMap;
    }

    @Override
    public void close() throws Exception {
        log.debug("close AntConfigService ...");
        this.zkConfigService.close();
    }

    /**
     * 获取在线状态路径
     *
     * @return
     */
    @Override
    public String getSkynetOnlinePath() {
        return SKYNET_ONLINE_PATH;
    }

    @Override
    public List<String> reportActionNode(AntActionStatus actionStatus) {

        List<String> nodes = new ArrayList<>();
        String path = String.format("%s/%s/%s", SKYNET_ONLINE_PATH, "action", actionStatus.getAction());

        path = String.format("%s/%s", path, actionStatus.getNodeName());
        // 如果存在，先删除，再添加
        if (zkConfigService.exists(path)) {
            zkConfigService.deleteNode(path);
        }
        zkConfigService.putEphemeralNode(path, JSON.toJSONString(actionStatus));
        nodes.add(path);
        return nodes;
    }

    /**
     * 取消节点注册
     * 批量删除指定的ZK节点路径
     *
     * @param nodes 要删除的节点路径集合
     */
    @Override
    public void cancelNode(Collection<String> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            log.debug("No nodes to cancel");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (String path : nodes) {
            if (StringUtils.isBlank(path)) {
                log.warn("Skipping blank node path");
                continue;
            }

            try {
                log.debug("Canceling node path: {}", path);
                zkConfigService.deleteNode(path);
                successCount++;
            } catch (Exception e) {
                log.warn("Failed to cancel node path: {}", path, e);
                failCount++;
            }
        }

        log.info("Cancel nodes completed: {} success, {} failed", successCount, failCount);
    }

    /**
     * TODO: 是否是全路径，还是只有一个Key
     */
    @Override
    public Map<String, String> getOnlineActionNodes(String actionPoint, OnlineNodeObserver observer) {
        String path = String.format("%s/%s/%s", SKYNET_ONLINE_PATH, "action", actionPoint);
        return this.getChildrenWithData2(path, observer);
    }

    /**
     * 获取skynet信息
     *
     * @return
     */
    @Override
    public NodeDescription getSkynet() {
        String name = zkConfigService.getData(String.format("%s/_name", SKYNET_ROOT_PATH));
        String desc = zkConfigService.getData(String.format("%s/_desc", SKYNET_ROOT_PATH));
        return new NodeDescription(clusterName, name, desc);
    }

    /**
     * XXX:===========================================================
     **/

    @Override
    public String getSkynetPluginPath() {
        return SKYNET_PLUGIN_PATH;
    }

    /**
     * 获取 action 路径 /skynet/plugin/{plugin}/action/{actionName}
     *
     * @param plugin
     * @param action
     * @return
     */
    @Override
    public String getActionPath(String plugin, String action) {
        // /skynet/plugin/{plugin}/action/{actionName}
        return String.format("%s/%s/action/%s", SKYNET_PLUGIN_PATH, plugin, action);
    }

    @Override
    public String getActionPath(String actionId) throws InvalidActionPointException {
        int index = actionId.indexOf('@');
        if (index < 0) {
            throw new InvalidActionPointException("invalid action point : " + actionId);
        }
        String actionCode = actionId.substring(0, index);
        String plugin = actionId.substring(index + 1);
        return getActionPath(plugin, actionCode);
    }

    /**
     * 获取所有插件列表
     * 从ZK中获取所有插件信息并按索引和名称排序
     *
     * @return 插件描述列表，按索引和名称排序
     */
    @Override
    public List<NodeDescription> getPlugins() {
        try {
            List<String> plugins = zkConfigService.getChildren(SKYNET_PLUGIN_PATH);
            if (plugins == null || plugins.isEmpty()) {
                log.debug("No plugins found in path: {}", SKYNET_PLUGIN_PATH);
                return new ArrayList<>();
            }

            List<NodeDescription> objList = new ArrayList<>();

            for (String plugin : plugins) {
                // 排除下划线开头的系统节点
                if (StringUtils.isBlank(plugin) || plugin.startsWith("_")) {
                    continue;
                }

                try {
                    NodeDescription pluginNode = getPlugin(plugin.trim());
                    if (pluginNode != null) {
                        objList.add(pluginNode);
                    }
                } catch (Exception e) {
                    log.warn("Failed to get plugin info for: {}", plugin, e);
                }
            }

            // 优化排序：先按索引排序，再按名称排序（合并为一次排序操作）
            return objList.stream()
                    .sorted(Comparator.comparing(NodeDescription::getIndex)
                            .thenComparing(NodeDescription::getName, Comparator.nullsLast(String::compareTo)))
                    .toList();

        } catch (Exception e) {
            log.error("Failed to get plugins list", e);
            return new ArrayList<>();
        }
    }

    @Override
    public NodeDescription getPlugin(String plugin) {

        Assert.hasText(plugin, "the plugin is blank.");
        plugin = plugin.trim();
        NodeDescription nodeDescription = new NodeDescription();
        nodeDescription.setCode(plugin);

        String path = String.format("%s/%s", SKYNET_PLUGIN_PATH, plugin);
        List<String> pluginInfoList = zkConfigService.getChildren(path);
        if (pluginInfoList.isEmpty()) {
            log.debug("pluginInfoList is Empty.[plugin={}]", plugin);
            return null;
        }
        for (String pluginInfo : pluginInfoList) {

            String info = zkConfigService.getData(String.format("%s/%s", path, pluginInfo));
            // 获取plugin基本信息
            switch (pluginInfo) {
                case "_name":
                    nodeDescription.setName(info);
                    break;
                case "_desc":
                    nodeDescription.setDesc(info);
                    break;
                case "_version":
                    nodeDescription.setVersion(info);
                    break;
                case "_index":
                    if (StringUtils.isNumeric(info)) {
                        nodeDescription.setIndex(Integer.parseInt(info));
                    }
                    break;
                default:
            }
        }
        return nodeDescription;
    }

    @Override
    public NodeDescription getPlugin() {
        return getPlugin(currentActionPlugin);
    }

    /**
     * 获取指定插件下的所有Action列表
     *
     * @param plugin 插件名称
     * @return Action描述列表，按索引和名称排序
     * @throws AntException 当插件不存在时抛出异常
     */
    @Override
    public List<NodeDescription> getActions(String plugin) {
        if (StringUtils.isBlank(plugin)) {
            throw new AntException("Plugin name cannot be blank");
        }

        // 验证插件是否存在
        NodeDescription pluginNodeDescription = this.getPlugin(plugin.trim());
        if (pluginNodeDescription == null) {
            throw new AntException(String.format("The plugin [%s] not exist.", plugin));
        }

        try {
            List<NodeDescription> objList = new ArrayList<>();
            String path = String.format("%s/%s/action", SKYNET_PLUGIN_PATH, plugin.trim());
            List<String> actions = zkConfigService.getChildren(path);

            if (actions == null || actions.isEmpty()) {
                log.debug("No actions found for plugin: {}", plugin);
                return objList;
            }

            for (String action : actions) {
                // 排除下划线开头的系统节点
                if (StringUtils.isBlank(action) || action.startsWith("_")) {
                    continue;
                }

                try {
                    String actionTrimmed = action.trim();
                    String name = this.getData(String.format("%s/%s/_atitle", path, actionTrimmed));
                    String desc = this.getData(String.format("%s/%s/_desc", path, actionTrimmed));
                    String index = this.getData(String.format("%s/%s/_index", path, actionTrimmed));

                    NodeDescription nodeDescription = new NodeDescription(actionTrimmed, name, desc);

                    // 安全解析索引值
                    if (StringUtils.isNumeric(index)) {
                        try {
                            nodeDescription.setIndex(Integer.parseInt(index.trim()));
                        } catch (NumberFormatException e) {
                            log.warn("Invalid index value for action {}@{}: {}", actionTrimmed, plugin, index);
                        }
                    }

                    objList.add(nodeDescription);
                } catch (Exception e) {
                    log.warn("Failed to get action info for {}@{}", action, plugin, e);
                }
            }

            // 优化排序：合并为一次排序操作
            return objList.stream()
                    .sorted(Comparator.comparing(NodeDescription::getIndex)
                            .thenComparing(NodeDescription::getName, Comparator.nullsLast(String::compareTo)))
                    .toList();

        } catch (Exception e) {
            log.error("Failed to get actions for plugin: {}", plugin, e);
            throw new AntException(String.format("Failed to get actions for plugin [%s]: %s", plugin, e.getMessage()));
        }
    }

    @Override
    public NodeDescription getAction(String plugin, String action) {
        log.debug("getAction:plugin={};action={}", plugin, action);

        NodeDescription pluginNode = this.getPlugin(plugin);
        if (pluginNode == null) {
            log.debug("getAction:plugin={};action={};pluginNode=null", plugin, action);
            return null;
        }
        String path = this.getActionPath(plugin, action);

        String name = this.getData(String.format("%s/_atitle", path));
        if (StringUtils.isBlank(name)) {
            log.debug("getAction:plugin={};action={};name is blank.", plugin, action);
            return null;
        }

        String desc = this.getData(String.format("%s/%s/_desc", path, action));
        return new NodeDescription(action, name, desc);
    }

    /**
     * <pre>
     * 如果不存将返回Null
     * </pre>
     *
     * @param actionPoint
     * @return
     */
    @Override
    public NodeDescription getAction(String actionPoint) {
        ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
        return getAction(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
    }

    /**
     * 获取 集群级别 Setting 路径
     *
     * @return
     */

    @Override
    public String getSettingPath() {
        return SKYNET_SETTINGS_PATH;
    }

    @Override
    public String getSettingPath(String plugin, String action) {
        if (StringUtils.isBlank(plugin)) {
            return SKYNET_SETTINGS_PATH;
        }
        if (StringUtils.isBlank(action)) {
            return String.format("%s/%s/setting", SKYNET_PLUGIN_PATH, plugin);
        } else {
            return String.format("%s/%s/action/%s/setting", SKYNET_PLUGIN_PATH, plugin, action);
        }
    }

    @Override
    public String getSetting(String key, Observer observer) {
        return this.getSetting(currentActionPlugin, key, observer);
    }

    @Override
    public String getSetting(final String plugin, String key, final Observer observer) {

        // SKYNET_ROOT_PATH/plugin/{plugin}/setting/{key}";
        final String path = getPluginSettingPath(plugin, key);
        String val = this.getData(path, observer);
        if (StringUtils.isBlank(val)) {
            return null;
        }
        return val;
    }

    @Override
    public void setSetting(String plugin, String key, String value) {
        final String path = getPluginSettingPath(plugin, key);
        this.setData(path, value);
    }

    /**
     * 获取配置值 如果key 值为空，或没有配置，直接返回 false
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key 配置
     * @return 值
     */

    @Override
    public boolean getBoolSetting(String key, BooleanObserver observer) {
        return getBoolSetting(currentActionPlugin, key, observer);
    }

    /**
     * 获取 某插件下得 配置项 值 如果key 值为空，或没有配置，直接返回 false
     *
     * @param plugin
     * @param key
     * @param observer
     * @return
     */
    @Override
    public boolean getBoolSetting(String plugin, String key, BooleanObserver observer) {
        String strValue = getSetting(plugin, key, observer);
        return "true".equalsIgnoreCase(strValue);
    }

    /**
     * 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     */
    @Override
    public int getIntegerSetting(String key, IntegerObserver observer) {

        return getIntegerSetting(currentActionPlugin, key, observer);
    }

    /**
     * 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     */
    @Override
    public int getIntegerSetting(String plugin, String key, IntegerObserver observer) {

        String strValue = getSetting(plugin, key, observer);

        if (StringUtils.isBlank(strValue) || !StringUtils.isNumeric(strValue)) {
            return Integer.MAX_VALUE;
        }
        return Integer.parseInt(strValue);
    }

    /**
     *
     */
    @Override
    public <T> T getTSetting(String key, Class<T> clazz, TObserver<T> observer) {
        return this.getTSetting(currentActionPlugin, key, clazz, observer);
    }

    @Override
    public <T> T getTSetting(String plubin, String key, Class<T> clazz, TObserver<T> tObserver) {

        String strValue = getSetting(plubin, key, tObserver);
        T setting = null;
        if (StringUtils.isNotBlank(strValue)) {
            try {
                setting = JSON.parseObject(strValue, clazz);
            } catch (Exception e) {
                log.error(String.format("getTSetting is error. key:%s; value json: %s", key, strValue), e);
            }
        }
        return setting;
    }

    @Override
    public Map<String, String> getSettings(Observer observer) {
        return getSettings(currentActionPlugin, observer);
    }

    @Override
    public Map<String, String> getSettings(String plugin, Observer observer) {

        String path = StringUtils.isBlank(plugin) ? SKYNET_SETTINGS_PATH : String.format("%s/plugin/%s/setting", SKYNET_ROOT_PATH, plugin);

        Map<String, String> settings = zkConfigService.getChildrenWithData(path, observer);
        Map<String, String> map = new TreeMap<>();
        for (Entry<String, String> item : settings.entrySet()) {
            map.put(item.getKey().replace(SKYNET_SETTINGS_PATH + "/", ""), item.getValue());
        }
        return map;
    }

    private String getPluginSettingPath(String plugin, String key) {
        // skynet/plugin/{plugin}/setting/{key}";
        // 如果plugin 为空 或不存在 ，就 获取全局的配置，否则具体的 plugin下得配置, 这是采用继承的机制
        if (StringUtils.isNotBlank(plugin)) {
            String path = String.format("%s/plugin/%s/setting/%s", SKYNET_ROOT_PATH, plugin, key);
            if (zkConfigService.exists(path)) {
                return path;
            }
        }

        return String.format("%s/%s", SKYNET_SETTINGS_PATH, key);
    }

    /**
     * XXX: AntConfigService4Logger===========================================================
     **/

    /**
     * ----- Properties & Logger- begin -------------------------------------------------------------------------
     */

    @Override
    public Map<String, String> getGlobalProperties() {
        return getProperties(null, null, null);
    }

    @Override
    public Map<String, String> getGlobalLoggers() throws Exception {
        return getLoggers(null, null, null);
    }

    @Override
    public Map<String, String> getProperties(String plugin, String action, Observer observer) {
        String path = getPropertyPath(plugin, action);
        return getChildrenWithData2(path, observer);
    }

    @Override
    public void setProperty(String plugin, String action, String property, String value) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getPropertyPath(plugin, action), property);
        zkConfigService.putNode(path, value);
    }

    @Override
    public void delProperty(String plugin, String action, String property) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getPropertyPath(plugin, action), property);
        zkConfigService.deleteNode(path);
    }

    @Override
    public Map<String, String> getLoggers(String plugin, String action, Observer observer) {
        String path = getLoggerPath(plugin, action);
        return getChildrenWithData2(path, observer);
    }

    @Override
    public void setLogger(String plugin, String action, String property, String value) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getLoggerPath(plugin, action), property);
        zkConfigService.putNode(path, value);
    }

    @Override
    public void delLogger(String plugin, String action, String property) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getLoggerPath(plugin, action), property);
        zkConfigService.deleteNode(path);
    }

    private String getLoggerPath(String plugin, String action) {
        return getPath(plugin, action, "_logger");
    }

    private String getPropertyPath(String plugin, String action) {
        return getPath(plugin, action, "_properties");
    }

    private String getPath(String plugin, String action, String contextPath) {
        String path;
        if (StringUtils.isNotEmpty(plugin) && StringUtils.isNotEmpty(action)) {// 设置action
            plugin = plugin.trim();
            action = action.trim();
            checkAction(plugin, action);
            path = String.format("%s/%s", this.getActionPath(plugin, action), contextPath);
        } else if (StringUtils.isNotEmpty(plugin)) {// 设置plugin
            plugin = plugin.trim();
            checkPlugin(plugin);
            path = String.format("%s/%s/setting/%s", SKYNET_PLUGIN_PATH, plugin, contextPath);
        } else {
            path = String.format("%s/%s", SKYNET_SETTINGS_PATH, contextPath);
        }
        return path;
    }

    private void checkPlugin(String plugin) {
        NodeDescription pluginNode = this.getPlugin(plugin);
        if (pluginNode == null) {
            throw new AntException("the plugin [%s] not exist.", plugin);
        }
    }

    private void checkAction(String plugin, String action) {
        NodeDescription actionNodeDescription = this.getAction(plugin, action);
        if (actionNodeDescription == null) {
            throw new AntException(String.format("the action [%s@%s] not exist", action, plugin));
        }
    }

    /**
     * ----- Properties & Logger- end -------------------------------------------------------------------------
     */

    @Override
    public Map<String, AntActionStatus> getOnlineActionNodeStatus(String actionPoint, OnlineActionStatusObserver observer) {
        Map<String, AntActionStatus> ret = new TreeMap<>();
        String path = String.format("%s/%s", SKYNET_ONLINE_ACTION_PATH, actionPoint);
        Map<String, String> children = this.zkConfigService.getChildrenWithData(path, observer);
        if (children == null) {
            return ret;
        }
        for (Entry<String, String> entry : children.entrySet()) {
            String key = StringUtils.substringAfterLast(entry.getKey(), "/");
            String value = entry.getValue();
            if (!key.startsWith("_") && StringUtils.isNoneBlank(value) && value.trim().startsWith("{")) {
                try {
                    AntActionStatus status = JSON.parseObject(value, AntActionStatus.class);
                    ret.put(key, status);
                } catch (Throwable t) {
                    //为了兼容 2.1.1009以前的格式转换。
                    JSONObject statusObj = JSON.parseObject(value);
                    if (statusObj.containsKey("start")) {
                        statusObj.replace("start", Calendar.getInstance().get(Calendar.YEAR) + "-" + statusObj.getString("start"));
                        try {
                            AntActionStatus status = JSON.parseObject(statusObj.toJSONString(), AntActionStatus.class);
                            ret.put(key, status);
                        } catch (Throwable t2) {
                            log.error("fail to parse json : key[{}] , value[{}], class:{}", key, value, AntActionStatus.class);
                        }
                    } else {
                        log.error("fail to parse json : key[{}] , value[{}], class:{}", key, value, AntActionStatus.class);
                    }
                }
            }
        }
        return ret;
    }

    @Override
    public List<String> getOnlineActionNames(OnlineActionObserver observer) {
        Map<String, String> settings = this.zkConfigService.getChildrenWithData(SKYNET_ONLINE_ACTION_PATH, observer);

        List<String> result = new ArrayList<>();

        if (settings != null) {
            for (Entry<String, String> entry : settings.entrySet()) {
                String value = entry.getValue();
                String key = StringUtils.substringAfterLast(entry.getKey(), "/");
                if (StringUtils.isBlank(value) && !key.startsWith("_")) {
                    result.add(key);
                }
            }
        }
        return result;
    }

    @Override
    public String filterRefSetting(String srcString, final String path, final Observer observer) {
        return this.filterRefSetting(currentActionPlugin, srcString, path, observer);
    }

    @Override
    public String filterRefSetting(String plugin, String srcString, final String path, final Observer observer) {

        // 替换 {$ref@setting:setting_key} debug by lyhu 2015年02月15日01:08:18
        Map<String, String> refDict = new HashMap<>();
        Matcher matcher = REF_PATTERN.matcher(srcString);
        while (matcher.find()) {
            String key = matcher.group(1);
            if (!refDict.containsKey(key)) {
                refDict.put(key, matcher.group(0));
            }
        }
        for (Entry<String, String> item : refDict.entrySet()) {
            String key = item.getKey();
            String value = this.getSetting(plugin, key, observer == null ? null : (o, arg) -> {
                String s = getData(path);
                observer.update(o, s);
            });
            if (StringUtils.isBlank(value)) {
                value = StringUtils.EMPTY;
                log.info(String.format("the setting key:[%s] value is not setting or blank.", key));
            }
            srcString = srcString.replace(item.getValue(), value);
        }
        return srcString;
    }


    //region

    /**
     * XXX:===========================================================
     **/

    /**
     * 获取服务器参数信息
     * 优先从LoadingCache中获取，如果缓存中不存在则自动从ZK加载
     *
     * @param ip 服务器IP地址
     * @return 服务器参数的克隆对象，如果不存在则返回null
     */
    @Override
    public AntServerParam getServerParam(String ip) {
        try {
            // 使用LoadingCache自动加载机制，如果缓存中不存在会自动调用CacheLoader加载
            AntServerParam antServerParam = antServerParamCache.get(ip);
            log.debug("Get AntServerParam from cache for IP: {}", ip);
            // 返回克隆对象以避免外部修改影响缓存数据
            return antServerParam != null ? antServerParam.clone() : null;
        } catch (Exception e) {
            log.error("Failed to get AntServerParam for IP: {}", ip, e);
            return null;
        }
    }

    /**
     * 获取服务器参数信息（带观察器）
     * 直接从ZK获取数据，不使用缓存，适用于需要监听数据变化的场景
     *
     * @param ip                  服务器IP地址
     * @param serverParamObserver 服务器参数变化观察器
     * @return 服务器参数对象，如果不存在则返回null
     */
    @Override
    public AntServerParam getServerParam(String ip, ServerParamObserver serverParamObserver) {
        if (StringUtils.isBlank(ip)) {
            log.warn("IP address is blank when getting server param");
            return null;
        }

        String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip.trim());
        String jsonData = this.getData(path, serverParamObserver);

        if (StringUtils.isBlank(jsonData)) {
            log.debug("No server param data found for IP: {}", ip);
            return null;
        }

        try {
            AntServerParam antParam = JSON.parseObject(jsonData, AntServerParam.class);
            antParam.setIp(ip.trim());
            return antParam;
        } catch (Exception e) {
            log.error("Failed to parse server param JSON for IP: {}, data: {}", ip, jsonData, e);
            return null;
        }
    }

    /**
     * 删除服务器参数
     * 同时删除ZK中的数据和缓存中的数据
     *
     * @param ip 服务器IP地址
     * @return 删除成功返回true
     */
    @Override
    public boolean delServerParam(String ip) {
        if (StringUtils.isBlank(ip)) {
            log.warn("IP address is blank when deleting server param");
            return false;
        }

        try {
            String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip.trim());
            this.delData(path);
            // 从缓存中移除对应的条目
            antServerParamCache.invalidate(ip.trim());
            log.info("Successfully deleted server param for IP: {}", ip);
            return true;
        } catch (Exception e) {
            log.error("Failed to delete server param for IP: {}", ip, e);
            return false;
        }
    }

    /**
     * 设置服务器参数
     * 将数据保存到ZK并清除缓存以确保数据一致性
     *
     * @param antServerParam 服务器参数对象
     */
    @Override
    public void setServerParam(AntServerParam antServerParam) {
        if (antServerParam == null || StringUtils.isBlank(antServerParam.getIp())) {
            log.warn("Invalid server param: antServerParam is null or IP is blank");
            return;
        }

        try {
            String ip = antServerParam.getIp().trim();
            String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip);

            // 先清除缓存，确保下次获取时能获取到最新数据
            antServerParamCache.invalidate(ip);

            // 保存到ZK
            this.setData(path, antServerParam.toString());
            log.debug("Successfully set server param for IP: {}", ip);
        } catch (Exception e) {
            log.error("Failed to set server param for IP: {}", antServerParam.getIp(), e);
        }
    }

    @Override
    public List<AntServerParam> getServers() {
        Map<String, String> servers = this.getChildrenWithData2(SKYNET_TOPOLOGY_PARAMS_PATH, null);
        List<AntServerParam> objList = new ArrayList<>(servers.size());

        for (Entry<String, String> item : servers.entrySet()) {
            String ipAddress = item.getKey();
            AntServerParam antServerParam = this.getServerParam(ipAddress);
            if (antServerParam == null) {
                antServerParam = new AntServerParam();
                antServerParam.setIp(ipAddress);
            }

            objList.add(antServerParam);
        }
        objList = objList.stream().sorted(Comparator.comparing(AntServerParam::getIndex)).toList();
        return objList;
    }

    /**
     * XXX:===========================================================
     **/

    /**
     * 获取Action参数信息
     * 优先从LoadingCache中获取，如果缓存中不存在则自动从ZK加载
     *
     * @param plugin     插件名称
     * @param actionName Action名称
     * @return Action参数对象
     * @throws ActionNotExistException 当Action不存在时抛出异常
     */
    @Override
    public AntActionParam getActionParam(String plugin, String actionName) throws ActionNotExistException {
        if (StringUtils.isBlank(plugin) || StringUtils.isBlank(actionName)) {
            throw new ActionNotExistException(plugin, actionName);
        }

        try {
            // 构建缓存键：actionName@plugin
            String key = String.format("%s@%s", actionName.trim(), plugin.trim());

            // 使用LoadingCache自动加载机制，如果缓存中不存在会自动调用CacheLoader加载
            AntActionParam antActionParam = antActionParamCache.get(key);
            log.debug("Get ActionParam from cache for actionPoint: {}", key);

            return antActionParam;
        } catch (Exception e) {
            if (e.getCause() instanceof ActionNotExistException) {
                throw (ActionNotExistException) e.getCause();
            }
            log.error("Failed to get ActionParam for plugin: {}, action: {}", plugin, actionName, e);
            throw new ActionNotExistException(plugin, actionName);
        }
    }


    /**
     * 从ZK加载Action参数数据（CacheLoader使用）
     *
     * @param key 缓存键，格式为 "actionName@plugin"
     * @return Action参数对象
     * @throws ActionNotExistException 当Action不存在时抛出异常
     */
    private AntActionParam loadActionParamFromZk(String key) throws ActionNotExistException {
        if (StringUtils.isBlank(key) || !key.contains("@")) {
            throw new ActionNotExistException("unknown", "invalid-key-format");
        }

        // 解析key：actionName@plugin
        String[] parts = key.split("@", 2);
        String actionName = parts[0];
        String plugin = parts[1];

        try {
            // 获取Action服务参数
            String paramPath = String.format("%s/_param", this.getActionPath(plugin, actionName));
            String paramData = this.getData(paramPath, null);

            AntServiceParam antServiceParam = new AntServiceParam();
            // 有可能是非SkynetBoot，by lyhu 2018年07月19日17:50:19
            if (StringUtils.isNoneBlank(paramData)) {
                antServiceParam = JSON.parseObject(paramData, AntServiceParam.class);
            }

            // 构建完整的Action参数对象
            return buildActionParam(plugin, actionName, antServiceParam);
        } catch (Exception e) {
            log.error("Failed to load ActionParam from ZK for key: {}", key, e);
            throw new ActionNotExistException(plugin, actionName);
        }
    }

    /**
     * 从ZK加载服务器参数数据（CacheLoader使用）
     *
     * @param ip 服务器IP地址
     * @return 服务器参数对象，如果不存在则返回null
     * @throws Exception 当加载失败时抛出异常
     */
    private AntServerParam loadServerParamFromZk(String ip) throws Exception {
        if (StringUtils.isBlank(ip)) {
            throw new IllegalArgumentException("IP address cannot be blank");
        }

        String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip.trim());
        String jsonData = this.getData(path, null);

        if (StringUtils.isBlank(jsonData)) {
            // 对于LoadingCache，如果数据不存在，我们返回null
            // 但需要包装在一个特殊的异常中，以便调用方能够处理
            return null;
        }

        try {
            AntServerParam antParam = JSON.parseObject(jsonData, AntServerParam.class);
            antParam.setIp(ip.trim());
            return antParam;
        } catch (Exception e) {
            log.error("Failed to parse server param JSON for IP: {}, data: {}", ip, jsonData, e);
            throw new Exception("Failed to parse server param for IP: " + ip, e);
        }
    }

    /**
     * 构建Action参数对象
     * 从ZK中获取Action的各种配置信息并组装成完整的参数对象
     *
     * @param plugin          插件名称
     * @param actionName      Action名称
     * @param antServiceParam 服务参数对象
     * @return 完整的Action参数对象
     * @throws ActionNotExistException 当Action不存在时抛出异常
     */
    private AntActionParam buildActionParam(String plugin, String actionName, AntServiceParam antServiceParam) {
        String actionPath = this.getActionPath(plugin, actionName);
        Map<String, String> nodeList = this.getChildrenWithData(actionPath, null);

        if (nodeList.isEmpty()) {
            throw new ActionNotExistException(plugin, actionName);
        }

        // 从ZK节点数据中提取各种配置信息
        String title = nodeList.get(String.format("%s/_atitle", actionPath));
        String desc = nodeList.get(String.format("%s/_desc", actionPath));
        String bootType = nodeList.get(String.format("%s/_xboot_type", actionPath));
        String port = nodeList.get(String.format("%s/_port", actionPath));
        String javaOpts = nodeList.get(String.format("%s/_java_opts", actionPath));
        String xBootParam = nodeList.get(String.format("%s/_xboot_param", actionPath));

        // 构建Action参数对象
        AntActionParam antActionParam = new AntActionParam();
        antActionParam.setPlugin(plugin);
        antActionParam.setService(antServiceParam);
        antActionParam.setCode(actionName);
        antActionParam.setTitle(title);
        antActionParam.setJavaOpts(javaOpts);
        antActionParam.setDesc(desc);

        // 安全解析端口号，默认为0
        try {
            antActionParam.setPort(StringUtils.isBlank(port) ? 0 : Integer.parseInt(port.trim()));
        } catch (NumberFormatException e) {
            log.warn("Invalid port number for action {}@{}: {}, using default 0", actionName, plugin, port);
            antActionParam.setPort(0);
        }

        // 安全解析启动类型，默认为SkynetBoot
        try {
            BootType bootTypeEnum = StringUtils.isBlank(bootType) ?
                    BootType.SkynetBoot : BootType.valueOf(bootType.trim());
            antActionParam.setBootType(bootTypeEnum);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid boot type for action {}@{}: {}, using default SkynetBoot", actionName, plugin, bootType);
            antActionParam.setBootType(BootType.SkynetBoot);
        }

        // 安全解析启动参数
        try {
            BootParam xBoot = StringUtils.isBlank(xBootParam) ?
                    new BootParam() : JSON.parseObject(xBootParam, BootParam.class);
            antActionParam.setBootParam(xBoot);
        } catch (Exception e) {
            log.warn("Invalid boot param JSON for action {}@{}: {}, using default", actionName, plugin, xBootParam, e);
            antActionParam.setBootParam(new BootParam());
        }

        return antActionParam;
    }

    /** XXX:=========================================================== **/

    /**
     * tags 样例 ["one","two","three"]
     *
     * @return tagList
     */
    @Override
    public List<String> getTags() {
        String data = this.getData(SKYNET_TAGS_PATH);
        return JSON.parseObject(data, new TypeReference<>() {
        });
    }

    /**
     * @param tags tags ["one","two","three"]
     */
    @Override
    public void updateTags(List<String> tags) {
        // 给 tag 去重
        Set<String> tagsSet = new HashSet<>(tags);
        List<String> tags2 = new ArrayList<>(tagsSet);
        this.setData(SKYNET_TAGS_PATH, JSON.toJSONString(tags2));
    }
    //endregion

    private String getOnlinePath() {
        return getSkynetOnlinePath() + "/menu";
    }

    @Override
    public List<AntMenuView> getMenusList() {
        List<AntMenuView> menuList = new ArrayList<>();

        String rootPath = getOnlinePath();
        Map<String, String> menuMap = getChildrenWithData(rootPath, null);
        for (Map.Entry<String, String> item : menuMap.entrySet()) {
            // 排除下划线开通的 叶子
            if (item.getKey().contains("_desc")) {
                continue;
            }
            menuList.add(JSON.parseObject(item.getValue(), AntMenuView.class));
        }
        log.debug("getMenusList= {}", menuList);

        return menuList;
    }


    @Override
    public String reportMenu(AntMenuView menuView) {

        log.info("Report online menu start, menu：{}", menuView.toString());
        String rootPath = getOnlinePath();
        if (!this.zkConfigService.exists(rootPath)) {
            this.zkConfigService.putNode(rootPath, "_desc", "Skynet-UI menu navigation");
        }

        String path = String.format("%s/SkynetUI-%s-%s", rootPath, OsUtil.getIPAddress(), menuView.getMenuMd5());
        this.zkConfigService.putEphemeralNode(path, menuView.toString());
        log.info("report online menu end, [{}] register address: {}", menuView.getName(), path);
        return path;
    }

}
