package skynet.platform.common.repository.config.setting;


import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.block.ConfigBlockService;
import skynet.platform.common.repository.config.setting.block.data.ConfigBlock;
import skynet.platform.common.repository.config.setting.block.support.ConfigBlockService4zk;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.CommentProperties;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 请是使用  ConfigItemService 代替 by 2023年09月27日10:37:28
 * @date 2020/7/14 16:20
 */
@Slf4j
public class SkynetSettingManager {

    private final SkynetConfigService skynetConfigService4Properties;
    private final SkynetConfigService skynetConfigService4Logger;
    private final ConfigBlockService configBlockService;
    private final IAntConfigService antConfigService;

    public SkynetSettingManager(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
        this.configBlockService = new ConfigBlockService4zk(antConfigService);
        this.skynetConfigService4Properties = new SkynetConfigServiceImpl(antConfigService, SkynetConfigType.PROPERTY);
        this.skynetConfigService4Logger = new SkynetConfigServiceImpl(antConfigService, SkynetConfigType.LOGGER);
    }

    public SkynetConfigService getPropertiesService() {
        return this.skynetConfigService4Properties;
    }

    public SkynetConfigService getLoggerServices() {
        return this.skynetConfigService4Logger;
    }

    /**
     * 获取 指定 服务坐标的 配置属性（自动合并包括：集群、系统、服务三个级别的）
     *
     * @param actionPoint
     * @return
     * @throws Exception
     */
    public Map<String, Object> getActionConfigProperties(String actionPoint, String ip) throws Exception {
        // 设置action properties属性,注意是从外到内的覆盖机制
        ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
        String plugin = actionNameContract.getPluginCode();
        String action = actionNameContract.getActionCode();
        return getActionConfigProperties(plugin, action, ip);
    }

    /**
     * 获取服务启动 属性配置
     *
     * @param plugin
     * @param action
     * @param ip
     * @return
     * @throws Exception
     */
    public Map<String, Object> getActionConfigProperties(String plugin, String action, String ip) throws Exception {
        log.debug("getActionConfigProperties: plugin={},action={},ip={}", plugin, action, ip);
        // 设置action properties属性,注意是从外到内的覆盖机制
        // Step 1: Plugin level configuration
        Map<String, Object> map = getPluginConfigFullProperties(plugin);

        //依赖的 配置块  by lyhu。  2023年09月27日19:42:14
        // Step 2: Action level configuration
        AntActionParam antActionParam = antConfigService.getActionParam(plugin, action);
        log.debug("Depend action={} ConfigBlockCodes()={}", action, antActionParam.getBootParam().getConfigBlockCodes());

        List<String> actionConfigBlockCodes = antActionParam.getBootParam().getConfigBlockCodes();
        map.putAll(processConfigBlocks(actionConfigBlockCodes));

        //服务级别的配置块
        // Step 3: Service level configuration
        map.putAll(getPropertiesService().getPropMap(plugin, action, null));
        map.putAll(appendLoggingLevel(getLoggerServices().getPropMap(plugin, action, null)));

        // Step 4: Server level configuration (if IP is provided) @sine 3.4.13
        if (StringUtils.isNotBlank(ip)) {
            map.putAll(getConfigBlockByHost(ip));
        }
        if (log.isDebugEnabled()) {
            log.debug("getActionConfigProperties: {}", JSON.toJSONString(map));
        }
        return map;
    }

    public Map<String, Object> getConfigBlockByHost(String ip) throws IOException {
        Map<String, Object> map = new HashMap<>();
        AntServerParam antServerParam = antConfigService.getServerParam(ip);
        if (!ObjectUtils.isEmpty(antServerParam) && !CollectionUtils.isEmpty(antServerParam.getConfigBlockCodes())) {
            log.debug("Depend host={} ConfigBlockCodes()={}", ip, antServerParam.getConfigBlockCodes());
            List<String> serverConfigBlockCodes = antServerParam.getConfigBlockCodes();
            map.putAll(processConfigBlocks(serverConfigBlockCodes));
        }
        return map;
    }

    /**
     * 处理配置块 属性配置
     *
     * @param configBlockCodes 配置块信息
     * @throws IOException
     */
    private Map<String, Object> processConfigBlocks(List<String> configBlockCodes) throws IOException {
        Map<String, Object> map = new HashMap<>();
        for (int index = configBlockCodes.size() - 1; index >= 0; index--) {
            String code = configBlockCodes.get(index);
            ConfigBlock configBlock = configBlockService.fetch(code);
            if (configBlock != null && StringUtils.isNotBlank(configBlock.getText())) {
                log.debug("code={}; ConfigBlock={}", code, configBlock);
                CommentProperties commentProperties = new CommentProperties(configBlock.getText());
                map.putAll(commentProperties.getAllProperty());
            }
        }
        return map;
    }

    /**
     * 获取插件完整的属性，包含集群、系统两个级别的属性（包含日志）
     *
     * @param plugin
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPluginConfigFullProperties(String plugin) throws Exception {
        // 设置action properties属性,注意是从外到内的覆盖机制
        Map<String, Object> map = new TreeMap<>();
        map.putAll(getPropertiesService().getPropMap(null, null, null));
        map.putAll(getPropertiesService().getPropMap(plugin, null, null));
        map.putAll(appendLoggingLevel(getLoggerServices().getPropMap(null, null, null)));
        map.putAll(appendLoggingLevel(getLoggerServices().getPropMap(plugin, null, null)));
        return map;
    }

    /**
     * 只获取插件本身的属性（包含日志）（注意，没有集群级别的）
     *
     * @param plugin
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPluginConfigProperties(String plugin) throws Exception {
        // 设置action properties属性,注意是从外到内的覆盖机制
        Map<String, Object> map = new TreeMap<>();
        map.putAll(getPropertiesService().getPropMap(plugin, null, null));
        map.putAll(appendLoggingLevel(getLoggerServices().getPropMap(plugin, null, null)));
        return map;
    }

    /**
     * 只获取集群级属性（包含日志）
     *
     * @return
     * @throws Exception
     */
    public Map<String, Object> getGlobalConfigProperties() throws Exception {
        return getPluginConfigProperties(null);
    }

    private Map<String, String> appendLoggingLevel(Map<String, String> properties) {
        Map<String, String> map = new LinkedHashMap<>();
        properties.forEach((key, value) -> map.put(String.format("%s%s", "logging.level.", key), value));
        return map;
    }
}
