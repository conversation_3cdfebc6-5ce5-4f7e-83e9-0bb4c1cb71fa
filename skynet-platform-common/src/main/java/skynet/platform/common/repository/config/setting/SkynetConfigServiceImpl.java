package skynet.platform.common.repository.config.setting;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.CommentProperties;
import skynet.platform.common.repository.domain.NodeDescription;

import java.io.IOException;
import java.util.Map;
import java.util.Observer;

/**
 * <AUTHOR> by jianwu6 on 2020/7/8 9:32
 */
@Slf4j
public class SkynetConfigServiceImpl implements SkynetConfigService {

    private final IAntConfigService antConfigService;
    private final SkynetConfigType skynetConfigType;

    public SkynetConfigServiceImpl(IAntConfigService antConfigService, SkynetConfigType skynetConfigType) {
        this.antConfigService = antConfigService;
        this.skynetConfigType = skynetConfigType;
    }

    /**
     * 集群级配置
     */
    @Override
    public String getGlobalProps() {
        return getProps(null, null, null);
    }

    /**
     * 系统级配置
     */
    @Override
    public String getProps(String plugin) {
        return this.getProps(plugin, null, null);
    }

    /**
     * 服务级配置
     */
    @Override
    @SneakyThrows
    public String getProps(String plugin, String action, Observer observer) {
        String path = getPropPath(plugin, action);
        return this.antConfigService.getData(path, observer);
    }

    @Override
    public void setProps(String plugin, String value) {
        this.setProps(plugin, null, value);
    }

    @Override
    public Map<String, String> getPropMap(String plugin, String action, Observer observer) throws IOException {
        CommentProperties commentProperties = getAllProperties(plugin, action, observer);
        return commentProperties.getAllProperty();
    }

    @Override
    public void setProps(String plugin, String action, String value) {
        String path = getPropPath(plugin, action);
        this.antConfigService.setData(path, StringUtils.isBlank(value) ? "" : value.trim());
        //删除老的配置
        path = path.replace(SkynetConfigType.PROPERTY.contextPath, "_properties");
        path = path.replace(SkynetConfigType.LOGGER.contextPath, "_logger");
        this.antConfigService.delData(path);
    }

    @Override
    public void setProp(String plugin, String action, String property, String value) throws IOException {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();

        CommentProperties commentProperties = getAllProperties(plugin, action, null);
        //更新属性配置
        commentProperties.setProperty(property.trim(), StringUtils.isBlank(value) ? "" : value.trim());
        this.setProps(plugin, action, commentProperties.store());
    }

    @Override
    public void delProp(String plugin, String action, String property) throws IOException {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();

        CommentProperties commentProperties = getAllProperties(plugin, action, null);
        //删除属性配置
        commentProperties.remove(property.trim());
        this.setProps(plugin, action, commentProperties.store());
    }

    /**
     * 读取属性配置，并转化为 CommentProperties，兼容老的属性配置
     */
    private CommentProperties getAllProperties(String plugin, String action, Observer observer) throws IOException {

        CommentProperties commentProperties = loadProps(plugin, action, observer);

        //如果已经存在，就丢弃上次的
        Map<String, String> map;
        if (SkynetConfigType.PROPERTY == this.skynetConfigType) {
            map = antConfigService.getProperties(plugin, action, observer);
        } else {
            map = antConfigService.getLoggers(plugin, action, observer);
        }
        // 从key-value 列表中 获取，进行合并,并以最新的配置为主
        map.forEach((k, v) -> {
            if (!commentProperties.containsKey(k)) {
                commentProperties.setProperty(k, v);
            }
        });
        return commentProperties;
    }

    /**
     * 读取属性配置，并转化为 CommentProperties
     */
    private CommentProperties loadProps(String plugin, String action, Observer observer) throws IOException {
        String properties = this.getProps(plugin, action, observer);
        CommentProperties commentProperties = new CommentProperties();
        commentProperties.load(properties);
        return commentProperties;
    }

    /**
     * 获取属性所在的 ZK 路径
     *
     * @param plugin 系统编码，如果为空表示集群级属性
     * @param action 服务编码，如果为空表示系统级属性
     * @return
     */
    private String getPropPath(String plugin, String action) {
        String contextPath = this.skynetConfigType.getContextPath();
        String path;
        if (StringUtils.isNotEmpty(plugin) && StringUtils.isNotEmpty(action)) {
            plugin = plugin.trim();
            action = action.trim();
            this.checkAction(plugin, action);
            path = String.format("%s/%s", this.antConfigService.getActionPath(plugin, action), contextPath);
        } else if (StringUtils.isNotEmpty(plugin)) {
            plugin = plugin.trim();
            this.checkPlugin(plugin);
            path = String.format("%s/%s/setting/%s", this.antConfigService.getSkynetPluginPath(), plugin, contextPath);
        } else {
            path = String.format("%s/%s", this.antConfigService.getSettingPath(null, null), contextPath);
        }

        return path;
    }

    private NodeDescription checkPlugin(String plugin) {
        // TODO 可以复用 AntConfigServiceImpl
        NodeDescription pluginNode = this.antConfigService.getPlugin(plugin);
        if (pluginNode == null) {
            throw new AntException("the plugin [%s] not exist.", plugin);
        } else {
            return pluginNode;
        }
    }

    private NodeDescription checkAction(String plugin, String action) {
        // TODO 可以复用 AntConfigServiceImpl
        NodeDescription actionNodeDescription = this.antConfigService.getAction(plugin, action);
        if (actionNodeDescription == null) {
            throw new AntException(String.format("the action [%s@%s] not exist", action, plugin));
        } else {
            return actionNodeDescription;
        }
    }

}
