package skynet.platform.common.xray;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;
import oshi.software.os.InternetProtocolStats;
import oshi.software.os.OSFileStore;
import oshi.software.os.OSProcess;
import skynet.platform.common.utils.cmd.CommandLineExecutor;
import skynet.platform.common.xray.domain.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统Metric采样器
 *
 * <AUTHOR>
 * @since 2019年1月27日 下午7:20:33
 */

@Slf4j
public class MetricSampler implements AutoCloseable {

    private static final Set<String> EXCEPTION_SET_AVOID_REPEAT_LOGGING = new HashSet<>();
    private final Map<String, NetworkIF> lastNetworkIFCache = new HashMap<>(5);
    private final SystemInfo systemInfo;
    private final HardwareAbstractionLayer hardware;

    private volatile long lastNetIfStatTime;
    private volatile Gpu gpu;

    public MetricSampler() {
        this.systemInfo = new SystemInfo();
        this.hardware = systemInfo.getHardware();
    }

    private static void logAvoidRepeatLogging(String method, Throwable t) {
        String key = String.format("%s:%s-%s", method, t.getClass().getName(), t.getMessage());
        if (!EXCEPTION_SET_AVOID_REPEAT_LOGGING.contains(key)) {
            log.error(String.format("%s failed!", method), t);
            EXCEPTION_SET_AVOID_REPEAT_LOGGING.add(key);
        }
    }

    /**
     * CPU采样
     *
     * @return
     */
    public CpuStat cpuStat() {
        CpuStat ret = null;
        try {
            ret = CpuStat.get(this.hardware.getProcessor());
        } catch (Throwable e) {
            logAvoidRepeatLogging("cpuStat", e);
        }
        return ret;
    }

    /**
     * 连接数采样
     *
     * @return
     */
    public ConnectionStat connectionStat() {
        ConnectionStat ret = null;
        try {
            InternetProtocolStats internetProtocolStats = systemInfo.getOperatingSystem().getInternetProtocolStats();
            ret = new ConnectionStat(internetProtocolStats);
        } catch (Throwable e) {
            logAvoidRepeatLogging("connectionStat", e);
        }
        return ret;
    }

    static final String CachedCommand = "cat /proc/meminfo | grep -w Cached | awk '{print $2}'";

    /**
     * 内存采样
     *
     * @return
     */
    public MemStat memStat() {
        MemStat ret = null;
        long cached = -1;
        try {
            if (SystemUtils.IS_OS_LINUX) {
                String result = CommandLineExecutor.executeShell(CachedCommand);
                if (org.springframework.util.StringUtils.hasText(result)) {
                    //查询出来的是 Cached 单位是KB。
                    cached = Long.parseLong(result.trim().replaceAll("\\D+", "")) << 10;
                }
            }
        } catch (Throwable e) {
            logAvoidRepeatLogging("memStat", e);
        }
        try {
            ret = new MemStat(hardware.getMemory(), cached);
        } catch (Throwable e) {
            logAvoidRepeatLogging("memStat", e);
        }
        return ret;
    }

    /**
     * 网卡流量采样
     *
     * @param renewInterval
     * @return
     */
    public synchronized List<NetworkStat> networkStat(boolean renewInterval) {

        List<NetworkIF> networkIfList = hardware.getNetworkIFs(false);
        List<NetworkStat> ret = new ArrayList<>(networkIfList.size());
        long currentTime = System.currentTimeMillis();
        for (NetworkIF networkIf : networkIfList) {
            String devName = networkIf.getName();
            try {
                // 排除虚拟网卡、本地环路网卡以及未使用的网卡
//                if (NetFlags.NULL_HWADDR.equals(ifConfig.getHwaddr()) || NetFlags.ANY_ADDR.equals(addr)
//                        || NetFlags.ANY_ADDR_V6.equals(addr) || NetFlags.isLoopback(addr)
//                        || currentNetIfStat.getTxBytes() == 0) {
//                    continue;
//                }
                //|| networkIF.getIPv6addr().length > 0)
                if (networkIf.getIPv4addr().length > 0 && networkIf.getBytesSent() > 0) {
                    NetworkIF lastNetworkIf = this.lastNetworkIFCache.get(devName);
                    long interval = (this.lastNetIfStatTime == 0) ? Integer.MAX_VALUE : (currentTime - this.lastNetIfStatTime);
                    ret.add(new NetworkStat(networkIf, lastNetworkIf, interval));
                    if (renewInterval) {
                        this.lastNetworkIFCache.put(devName, networkIf);
                    }
                }
            } catch (Throwable e) {
                logAvoidRepeatLogging(String.format("networkStat:%s", devName), e);
            }

        }
        if (renewInterval) {
            this.lastNetIfStatTime = currentTime;
        }

        return ret;
    }

    /**
     * 存储设备采样
     *
     * @param ignoreMountedDirNames
     * @return
     */
    public synchronized List<DiskStat> diskStat(List<String> ignoreMountedDirNames) {

        List<OSFileStore> osFileStoreList = new ArrayList<>();
        try {
            oshi.software.os.FileSystem fileSystem = systemInfo.getOperatingSystem().getFileSystem();
            osFileStoreList = fileSystem.getFileStores(true);
        } catch (Throwable e) {
            logAvoidRepeatLogging("diskStat", e);
        }
        List<DiskStat> ret = new ArrayList<>(osFileStoreList.size());
        if (osFileStoreList.isEmpty()) {
            return ret;
        }

        for (OSFileStore osFileStore : osFileStoreList) {
            ret.add(new DiskStat(osFileStore));
        }
        hardware.getDiskStores();

        //排除 忽略的 挂载目录名称
        if (ignoreMountedDirNames != null) {
            log.debug("ignoreMountedDirNames={}", ignoreMountedDirNames);
            for (String dirName : ignoreMountedDirNames) {
                ret = ret.stream().filter(e -> e.getDirName() != null && !e.getDirName().toLowerCase().startsWith(dirName.toLowerCase())).toList();
            }
        }

        ret = ret.stream().filter(x -> !"rootfs".equalsIgnoreCase(x.getFsType()))
                .sorted((x, y) -> x.getDirName().length() > y.getDirName().length() ? 1 : -1)
                .toList();

        return ret;
    }

    /**
     * 显卡采样
     *
     * @param queryString
     * @return
     */
    public List<GpuStat> gpuStat(String queryString) {
        List<GpuStat> ret = new ArrayList<>(4);
        if (this.gpu == null) {
            synchronized (this) {
                if (this.gpu == null) {
                    this.gpu = new Gpu(queryString);
                }
            }
        }
        List<Map<String, Object>> metrics = this.gpu.getMetrics();
        for (Map<String, Object> metric : metrics) {
            ret.add(new GpuStat(metric));
        }
        return ret;
    }

    /**
     * 进程信息采样
     *
     * @return
     */
    public ProcStat procStat(String aid, String ip, int port, int pid) {
        ProcStat ret = null;
        try {
            OSProcess p = systemInfo.getOperatingSystem().getProcess(pid);
            ret = new ProcStat(aid, ip, port, pid, p);
        } catch (Throwable e) {
            log.error("AID={};PID={};Error={}", aid, pid, e.getMessage());
            logAvoidRepeatLogging("procStat", e);
        }
        return ret;
    }

    @Override
    public void close() throws Exception {

    }
}
