package skynet.platform.common.xray.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * 英伟达显卡信息概要
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class NvidiaGPU extends Jsonable {
    /**
     * 显卡名称, 如: Tesla P40
     */
    @JSONField(ordinal = 10)
    private String name;

    /**
     * 显卡 uuid, 如: GPU-53cd01d1-6b66-98c0-8fb6-827c0187a6ba
     */
    @JSONField(ordinal = 11)
    private String uuid;

    /**
     * 第几个显卡, 如: 1
     */
    @JSONField(ordinal = 12)
    private String index;

    /**
     * 显卡自身序号, 如: 0420418030107
     */
    @JSONField(ordinal = 13)
    private String serial;

    /**
     * 显卡总内存, 如: 22919M
     */
    @JSONField(name = "memory.total", ordinal = 14)
    private String memoryTotal;

    /**
     * 显卡未使用内存, 如: 2117M
     */
    @JSONField(name = "memory.free", ordinal = 15)
    private String memoryFree;

    /**
     * 显卡已用内存, 如: 20802M
     */
    @JSONField(name = "memory.used", ordinal = 16)
    private String memoryUsed;

    @Override
    public String toString() {
        return super.toString();
    }
}
