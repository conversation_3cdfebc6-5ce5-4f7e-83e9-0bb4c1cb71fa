package skynet.platform.common.endpoint;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.util.StringUtils;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.common.config.PlatformProperties;
import skynet.platform.common.repository.config.AntConfigServiceAdaptor;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.impl.AntConfigServiceAdaptorImpl;
import skynet.platform.common.repository.domain.AntActionLabel;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.io.Serial;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


/**
 * skynet-prometheus-target
 *
 * <AUTHOR>  at  2024年12月02日14:29:09
 */
@Slf4j
@Endpoint(id = "skynet-prometheus-target")
public class PrometheusTargetEndpoint {

    private final AntConfigServiceAdaptor antConfigServiceAdaptor;
    private final PlatformProperties platformProperties;
    private final static Set<String> excludeList = new HashSet<>(Arrays.asList("ant-xagent@ant", "ant-xmanager@ant"));

    public PrometheusTargetEndpoint(IAntConfigService configSrv4Server, PlatformProperties platformProperties) {
        log.info("init PrometheusTargetEndpoint");
        this.antConfigServiceAdaptor = new AntConfigServiceAdaptorImpl(configSrv4Server);
        this.platformProperties = platformProperties;
    }

    @ReadOperation
    public Object invoke() {

        List<TargetItem> items = new ArrayList<>();
        List<AntActionStatus> allActionStatusList = new ArrayList<>();
        Map<String, List<AntActionStatus>> onlineMap = antConfigServiceAdaptor.getAllOnlineActionStatus(null);
        onlineMap.forEach((k, v) -> {
            allActionStatusList.addAll(v);
        });

        for (AntActionStatus status : allActionStatusList) {
            AntActionLabel antActionLabel = status.getActionLabelByCode("enablePrometheusTarget");
            if (antActionLabel != null && antActionLabel.getValue() && StringUtils.hasText(antActionLabel.getExtProperty())) {
                ScrapePara scrapePara;
                try {
                    scrapePara = ScrapePara.parse(antActionLabel.getExtProperty());
                } catch (Exception e) {
                    log.error("[{}]invalid label format : {}; error:{}", status.getAction(), antActionLabel.getExtProperty(), e.getMessage());
                    continue;
                }

                TargetItem targetItem = new TargetItem();
                BeanUtils.copyProperties(status, targetItem);
                targetItem.setPath(scrapePara.getUrl());
                targetItem.setInterval(scrapePara.getInterval());
                BasicAuth basicAuth = scrapePara.getBasicAuth();
                if (basicAuth != null) {
                    targetItem.setBasicAuth(new String(Base64.getEncoder().encode(basicAuth.toString().getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8));
                }
                items.add(targetItem);
            }
        }

        if (platformProperties.isDisabledSkynet2PrometheusTargetEndpoint()) {
            items = items.stream().filter(item -> !excludeList.contains(item.getAction())).toList();
        }

        return items;

    }

    @Getter
    @Setter
    static class TargetItem extends Jsonable {

        @JSONField(ordinal = 10)
        private String action;

        @JSONField(ordinal = 12)
        private String name;

        @JSONField(ordinal = 15)
        private String protocol = "http";

        @JSONField(ordinal = 20)
        private String ip;

        @JSONField(ordinal = 30)
        private int port;

        @JSONField(ordinal = 31)
        private String path;

        @JSONField(ordinal = 32)
        private String interval;

        @JSONField(ordinal = 33)
        private String basicAuth;

        @JSONField(ordinal = 50, format = "yyyy-MM-dd HH:mm:ss")
        private Date start;

        @JSONField(ordinal = 55, format = "yyyy-MM-dd HH:mm:ss")
        private Date report;

        @JSONField(ordinal = 60)
        private Map<String, Object> metadata;

        @JSONField(ordinal = 70)
        private String from;


    }

    @Getter
    @Setter
    static class BasicAuth extends Jsonable {
        private String username;
        private String password;
        private String password_file;

        public BasicAuth(String username, String password) {
            this.username = username;
            this.password = password;
        }
    }


    @Slf4j
    @Builder
    @Getter
    @Setter
    static class ScrapePara extends Jsonable {

        private String url;
        private String interval;

        private String basicUsername;
        private String basicPassword;

        public static ScrapePara parse(String labelExtProperty) throws InvalidFormatException {
            log.debug("labelExtProperty= {}", labelExtProperty);
            labelExtProperty = labelExtProperty.trim();
            String[] authPath = labelExtProperty.split("@");
            if (authPath.length == 0 || authPath.length > 2) {
                throw new InvalidFormatException(labelExtProperty);
            }
            ScrapeParaBuilder builder = ScrapePara.builder();
            String path = labelExtProperty;
            if (authPath.length == 2) {
                //BASE64(admin:password)
                String userPwd;
                log.debug("admin:password base64 String = {}", authPath[0]);
                userPwd = new String(Base64.getDecoder().decode(authPath[0]), StandardCharsets.UTF_8);
                log.debug("admin:password = {}", userPwd);
                String[] splits = userPwd.split(":");
                if (splits.length != 2) {
                    throw new InvalidFormatException(labelExtProperty);
                }
                builder.basicUsername(splits[0].trim()).basicPassword(splits[1].trim());
                path = authPath[1];
            }

            String[] splits = path.split(":");
            if (splits.length != 2) {
                throw new InvalidFormatException(path);
            }
            builder.url(splits[0].trim()).interval(splits[1].trim());
            return builder.build();
        }

        public BasicAuth getBasicAuth() {
            return StringUtils.hasText(this.basicPassword) ? new BasicAuth(this.basicUsername, this.basicPassword) : null;
        }

        //admin:pwd@/actuator/prometheus:15s
        public static class InvalidFormatException extends Exception {

            @Serial
            private static final long serialVersionUID = -2563361184960591695L;

            public InvalidFormatException(String prop) {
                super(String.format("The property [%s] format invalid. eg\tYWRtaW46cHdk@/actuator/prometheus:15s  or /actuator/prometheus:15s", prop));
            }
        }
    }
}

