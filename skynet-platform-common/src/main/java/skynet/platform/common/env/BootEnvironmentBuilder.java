package skynet.platform.common.env;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.util.ObjectUtils;
import skynet.boot.AppContext;
import skynet.boot.SkynetProperties;
import skynet.boot.security.config.SkynetBaseAuthProperties;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.AntServiceParam;
import skynet.platform.common.domain.BootParam;
import skynet.platform.common.logging.LogbackConfig;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;
import skynet.platform.common.repository.domain.ActionNameContract;

import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public class BootEnvironmentBuilder {
    public static final String CONFIG_SERVER_PREFIX = "/skynetconfig";
    public static final String SKYNET_CONFIG_SERVER_URI = "SKYNET_CONFIG_SERVER_URI";

    @Getter
    private final Environment environment;
    private final IAntConfigService antConfigService;
    private final ManagerEncryptor managerEncryptor;
    private final SkynetZkProperties skynetZkProperties;
    private final SkynetSettingManager skynetSettingManager;
    private final LoadBalancerClient loadBalancerClient;
    private final ConfigClientFetcher configClientFetcher;
    private final DnsEnvironmentBuilder dnsEnvironmentBuilder;
    private final SkynetBaseAuthProperties securityProperties;
    private final AppContext appContext;

    public BootEnvironmentBuilder(Environment environment, IAntConfigService antConfigService, ManagerEncryptor managerEncryptor,
                                  SkynetSettingManager skynetSettingManager,
                                  LoadBalancerClient loadBalancerClient, ConfigClientFetcher configClientFetcher,
                                  SkynetBaseAuthProperties securityProperties, AppContext appContext) {
        this.environment = environment;
        this.antConfigService = antConfigService;
        this.managerEncryptor = managerEncryptor;
        this.appContext = appContext;
        this.skynetZkProperties = this.antConfigService.getSkynetZKProperties();
        this.skynetSettingManager = skynetSettingManager;
        this.loadBalancerClient = loadBalancerClient;
        this.configClientFetcher = configClientFetcher;
        this.securityProperties = securityProperties;
        this.dnsEnvironmentBuilder = new DnsEnvironmentBuilder(antConfigService);
    }


    public static Map<String, Object> getSkynetEnvironment(SkynetZkProperties skynetZkProperties, SkynetProperties skynetProperties) {

        // 工作目录是否隔离，主要解决 多服务器的 skynet 工作目录都使用的相关挂载目录导致状态文件覆盖问题
        // 如果隔离，通过IP地址目录区分  by lyhu 2024年03月05日11:19:03
        boolean isolation = "true".equalsIgnoreCase(System.getProperty("skynet.isolation", "false"));
        log.debug("skynet.isolation={}", isolation);

        HashMap<String, Object> map = new LinkedHashMap<>();
        // skynet props and logger
        map.put("IP", skynetProperties.getIpAddress());
        map.put("PORT", String.valueOf(skynetProperties.getPort()));
        map.put("SKYNET_ZK", skynetZkProperties.getServerList());
        map.put("SKYNET_ZK_JAAS_FILE", System.getProperty("java.security.auth.login.config", ""));
        map.put("SKYNET_CLUSTER", skynetZkProperties.getClusterName());
        map.put("SKYNET_HOME", skynetProperties.getHome());

        //  /iflytek/server/skynet/tmp/**************/
        String tmp = Paths.get(skynetProperties.getHome(), "tmp", isolation ? skynetProperties.getIpAddress() : "").toString();
        map.put("SKYNET_TMP_ROOT", tmp);
        map.put("SKYNET_ACTION_POINT", skynetProperties.getActionPoint());
        map.put("SKYNET_ACTION_ID", skynetProperties.getActionId());
        map.put("SKYNET_ACTION_PLUGIN", skynetProperties.getPlugin());
        map.put("SKYNET_PLUGIN_CODE", skynetProperties.getPlugin());
        map.put("SKYNET_ACTION_CODE", skynetProperties.getActionCode());
        // eg: /iflytek/server/skynet/plugin/**************
        String pluginRoot = Paths.get(skynetProperties.getHome(), "plugin", isolation ? skynetProperties.getIpAddress() : "").toString();
        map.put("SKYNET_PLUGIN_ROOT", pluginRoot);
        // eg: /iflytek/server/skynet/plugin/**************/ant
        map.put("SKYNET_PLUGIN_HOME", String.format("%s/%s", pluginRoot, skynetProperties.getPlugin()));
        map.put("SKYNET_EXT_CONFIG_ROOT", String.format("%s/%s", tmp, skynetProperties.getActionId()));
        map.put("SKYNET_CONFIG_LOCATION_URI", String.format("http://%s:%d/skynet/config/%s?actionId=%s",
                skynetProperties.getIpAddress(), skynetProperties.getPort(), skynetProperties.getActionPoint(), skynetProperties.getActionId()));
        map.put("SKYNET_CONFIG_LOCATION_FILE", Paths.get(tmp, String.format("%s.app.properties", skynetProperties.getActionId())).toFile().toString());
        map.put("SKYNET_LOGBACK_FILE", String.format("%s/conf/skynet-logback.xml", skynetProperties.getHome()));

        map.put(SKYNET_CONFIG_SERVER_URI, String.format("http://%s:%s%s", skynetProperties.getIpAddress(), skynetProperties.getPort(), CONFIG_SERVER_PREFIX));
        return map;
    }

    /**
     * 获取配置中的 配置了 SpringCloudConfigServer中的配置。
     *
     * @param bootEnvironment
     * @return
     */
    private Map<String, Object> fetchEnvFromConfigServer(BootEnvironment bootEnvironment) {
        PropertySource<?> propertySource = configClientFetcher.getPropertySource(bootEnvironment);
        Map<String, Object> map = new LinkedHashMap<>();
        if (propertySource != null) {
            CompositePropertySource propertySource1 = (CompositePropertySource) propertySource;
            String[] propertyNames = propertySource1.getPropertyNames();
            log.debug("Fetch propertySource by SpringCloudConfigClient. propertySource len={}", propertyNames.length);
            for (String propertyName : propertyNames) {
                map.put(propertyName, propertySource1.getProperty(propertyName));
            }
        }
        return map;
    }

//    /**
//     * 只构建 用户配置的属性
//     * <p>
//     * 配置中的占位符会自定完成替换
//     *
//     * @param skynetProperties
//     * @return
//     * @throws Exception
//     */
//    public BootEnvironment buildUserConfig(SkynetProperties skynetProperties) throws Exception {
//
//        BootEnvironment bootEnvironment = build(skynetProperties);
//        //获取用户配置的属性
//        Map<String, Object> vars = skynetSettingManager.getActionConfigProperties(skynetProperties.getActionPoint(), skynetProperties.getIpAddress());
//        BootEnvironment env = new BootEnvironment(environment);
//        //只保留用户配置的属性
//        for (String key : vars.keySet()) {
//            env.put(key, bootEnvironment.getOrDefault(key, ""));
//        }
//        return env;
//    }

    public BootEnvironment build(SkynetProperties skynetProperties, boolean fromConfigClient) throws Exception {
        BootEnvironment bootEnvironment = this.build(skynetProperties);
        List<String> delKeyList = new ArrayList<>();
        delKeyList.add(SkynetProperties.SKYNET_IP_KEY);
        delKeyList.add("IP");
        delKeyList.add("PORT");
        delKeyList.add("spring.cloud.zookeeper.discovery.instance-host");
        if (fromConfigClient) {
            for (String key : bootEnvironment.keySet()) {
                if (key.startsWith("spring.cloud.config")) {
                    delKeyList.add(key);
                }
            }
        }
        delKeyList.forEach(bootEnvironment::remove);
        return bootEnvironment;
    }

    /**
     * 构建服务环境
     */
    public BootEnvironment build(BootEnvironmentBuildParam buildParam) throws Exception {
        SkynetProperties skynetProperties = new SkynetProperties(this.environment);
        if (buildParam.getIpAddress() != null) {
            skynetProperties.setIpAddress(buildParam.getIpAddress());
        }

        skynetProperties.setActionPoint(buildParam.getActionPoint());
        skynetProperties.setActionId(buildParam.getActionId());
        skynetProperties.setPort(buildParam.getAppPort());
        //TODO: skynetProperties add ACTION_INDEX
        String masterIP = StringUtils.isNotBlank(buildParam.getMasterIp()) ? buildParam.getMasterIp() : skynetProperties.getIpAddress();
        BootEnvironment map = this.build(skynetProperties, masterIP);

        // 增加扩展端口环境变量
        if (buildParam.getExtPorts() != null) {
            AtomicInteger indexCounter = new AtomicInteger(1);
            buildParam.getExtPorts().forEach(x -> map.put("PORT" + indexCounter.getAndIncrement(), x));
        }
        map.put("SKYNET_ACTION_INDEX", buildParam.getActionIndex());
        map.put("MASTER_IP", masterIP);

        // 伪域名解析
        return dnsEnvironmentBuilder.build(map);
    }

    public BootEnvironment build(SkynetProperties skynetProperties) throws Exception {
        return build(skynetProperties, skynetProperties.getIpAddress());
    }

    public BootEnvironment build(SkynetProperties skynetProperties, String masterIP) throws Exception {
        log.debug("build BootEnvironment skynetProperties={};", skynetProperties);
        if (StringUtils.isBlank(skynetProperties.getActionId()) || skynetProperties.isDebugMode()) {
            skynetProperties.setActionId(skynetProperties.getActionPoint());
        }

        BootEnvironment map = new BootEnvironment(environment);

        ActionNameContract actionNameContract = new ActionNameContract(skynetProperties.getActionPoint());
        String plugin = actionNameContract.getPluginCode();
        String action = actionNameContract.getActionCode();

        // 通过ZK， 获取 AntActionParam
        AntActionParam antActionParam = this.antConfigService.getActionParam(plugin, action);

        try {
            Map<String, Object> vars = skynetSettingManager.getActionConfigProperties(skynetProperties.getActionPoint(), masterIP);
            map.putAll(vars);
        } catch (Exception e) {
            String msg = String.format("Get The Action %s ConfigProperties Error=%s", skynetProperties.getActionPoint(), e.getMessage());
            log.error(msg, e);
        }

//        map.put(SkynetProperties.SKYNET_ACTION_NAME_KEY, actionPoint);
        map.put(SkynetProperties.SKYNET_ACTION_POINT_KEY, skynetProperties.getActionPoint());
        map.put(SkynetProperties.SKYNET_ACTION_ID_KEY, skynetProperties.getActionId());
//        map.put(SkynetProperties.SKYNET_ACTION_TITLE_KEY, antActionParam.getTitle());
//        map.put(SkynetProperties.SKYNET_ACTION_DESC_KEY, antActionParam.getDesc());

        AntServiceParam service = antActionParam.getService();
        if (service != null && (StringUtils.isNoneBlank(service.getName()))) {
            map.put(String.format("%s.name", AntServiceParam.PROP_PREFIX), service.getName());
            map.put(String.format("%s.desc", AntServiceParam.PROP_PREFIX), StringUtils.isBlank(service.getDesc()) ? "" : service.getDesc());
            map.put(String.format("%s.context", AntServiceParam.PROP_PREFIX), JSON.toJSONString(service.getContext()));
        }

        map.put(SkynetZkProperties.SKYNET_ZOOKEEPER_ENABLED, true);
        map.put(SkynetZkProperties.SKYNET_ZOOKEEPER_CLUSTER_NAME, skynetZkProperties.getClusterName());
        map.put(SkynetZkProperties.SKYNET_ZOOKEEPER_SERVER_LIST, skynetZkProperties.getServerList());
        map.put(SkynetZkProperties.SKYNET_ZOOKEEPER_SESSION_TIMEOUT, skynetZkProperties.getSessionTimeout());
        map.put(SkynetZkProperties.SKYNET_ZOOKEEPER_CONNECTION_TIMEOUT, skynetZkProperties.getConnectionTimeout());

//        map.put("skynet.zookeeper.onlinePath", String.format("%s/action", this.antConfigService.getSkynetOnlinePath()));

        BootParam bootParam = antActionParam.getBootParam();
        // 为了兼容以前的配置
        if (bootParam == null) {
            bootParam = new BootParam();
        }
        if (skynetProperties.getPort() == 0) {
            skynetProperties.setPort(antActionParam.getPort());
        }

        map.put("info.name", antActionParam.getTitle());
        map.put("info.description", antActionParam.getDesc());

        // 默认日志配置文件
        // 强制使用 统一的log配置， 以下的Key 要与 skynet-boot-build/misc/conf/ 中的skynet.logback.xml 中的占位符匹配
        if (bootParam.isLogCollection() && !skynetProperties.isDebugMode()) {
            // debug 下不用设置 logging.config
            this.fillEnvMap(map, "logging.pattern.console", LogbackConfig.getLoggingPatternConsole());
            this.fillEnvMap(map, "logging.pattern.file", LogbackConfig.getLoggingPatternFile());
            this.fillEnvMap(map, "logging.config", String.format("file:%s/conf/skynet-logback.xml", skynetProperties.getHome()));
//            map.put("logging.file.name", LogbackConfig.getLogFullFileName(actionId, map));
//            //为了兼容 spring boot 1.5.x
//            map.put("logging.file", LogbackConfig.getLogFullFileName(actionId, map));
        }

        // debug 状态下 不用设置IP，和skynet.home
        if (skynetProperties.isDebugMode()) {
            map.put("debug", true);
        } else {
            map.put(SkynetProperties.SKYNET_HOME_KEY, skynetProperties.getHome());
            // 如果没有配置，就采用默认配置
            this.fillEnvMap(map, SkynetProperties.SKYNET_IP_KEY, skynetProperties.getIpAddress());
        }

        // 此处要与 skynet.boot.config.ConfigServer4ZkEnvironmentRepository findOne 中的保持一致。
        // 以外围配置为主
        // 服务编码

        this.fillEnvMap(map, "spring.application.name", action);
        this.fillEnvMap(map, "spring.cloud.zookeeper.connect-string", skynetZkProperties.getServerList());
        this.fillEnvMap(map, "spring.cloud.zookeeper.discovery.root", String.format("/%s/discovery", skynetZkProperties.getClusterName()));
        this.fillEnvMap(map, "spring.cloud.zookeeper.discovery.instance-host", skynetProperties.getIpAddress());

        this.fillEnvMap(map, "server.tomcat.mbeanregistry.enabled", "true");
        this.fillEnvMap(map, "management.metrics.distribution.percentilesHistogram.all", "false");

        // 镜像仓库地址 服务地址中配置
        AntServerParam antServerParam = this.antConfigService.getServerParam(masterIP);
        if (!ObjectUtils.isEmpty(antServerParam.getK8s())) {
            this.fillEnvMap(map, "registry.url", antServerParam.getK8s().getRegistryUrl());
            this.fillEnvMap(map, "registry.username", antServerParam.getK8s().getRegistryUsername());
            this.fillEnvMap(map, "registry.password", managerEncryptor.decrypt(antServerParam.getK8s().getRegistryPassword()));
            this.fillEnvMap(map, "registry.context-path", antServerParam.getK8s().getRegistryContextPath());
            this.fillEnvMap(map, "k8s.docker.hub.host", String.format("%s/%s", antServerParam.getK8s().getRegistryUrl(), antServerParam.getK8s().getRegistryContextPath()));
        }

        Map<String, Object> skynetEnvMap = getSkynetEnvironment(skynetZkProperties, skynetProperties);
        map.putAll(skynetEnvMap);

        map.put("SKYNET_AGENT_URI", getSkynetUri(AppBootEnvironment.AGENT_SERVICE_ID));
        map.put("SKYNET_MANAGER_URI", getSkynetUri(AppBootEnvironment.MANAGER_SERVICE_ID));
        map.put("WORK_HOME", bootParam.getWorkHome());

        map.replacePlaceholder();

        //获取配置中的 配置了 SpringCloudConfigServer中的配置。
        Map<String, Object> configServerMap = this.fetchEnvFromConfigServer(map);
        if (!configServerMap.isEmpty()) {
            map.putAll(configServerMap);
        }
        // 服务实例ID 或 debug  // 服务坐标
        //"[application]actionName  [profile]actionId  [label]actionPoint
        this.fillEnvMap(map, "spring.cloud.config.profile", skynetProperties.isDebugMode() ? "debug" : skynetProperties.getActionId());
        this.fillEnvMap(map, "spring.cloud.config.label", skynetProperties.getActionPoint());
        this.fillEnvMap(map, "spring.cloud.config.uri", String.format("http://%s:%s%s", appContext.getIpAddress(), appContext.getPort(), CONFIG_SERVER_PREFIX));

        if (securityProperties.getUser() != null) {
            String securityUser = securityProperties.getUser().getName();
            String securityPw = securityProperties.getUser().getPassword();
            if (StringUtils.isNoneBlank(securityUser) && StringUtils.isNoneBlank(securityPw)) {
                this.fillEnvMap(map, "spring.cloud.config.username", securityUser);
                this.fillEnvMap(map, "spring.cloud.config.pass" + "word", securityPw);//小安平台检测
                String configUri = String.format("http://%s:%s@%s:%d/skynet/config/%s?actionId=%s", securityUser, securityPw, appContext.getIpAddress(), appContext.getPort(), skynetProperties.getActionPoint(), skynetProperties.getActionId());
                map.put("SKYNET_CONFIG_LOCATION_URI", configUri);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("#---replacePlaceholder before ---------------------\n");
            log.debug("{}", map.toPropLines());
            log.debug("#------------------------------------------------\n\n");
        }
        map = map.replacePlaceholder();
        if (log.isDebugEnabled()) {
            log.debug("#---replacePlaceholder after ---------------------\n");
            log.debug("{}", map.toPropLines());
            log.debug("#------------------------------------------------\n\n");
        }
        return map;
    }

    private void fillEnvMap(BootEnvironment bootEnvironment, String key, Object defaultValue) {
        bootEnvironment.put(key, bootEnvironment.getOrDefault(key, defaultValue));
    }

    private String getSkynetUri(String serviceId) {
        ServiceInstance serviceInstance = loadBalancerClient.choose(serviceId);
        return (serviceInstance != null) ? String.format("%s%s", serviceInstance.getUri(), serviceInstance.getMetadata().getOrDefault("server.servlet.context-path", "")) : "";
    }

}