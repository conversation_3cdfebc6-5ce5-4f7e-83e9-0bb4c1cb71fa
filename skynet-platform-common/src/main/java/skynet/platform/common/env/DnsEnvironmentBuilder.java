package skynet.platform.common.env;

import lombok.extern.slf4j.Slf4j;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 构建用于伪域名解析的环境变量
 */
@Slf4j
public class DnsEnvironmentBuilder {

    private final IAntConfigService antConfigService;

    public DnsEnvironmentBuilder(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    /**
     * 构建用于伪域名解析的环境变量，每个域名可以解析成服务对应的地址信息，比如 `IP:PORT`，以逗号分割；
     * Skynet 会将部署服务的端点信息注册到 `/skynet/cluster/online/action` 节点下，系统会自动扫描该节点，
     * 为每个服务生成伪域名，伪域名以环境变量的格式生成在配置文件中，格式如下：
     * <p>
     * * dns.action.plugin.skynet.cluster.local            解析成 `IP:PORT`
     * * dns.ip.action.plugin.skynet.cluster.local         解析成 `IP`
     * * dns.port.action.plugin.skynet.cluster.local       解析成 `PORT`
     * * dns.ext1.action.plugin.skynet.cluster.local       解析成 `IP:PORT1`
     * * dns.port1.action.plugin.skynet.cluster.local      解析成 `PORT1`
     */
    public BootEnvironment build(BootEnvironment map) {
        List<String> onlineActions = this.antConfigService.getOnlineActionNames(null);
        for (String action : onlineActions) {
            log.debug("Generating action domain, action={}", action);
            Map<String, AntActionStatus> status = this.antConfigService.getOnlineActionNodeStatus(action, null);
            if (status.isEmpty()) {
                continue;
            }

            // 主端口
            buildActionMainHost(action, status, map);

            // 扩展端口
            buildActionExtHosts(action, status, map);
        }
        return map.replacePlaceholder();
    }

    /**
     * 构建主端口的地址信息
     */
    private void buildActionMainHost(String action, Map<String, AntActionStatus> status, BootEnvironment map) {
        String hostKey = String.format("dns.%s.skynet.cluster.local", action.replace("@", "."));
        String hostValue = status.values().stream().map(x -> x.getIp() + ":" + x.getPort()).collect(Collectors.joining(","));
        map.put(hostKey, hostValue);
        log.debug("Generating action host, action={}, {}={}", action, hostKey, hostValue);

        String ipKey = String.format("dns.ip.%s.skynet.cluster.local", action.replace("@", "."));
        String ipValue = status.values().stream().map(AntActionStatus::getIp).collect(Collectors.joining(","));
        map.put(ipKey, ipValue);
        log.trace("Generating action ip, action={}, {}={}", action, ipKey, ipValue);

        String portKey = String.format("dns.port.%s.skynet.cluster.local", action.replace("@", "."));
        String portValue = status.values().stream().map(x -> String.valueOf(x.getPort())).collect(Collectors.joining(","));
        map.put(portKey, portValue);
        log.trace("Generating action port, action={}, {}={}", action, portKey, portValue);
    }

    /**
     * 构建扩展端口的地址信息
     */
    private void buildActionExtHosts(String action, Map<String, AntActionStatus> status, BootEnvironment map) {

        Map.Entry<String, AntActionStatus> first = status.entrySet().iterator().next();
        int extPortCount = first.getValue().getPorts() == null ? 0 : first.getValue().getPorts().size();
        for (int i = 0; i < extPortCount; i++) {
            List<String> ihosts = new ArrayList<>();
            List<String> iports = new ArrayList<>();
            for (Map.Entry<String, AntActionStatus> entry : status.entrySet()) {
                if (Optional.ofNullable(entry.getValue().getPorts()).map(List::size).orElse(0) > i) {
                    ihosts.add(entry.getValue().getIp() + ":" + entry.getValue().getPorts().get(i));
                    iports.add(String.valueOf(entry.getValue().getPorts().get(i)));
                }
            }

            String extHostKey = String.format("dns.ext%d.%s.skynet.cluster.local", i + 1, action.replace("@", "."));
            String extHostValue = String.join(",", ihosts);
            map.put(extHostKey, extHostValue);
            log.debug("Generating action ext host, action={}, {}={}", action, extHostKey, extHostValue);

            String extPortKey = String.format("dns.port%d.%s.skynet.cluster.local", i + 1, action.replace("@", "."));
            String extPortValue = String.join(",", iports);
            map.put(extPortKey, extPortValue);
            log.debug("Generating action ext port, action={}, {}={}", action, extPortKey, extPortValue);
        }
    }
}
