package skynet.platform.common.route;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.boot.common.domain.RestResponse;
import skynet.boot.common.domain.SampleState;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.AntActionLabel;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.common.repository.domain.NodeDescription;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线服务状态
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年4月8日 下午3:08:46]
 */

@Slf4j
public class OnlineActionController {

    private static final Date startTime = new Date();

    private final OnlineActionManager onlineActionManager;
    private final IAntConfigService antConfigService;

    public OnlineActionController(OnlineActionManager onlineActionManager, IAntConfigService antConfigService) {
        this.onlineActionManager = onlineActionManager;
        this.antConfigService = antConfigService;
    }

    /**
     * 显示主页内容
     *
     * @return 系统信息
     */
    @RequestMapping(value = "/_help", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public Map<String, Object> index() {
        Map<String, Object> map = new HashMap<>(2);
        map.put("welcome", "hello web route service!");
        map.put("start_time", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        return map;
    }

    @ResponseBody
    @RequestMapping(value = "/_list", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public RestResponse<List<AntActionStatus>> getAllActions(//
                                                             @Parameter(name = "actionLab", description = "服务标签") @RequestParam(value = "actionLab", required = false) String actionLab,
                                                             @Parameter(name = "plugin", description = "子系统(插件)") @RequestParam(value = "plugin", required = false) String plugin) {
        log.debug("get all online the action list");

        RestResponse<List<AntActionStatus>> response = new RestResponse<>();
        try {
            List<String> nodes = antConfigService.getOnlineActionNames(null);
            List<AntActionStatus> onlineList = new ArrayList<>();
            for (String actionPoint : nodes) {
                if (StringUtils.isNoneBlank(plugin) && !actionPoint.endsWith("@" + plugin)) {
                    continue;
                }

                List<AntActionStatus> ndoes = onlineActionManager.getAllNodes(actionPoint);
                NodeDescription nodeDescription = antConfigService.getAction(actionPoint);

                for (AntActionStatus antActionStatus : ndoes) {
                    if (nodeDescription != null) {
                        antActionStatus.setName(nodeDescription.getName());
                    }
                    onlineList.add(antActionStatus);
                }
            }

            if (StringUtils.isNoneBlank(actionLab)) {
                List<AntActionStatus> resultList = new ArrayList<>();
                for (AntActionStatus antActionStatus : onlineList) {
                    AntActionLabel antActionLabel = antActionStatus.getActionLabelByCode(actionLab);
                    if (antActionLabel != null) {
                        resultList.add(antActionStatus);
                    }
                }
                onlineList = resultList;
            }

            response.setBody(onlineList);
        } catch (Exception ex) {
            log.error(String.format("get all online action list err. %s", ex.getMessage()), ex);
            response.setState(new SampleState(String.format("get all online action list err.%s", ex.getMessage()), ex));
        }
        log.debug("Fetch all online action list.response={}.", response);

        return response;
    }

    /**
     * 获取指定 能力服务的 使用状态
     *
     * @param actionPoint 会话能力服务名称
     *                    <p>
     *                    如中文： rest-ast-chin-v20@tuling-ast
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/{action_point:.+}/_list", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public RestResponse<List<String>> getAllActions(@PathVariable("action_point") String actionPoint) {
        log.debug("get the action [{}] list", actionPoint);

        RestResponse<List<String>> response = new RestResponse<>();
        try {
            List<AntActionStatus> nodes = onlineActionManager.getAllNodes(actionPoint);
            if (nodes.isEmpty()) {
                response.setState(new SampleState(String.format("the action [%s] no available endpoint", actionPoint), null));
            } else {
                List<String> availableEndpoint = nodes.stream().map(AntActionStatus::getRestWebUri).toList();
                response.setBody(availableEndpoint);
            }
        } catch (Exception ex) {
            log.error(String.format("get action list [%s] available endpoint error", actionPoint), ex);

            response.setState(new SampleState(String.format("get action [%s] available_endpoint error", actionPoint), ex));
        }
        log.debug("Fetch the action [{}] available endpoint.response={}.", actionPoint, response);

        return response;
    }

    /**
     * * 获取指定 能力服务的 可用访问地址
     *
     * @param action 会话能力服务名称
     *               <p>
     *               如中文： rest-ast-chin-v20@tuling-ast
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/{action_point:.+}/_available_endpoint", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public RestResponse<String> getEndpoint(@PathVariable("action_point") String action) {
        log.debug("get the action [{}] available endpoint", action);
        RestResponse<String> response = new RestResponse<>();
        try {

            AntActionStatus node = onlineActionManager.getNode(action);

            if (node != null) {
                response.setBody(node.getRestWebUri());
            } else {
                response.setState(new SampleState(String.format("the action [%s] no available endpoint", action), null));
            }
        } catch (Exception ex) {
            log.error(String.format("get action [%s] available endpoint error", action), ex);
            response.setState(new SampleState(String.format("get action [%s] available_endpoint error", action), ex));
        }
        log.debug("get the action [{}] available endpoint.response={}.", action, response);
        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/{action_point:.+}/_state_list", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public RestResponse<List<AntActionStatus>> getAllActionState(@PathVariable("action_point") String actionPoint) {
        log.debug("get the action [{}] available endpoint", actionPoint);
        RestResponse<List<AntActionStatus>> response = new RestResponse<>();
        try {
            List<AntActionStatus> objList = onlineActionManager.getAllNodes(actionPoint);
            if (objList.size() > 0) {
                ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
                NodeDescription nodeDescription = antConfigService.getAction(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
                if (nodeDescription != null) {
                    for (AntActionStatus antActionStatus : objList) {
                        antActionStatus.setName(nodeDescription.getName());
                    }
                }
            }
            response.setBody(objList);

        } catch (Exception ex) {
            log.error(String.format("get action [%s] available endpoint error", actionPoint), ex);
            response.setState(new SampleState(String.format("get action [%s] available_endpoint error.", actionPoint), ex));
        }
        log.debug("get the action [{}] available endpoint.response={}.", actionPoint, response);
        return response;
    }
}
