package skynet.platform.common.logging;


import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.logging.LogFile;
import org.springframework.core.env.Environment;
import skynet.boot.websocket.WebSocketConfig;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.exception.ActionNotExistException;

import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Log日志推送服务
 *
 * <pre>
 * // ws://127.0.0.1:8080/log/mq-vspp-v1_0@engine_1
 * </pre>
 *
 * <AUTHOR> [2017年12月1日 上午11:12:33]
 */
@Slf4j
public class BaseLogWebsocket {

    private final Environment environment;
    private final Map<String, LogFileWatcher> sessionLogFileWatcherMap;

    public BaseLogWebsocket(Environment environment) {
        this.environment = environment;
        this.sessionLogFileWatcherMap = new ConcurrentHashMap<>(0);
    }

    @OnOpen
    public void onOpen(@PathParam("actionId") String actionId, Session session, EndpointConfig config) throws IOException {

        try {
            InetSocketAddress clientHostIp = WebSocketConfig.getRemoteAddressBySession(session);
            log.debug("New Connected [sessionId={}][from={}][actionId={}] ... ", session.getId(), clientHostIp, actionId);

            String actionPoint = actionId.replaceAll("_\\d{1,100}", "");

            actionPoint = URLDecoder.decode(actionPoint, StandardCharsets.UTF_8);
            actionId = URLDecoder.decode(actionId, StandardCharsets.UTF_8);

            String logFileName = LogbackConfig.getLogFullFileName(actionId);
            // 排除自己
            if (StringUtils.equalsIgnoreCase(AppBootEnvironment.AGENT_ACTION_POINT, actionPoint) || StringUtils.equalsIgnoreCase(AppBootEnvironment.MANAGER_ACTION_POINT, actionPoint)) {
                logFileName = LogFile.get(environment).toString();
            } else {
                try {
                    logFileName = LogbackConfig.getConsoleLogFullFileName(actionId, environment);
                } catch (ActionNotExistException e) {
                    log.error("open the log file failed={}", e.getMessage());
                } catch (Exception e) {
                    log.error("open the log file failed：" + e.getMessage(), e);
                }
            }

            log.debug("LogFileName={}", logFileName);
            session.getBasicRemote().sendText(String.format("[logServer connected.][%s]", logFileName));

            LogFileWatcher logFileWatcher = new LogFileWatcher(new File(logFileName), session);
            this.sessionLogFileWatcherMap.put(session.getId(), logFileWatcher);
            logFileWatcher.watch();
        } catch (Exception e) {
            log.error("LogWebsocket onOpen error. actionId=" + actionId, e);
            throw e;
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug(String.format("sessionId[%s] : message[%S]", session.getId(), message));

        LogFileWatcher logFileWatcher = this.sessionLogFileWatcherMap.get(session.getId());
        logFileWatcher.isPause("pause".equals(message));
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.debug(String.format("Session %s closed because of %s", session.getId(), closeReason));
        try {
            if (sessionLogFileWatcherMap.containsKey(session.getId())) {
                this.sessionLogFileWatcherMap.remove(session.getId()).close();
            }

        } catch (Exception e) {
            log.error("close error:{}", e.getMessage());
        }
    }

    @OnError
    public void onError(Session session, Throwable t) {
        try {
            log.debug("LogPushServer WebSocket onError." + t.getMessage());
            if (sessionLogFileWatcherMap.containsKey(session.getId())) {
                this.sessionLogFileWatcherMap.remove(session.getId()).close();
            }

        } catch (Exception e) {
            log.error("close error.{}", e.getMessage());
        }
    }
}
