package skynet.platform.common;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.env.MockEnvironment;
import skynet.boot.SkynetProperties;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.boot.zookeeper.impl.ZkConfigServiceImpl;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.repository.config.impl.AntConfigServiceImpl;

import static org.junit.Assert.*;

/**
 * AntConfigServiceImpl LoadingCache功能测试
 * 测试Cache到LoadingCache的重构是否正常工作
 * 
 * <AUTHOR>
 */
public class AntConfigServiceImplLoadingCacheTest {

    private final String plugin = "turing-test";
    private final String action = "test-svc";
    private final String testIp = "127.0.0.1";

    private AntConfigServiceImpl antConfigService;

    @Before
    public void setUp() throws Exception {
        // 设置测试环境的ZK连接信息
        System.setProperty("skynet.zookeeper.cluster_name", "skynet");
        System.setProperty("skynet.zookeeper.server_list", "**************:2181");

        MockEnvironment mockEnvironment = new MockEnvironment();
        SkynetProperties skynetProperties = new SkynetProperties(mockEnvironment);
        skynetProperties.setActionTitle("[TEST]测试服务");
        skynetProperties.setActionDesc("测试服务");
        skynetProperties.setActionPoint("test-svc@turing-test");
        skynetProperties.setHome("/iflytek/server/skynet");

        SkynetZkProperties skynetZkProperties = new SkynetZkProperties();
        ZkConfigService zkConfigService = new ZkConfigServiceImpl(skynetZkProperties);

        antConfigService = new AntConfigServiceImpl(zkConfigService, skynetProperties);
    }

    /**
     * 测试LoadingCache的AntActionParam自动加载功能
     * 验证缓存机制是否正常工作
     */
    @Test
    public void testActionParamLoadingCache() throws Exception {
        try {
            // 第一次调用，应该从ZK加载数据
            AntActionParam actionParam1 = antConfigService.getActionParam(plugin, action);
            assertNotNull("ActionParam should not be null", actionParam1);
            assertEquals("Plugin should match", plugin, actionParam1.getPlugin());
            assertEquals("Action code should match", action, actionParam1.getCode());

            // 第二次调用，应该从缓存获取数据
            AntActionParam actionParam2 = antConfigService.getActionParam(plugin, action);
            assertNotNull("ActionParam should not be null", actionParam2);
            assertEquals("Plugin should match", plugin, actionParam2.getPlugin());
            assertEquals("Action code should match", action, actionParam2.getCode());

            System.out.println("✓ ActionParam LoadingCache test passed");
            System.out.println("ActionParam: " + actionParam1.toString());
            
        } catch (Exception e) {
            System.out.println("ActionParam LoadingCache test failed (expected if ZK not available): " + e.getMessage());
            // 在没有ZK环境的情况下，这是预期的行为
        }
    }

    /**
     * 测试LoadingCache的AntServerParam自动加载功能
     * 验证缓存机制是否正常工作
     */
    @Test
    public void testServerParamLoadingCache() throws Exception {
        try {
            // 第一次调用，应该从ZK加载数据
            AntServerParam serverParam1 = antConfigService.getServerParam(testIp);
            // 注意：如果ZK中没有该IP的数据，返回null是正常的
            
            // 第二次调用，应该从缓存获取数据
            AntServerParam serverParam2 = antConfigService.getServerParam(testIp);
            
            // 验证两次调用的结果一致性
            if (serverParam1 == null && serverParam2 == null) {
                System.out.println("✓ ServerParam LoadingCache test passed (no data in ZK)");
            } else if (serverParam1 != null && serverParam2 != null) {
                assertEquals("IP should match", testIp, serverParam1.getIp());
                assertEquals("IP should match", testIp, serverParam2.getIp());
                System.out.println("✓ ServerParam LoadingCache test passed");
                System.out.println("ServerParam: " + serverParam1.toString());
            }
            
        } catch (Exception e) {
            System.out.println("ServerParam LoadingCache test failed (expected if ZK not available): " + e.getMessage());
            // 在没有ZK环境的情况下，这是预期的行为
        }
    }

    /**
     * 测试缓存失效和重新加载功能
     */
    @Test
    public void testCacheInvalidation() throws Exception {
        try {
            // 创建一个测试用的服务器参数
            AntServerParam testServerParam = new AntServerParam();
            testServerParam.setIp(testIp);
            testServerParam.setName("Test Server");
            testServerParam.setType("test");

            // 设置服务器参数（这会清除缓存）
            antConfigService.setServerParam(testServerParam);

            // 获取服务器参数（应该从ZK重新加载）
            AntServerParam retrievedParam = antConfigService.getServerParam(testIp);
            
            if (retrievedParam != null) {
                assertEquals("IP should match", testIp, retrievedParam.getIp());
                System.out.println("✓ Cache invalidation test passed");
            } else {
                System.out.println("✓ Cache invalidation test passed (no data retrieved)");
            }

            // 删除服务器参数（这也会清除缓存）
            boolean deleted = antConfigService.delServerParam(testIp);
            assertTrue("Delete should succeed", deleted);
            
            System.out.println("✓ Cache invalidation and cleanup test passed");
            
        } catch (Exception e) {
            System.out.println("Cache invalidation test failed (expected if ZK not available): " + e.getMessage());
            // 在没有ZK环境的情况下，这是预期的行为
        }
    }

    /**
     * 测试错误处理和边界情况
     */
    @Test
    public void testErrorHandling() throws Exception {
        try {
            // 测试空参数处理
            AntActionParam nullParam = antConfigService.getActionParam("", "");
            fail("Should throw exception for empty parameters");
        } catch (Exception e) {
            System.out.println("✓ Empty parameter handling test passed: " + e.getMessage());
        }

        try {
            // 测试不存在的Action
            AntActionParam nonExistentParam = antConfigService.getActionParam("non-existent-plugin", "non-existent-action");
            fail("Should throw exception for non-existent action");
        } catch (Exception e) {
            System.out.println("✓ Non-existent action handling test passed: " + e.getMessage());
        }

        // 测试空IP处理
        AntServerParam nullServerParam = antConfigService.getServerParam("");
        assertNull("Should return null for empty IP", nullServerParam);
        System.out.println("✓ Empty IP handling test passed");
    }
}
