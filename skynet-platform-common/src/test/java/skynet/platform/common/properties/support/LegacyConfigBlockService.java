package skynet.platform.common.properties.support;

import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.StringUtils;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetConfigService;
import skynet.platform.common.repository.config.setting.SkynetConfigServiceImpl;
import skynet.platform.common.repository.config.setting.SkynetConfigType;
import skynet.platform.common.repository.config.setting.block.data.ConfigBlock;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.CommentProperties;

import java.io.IOException;
import java.util.Map;

/**
 * 历史配置服务接口（集群配置块和服务配置块)
 * <p>
 * since 3.4.11
 *
 * <AUTHOR> 2023年09月26日20:52:09
 */
@Slf4j
public class LegacyConfigBlockService {

    private final SkynetConfigService skynetConfigService4Properties;
    private final SkynetConfigService skynetConfigService4Logger;

    public LegacyConfigBlockService(IAntConfigService antConfigService) {
        this.skynetConfigService4Properties = new SkynetConfigServiceImpl(antConfigService, SkynetConfigType.PROPERTY);
        this.skynetConfigService4Logger = new SkynetConfigServiceImpl(antConfigService, SkynetConfigType.LOGGER);
    }

    /**
     * 集群配置
     * <p>
     * 包含 logger 和 基本 props
     *
     * @return
     */
    public ConfigBlock getGlobal() throws IOException {
        log.debug("get getGlobalProps ConfigBlock");
        String sb = skynetConfigService4Properties.getGlobalProps() + System.lineSeparator() +
                convert(skynetConfigService4Logger.getGlobalProps());
        return new ConfigBlock().setCode("global")
                .setName("集群级配置").setIndex(Integer.MAX_VALUE).setText(sb);
    }

    /**
     * 系统级别 属性
     * <p>
     * 包含 logger 和 基本 props
     *
     * @param plugin
     * @return
     */
    public ConfigBlock getPlugin(String plugin) throws IOException {
        Assert.hasText(plugin, "plugin is blank.");
        log.debug("get plugin ConfigBlock ={}", plugin);
        String sb = skynetConfigService4Properties.getProps(plugin) + System.lineSeparator() +
                convert(skynetConfigService4Logger.getProps(plugin));
        return new ConfigBlock().setCode(plugin)
                .setName(String.format("[%s]系统级配置", plugin)).setIndex(Integer.MIN_VALUE).setText(sb);
    }


    /**
     * 服务级别 属性
     * <p>
     * 包含 logger 和 基本 props
     *
     * @param actionPoint
     * @return
     */
    public ConfigBlock getAction(String actionPoint) throws IOException {
        Assert.hasText(actionPoint, "actionPoint is blank.");
        log.debug("get actionPoint ConfigBlock ={}", actionPoint);

        ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
        String plugin = actionNameContract.getPluginCode();
        String action = actionNameContract.getActionCode();
        String sb = skynetConfigService4Properties.getProps(plugin, action, null) + System.lineSeparator() +
                convert(skynetConfigService4Logger.getProps(plugin, action, null));
        return new ConfigBlock().setCode(actionPoint)
                .setName(String.format("[%s]服务级配置", actionPoint)).setIndex(Integer.MIN_VALUE).setText(sb);
    }


    private String convert(String loggers) throws IOException {
        if (StringUtils.isBlank(loggers)) {
            return "";
        }
        log.debug("convert loggers={}", loggers);
        CommentProperties commentProperties = new CommentProperties();
        commentProperties.load(loggers);

        for (Map.Entry<String, String> item : commentProperties.getAllProperty().entrySet()) {
            commentProperties.setProperty(String.format("%s%s", "logging.level.",
                    item.getKey()), item.getValue(), commentProperties.getComment(item.getKey()));
            commentProperties.remove(item.getKey());
        }
        return commentProperties.store();
    }
}
