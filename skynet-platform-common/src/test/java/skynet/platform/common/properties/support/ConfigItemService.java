package skynet.platform.common.properties.support;

import com.alibaba.fastjson2.JSON;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.platform.common.repository.config.setting.block.ConfigBlockService;
import skynet.platform.common.repository.config.setting.block.data.ConfigBlock;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.CommentProperties;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * 配置项Service
 */
@Slf4j
public class ConfigItemService {

    private final ConfigBlockService configBlockService;
    private final LegacyConfigBlockService legacyConfigBlockService;

    public ConfigItemService(ConfigBlockService configBlockService, LegacyConfigBlockService legacyConfigBlockService) {
        this.configBlockService = configBlockService;
        this.legacyConfigBlockService = legacyConfigBlockService;
    }

    public Map<String, Object> getActionProperties(String actionPoint, List<String> dependOnConfigBlockCodes) throws Exception {
        Assert.hasText(actionPoint, "actionPoint is blank.");
        Assert.notNull(dependOnConfigBlockCodes, "dependOnConfigBlockCodes is blank.");
        log.debug("actionPoint={};dependOnConfigBlockCodes={}", actionPoint, dependOnConfigBlockCodes);
        // 设置action properties属性,注意是从外到内的覆盖机制
        ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
        String plugin = actionNameContract.getPluginCode();
//        String action = actionNameContract.getActionCode();

        List<ConfigBlock> configBlockList = new ArrayList<>(dependOnConfigBlockCodes.size() + 3);

        //注意顺序：集群级，系统级，依赖配置块，服务级（优先级从低到高）
        configBlockList.add(legacyConfigBlockService.getGlobal());
        configBlockList.add(legacyConfigBlockService.getPlugin(plugin));
        for (String code : dependOnConfigBlockCodes) {
            ConfigBlock configBlock = configBlockService.fetch(code);
            if (configBlock != null) {
                configBlockList.add(configBlock);
            }
        }
        configBlockList.add(legacyConfigBlockService.getAction(actionPoint));

        // 兼容 集群级+系统级, 并将其合并
        Map<String, Object> map = new LinkedHashMap<>();
        for (ConfigBlock item : configBlockList) {
            CommentProperties commentProperties = new CommentProperties();
            if (StringUtils.isNotBlank(item.getText())) {
                commentProperties.load(item.getText());
                map.putAll(commentProperties.getAllProperty());
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("The action full properties:={}", JSON.toJSONString(map));
        }
        return map;
    }
}
