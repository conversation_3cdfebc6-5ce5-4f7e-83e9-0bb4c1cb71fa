文档密级：公司内部A



# **skynet3.0使用手册**

# **Skynet Platform 3.0 Instructions**

<table>
    <tr>
     <td>当前版本：</td> 		
     <td>V1.0</td>
    </tr>
        <tr>
     <td>作    者：</td> 		
     <td>谭现成</td>
    </tr>
        <tr>
     <td>完成日期：</td> 		
     <td>2020.12.28</td>
    </tr>
</table>






| **序号** | **版本** | **编写/修订说明**                 | **修订人** | **修订日期** | **备注** |
| -------- | -------- | --------------------------------- | ---------- | ------------ | -------- |
| 1        | V0.1     | skynet3.0总体介绍和各功能模块介绍 | 谭现成     | 2020/12/28   |          |
| 2        | V1.0     | skynet3.0平台使用说明补充         | 谭现成     | 2020/03/04   |          |

目录

- [1. skynet概述](#1-skynet概述)

- [2. 功能说明](#2-功能说明)

  - [2.1 功能特性](#21-功能特性)

  - [2.2 功能模块](#22-功能模块)

- [3. 平台登录和用户密码修改](#3-平台登录和用户密码修改)

  - [3.1 平台地址](#31-平台地址)

  - [3.2 用户登录](#32-用户登录)

  - [3.3 用户名修改](#33-用户名修改)

- [4. 服务管理](#4-服务管理)

  - [4.1 页面介绍](#41-页面介绍)

  - [4.2 服务汇总信息](#42-服务汇总信息)

  - [4.3 服务筛选](#43-服务筛选)

  - [4.4 服务操作](#44-服务操作)

    - [4.4.1 控制台日志](#441-控制台日志)

    - [4.4.2 服务分配](#442-服务分配)

    - [4.4.3 服务启用/重启/禁用](#443-服务启用-重启-禁用)

  - [4.5 服务展示](#45-服务展示)

    - [4.5.1 服务器视图类别](#451-服务器视图类别)

    - [4.5.2 服务视图类别](#452-服务视图类别)

  - [4.6 服务节点信息查看](#46-服务节点信息查看)

  - [4.7 服务右键信息查看](#47-服务右键信息查看)

- [5. 节点管理](#5-节点管理)

  - [5.1 页面介绍](#51-页面介绍)

  - [5.2 节点筛选](#52-节点筛选)

  - [5.3 节点操作](#53-节点操作)

    - [5.3.1 控制台日志](#531-控制台日志)

    - [5.3.2 节点添加](#532-节点添加)

    - [5.3.3 节点启动/停止/删除](#533-节点启动-停止-删除)

    - [5.3.4 节点信息](#534-节点信息)

 - [6. 服务定义](#6-服务定义)

   - [6.1 页面介绍](#61-页面介绍)

   - [6.2 集群配置](#62-集群配置)

   - [6.3 应用系统](#63-应用系统)

   - [6.4 服务节点操作](#64-服务节点操作)

     - [6.4.1 新建服务节点](#641-新建服务节点)

     - [6.4.2 编辑/删除服务节点](#642-编辑-删除服务节点)

- [7. 集群监控](#7-集群监控)

  - [7.1 集群概述](#71-集群概述)

  - [7.2 信息摘要](#72-信息摘要)



# 1. skynet概述
skynet是集开发、运维为一体的微服务托管平台。用户可以通过该平台的节点管理、服务管理，对服务器以及服务器上的服务可视化操作。平台还包括服务定义、集群控制、辅助工具等功能模块。



# 2. 功能说明


## 2.1 功能特性

skynet3.0作为平台的最新版本，具备以下功能特性：

1）全新交互：图聆UI风格，人性化交互，从不同角度场景定位操作、高亮提示等提高运维效率。

2）分类聚焦；将服务器、服务定义、服务管理分层分类，并根据各模块的特点加强了优化完善。

3）安全可靠：对访问接口进行skynet-boot-auth签名、jwt token认证、base-auth分级鉴权验证。

4）开放共享：提供丰富的WebAPI接口以及java-feign-sdk，方便第三方集成服务托管治理操作。



## 2.2 功能模块

![](asserts/46e5892ae33c46bfbf73453a0445749a.png)

共有7种功能模块：服务管理、节点管理、服务定义、集群监控、辅助工具、基础平台-日志、基础平台-监控，其中“基础平台-日志”与“基础平台-监控”为可选功能模块。下面是模块功能说明：

**服务管理**：对服务节点进行展示，展示服务节点的统计信息，并能实时展示服务节点的运行状态。支持运行节点的各种操作，包括启用、禁用、日志查看、服务定义查看等功能，是我们在平台使用过程中主要进行操作的页面。

**节点管理**：对服务器进行管理，主要功能包括服务器注册、版本更新以及服务器信息查看等功能。在对线上环境进行运维时，通过平台的这一页面，即可基本上满足能力服务在运维过程中的环境信息获取需求。

**服务定义**：展示服务节点的配置信息，最常用配置为接口配置，不同的服务节点配置不同。服务定义支持导入导出，一般发布版本上的服务定义均已配置完成，不需要再进行配置。

**集群监控**：集群的监控信息，需要启动Skynet监控/仪表盘方可进行查看。除了可以查看集群的整体监控信息外，还可以查看不同主机的运行信息。

**辅助工具**：平台使用过程的常用的一些小工具，包括加密/解密、编解码以及数据JSON结构化。

**集成平台-日志**：平台的日志模块，为可选功能模块，平台日志模板采用的是ELK的解决方案。如果需要使用日志模块，需要申请相关的日志模块组件。

**基础平台-监控**：平台的监控模块，可以监控服务节点的资源使用信息以及运营信息（服务节点需支持）。如果需要使用监控模块，需要申请相关的日志模块组件。


# 3. 平台登录和用户密码修改



## 3.1 平台地址

平台部署完成后，访问地址为：http://{部署服务器IP}:2230


## 3.2 用户登录

在页面右侧输入用户名密码之后（默认用户名/密码为admin/Skynet@2230），即可进入到skynet后面管理界面。

![](asserts/9b1ea5c6840c437284d68f430d2181b0.png)



## 3.3 用户名修改

在登陆区域，可以选择“修改密码”进行密码修改，下面是密码修改页面，可以在此页面进行密码修改。

![](asserts/003a602bd696e01733e52dfe6ed9248b.png)



# 4. 服务管理



## 4.1 页面介绍

通过点击左侧的导航区的“服务管理”进入到服务管理页面。在服务管理页面，上侧展示一些服务汇总信息、服务筛选功能区，中间展示服务相关操作按钮，下方是服务展示区。

![](asserts/44eebe600de6561d20e96223458558cd.png)



## 4.2 服务汇总信息

在页面的左上角显示服务器汇总信息，包括服务器数量、服务器正常启动数量、服务器异常启动数量。右上角显示服务汇总信息，包括服务数量、正常启动服务数量、已停止服务数量、启动中服务数量以及启动失败服务数量。

![](asserts/e7b955c87a522986bd282234ba3190da.png)



## 4.3 服务筛选

可以在界面上方，对页面的服务进行筛选。筛选维度包括应用系统、服务器标签、服务器状态、服务状态、服务名称。下面分别对这几个筛选维度进行介绍：

应用系统：通过应用系统对服务进行筛选，应用系统指的的服务定义里面的应用系统。通过选择某个应用系统，下面展示该应用系统下面的所有服务。

![](asserts/112488c6404d595b3cdcb780909ce7f9.png)

服务器标签：通过选择服务器标签，可以在下方快速筛选出具备该标签的服务器。如某现场具备大量的转写服务器，可以通过为这些转写服务器打上转写标签（在节点管理功能模块操作），从而清晰的展示具备转写能力的节点。

![](asserts/ad39216d6f5ce51f36aadc9a2cf58cba.png)

服务器状态：可以通过选择服务器正常状态或者服务器异常状态的服务器，快速找到不同服务器状态下的服务节点。

![](asserts/b2297915cab2ae9f3a06806eea88a4df.png)

服务状态：通过选择不同状态的服务节点状态，对不同状态的服务节点进行快速筛选，服务状态包括正常、停止、启动中和启动失败。

![](asserts/85848228129adbe57f0c800ee4480d3f.png)

服务名称：通过输入服务名称的某些字段，可以以通配方式对服务名称进行搜索

![](asserts/c9591ba45353f6107b6ed13e8141bb84.png)



## 4.4 服务操作

服务操作主要包括控制台日志、服务分配、启用、重启与禁用操作。下面对这些操作进行分别介绍：


### 4.4.1 控制台日志

控制台日志查看的是skynet管理界面xmanager的日志。只要在界面上点击“控制台日志”，即可直接进入到控制台日志页面。

![](asserts/49355ca2170972b4345077ddfa1da3b5.png)

控制台日志页面如下图所示，日志查看页面上面包含一些功能按键，辅助进行日志查看，下面是这些辅助按键的功能介绍：

1）刷新：对日志文件显示区的文件进行刷新操作；

2）暂停跟踪：控制台日志默认会对文件进行持续追踪操作（类似于tail –f命令），点击暂停追踪会停止这种追踪，此时该按钮变为继续追踪，可以通过点击继续追踪进行日志的继续追踪；

3）清空：清空日志文件显示区的日志文件显示；

4）删除：删除控制要日志文件；

5）下载：对控制台日志文件进行下载。

![](asserts/6cf09528170a33ac53f25fdcff4d7b7b.png)



### 4.4.2 服务分配

服务分配功能是为注册的服务器节点分配相应的服务/为对应的服务分配注册的服务器节点，通过视图类别进行切换。已服务器视图为例，在界面上点击“服务分配”，进入到服务分配页面。


#### 服务器视图

![](asserts/1ed8377101c04efe01fa5ec647825bb0.png)

下面是服务分配页面说明，在服务选择区选择需要分配的服务，在待选服务列表显示的是已经注册的服务，通过![](asserts/686dacb62102f1deb5c95f7f86e55a1c.png)图标，可以将服务从待选列表移到已分配服务列表。服务会在分配的服务器上进行部署。![](asserts/d39ced61e9bd91d0a08c31dd952a0431.png)图标会将左右列表进行重置。

![](asserts/690d1aeb63c54eb7bbc73c90a8abb5bd.png)

标签:可为服务添加自定义标签，并在分配时显示

![](asserts/4aba087bfb02d77ef388269850241d50.png)



### 4.4.3 服务启用-重启-禁用

服务启用、重启以及禁用是管理界面对于服务节点进行的几种操作

![](asserts/c316033407c7a0e150002a9d0d9c5e1a.png)

使用这三个按钮前，需要先勾选服务节点，先选择“多选”，然后选择需要进行操作的服务节点，然后选择相应的操作按钮即可

![](asserts/f7502ddd2c57bbc653f7a51e6c56e854.png)

点击操作之后，会弹出确认框，通过点击验证码滑动滑块进行确认操作

![](asserts/380a471cada59a2759d466a9b4526853.png)
 

## 4.5 服务展示

服务节点支持多种展示方式，包括按照视图类别进行展示、按照视图状态进行展示，并且支持按照“平铺”、“分组”、“折叠”等显示形式进行展示，下面对这些服务展示方式进行介绍。


### 4.5.1 服务器视图类别

服务器视图类别显示，这种显示方式和skynet3.0之前版本显示方式一致，以服务器为显示主体，并显示不同服务器上面的服务节点。下面是“平铺”显示方式展示样式，直接按照服务分配顺序进行服务展示：

![](asserts/94ca0a170518ef9a3a4fbb7da8bb9c7d.png)

选择“分组”方式，会使服务按照应用系统进行分组展示

![](asserts/2033ac065bc501d90e99ce7a956dfad3.png)

选择“折叠”方式，会使对服务节点按照应用系统进行折叠展示，展示折叠的服务节点数量

![](asserts/a758c9397d48a25773ba761bebb7098b.png)

在服务器视图类别下，选择收起，下面展示区就只展示服务器，而服务器上的服务节点将不再展示

![](asserts/937b1d3d2a89359a5008834863941bb3.png)

服务器视图类别下，可以进行服务分配操作，此时服务分配页面，是为改服务器节点分配不同的服务节点。

![](asserts/978dc7816e039a66afa8cda0d4b4a1b0.png)


### 4.5.2 服务视图类别

服务视图类别展示，是以服务节点为主体，展示不同服务节点在各个服务器上的部署情况，下面是“平铺”显示下的页面

![](asserts/668ad7cc068b87062e1db2b3ba8580e8.png)

“分组”显示下的页面展示

![](asserts/68c0d06f518f70184ddcaab0312950c6.png)

“折叠”显示下的页面展示，显示分配在服务器上的该服务节点的数量（同一种服务可以在一个服务器上分配多个节点）

![](asserts/c27ff4b3eb95a191c59d0ab77e1f98d9.png)

服务视图类型下服务分配视图，是以服务为主体，将服务分配到不同的服务器节点，左侧为未分配的服务节点，右侧为已分配的服务节点

![](asserts/2091a967509949c297dc29b072f1c0f8.png)


## 4.6 服务节点信息查看

服务节点启动之后，可以通过点击方式查看服务信息，主要包括服务标识、服务坐标、服务类型、服务地址、启动时间、运行时长以及PID信息，这些信息都支持单击进行复制。并且支持服务首页（服务节点的首页页面，通过服务定义配置，可能不存在）、服务定义、服务属性、启动详情、服务日志、标准输出、健康监测和监控图表等信息页面跳转。

![](asserts/c9a9538a4cb8a5a9bf7b7ffab3a4524b.png)

服务定义：跳转到该服务节点的服务定义页面，服务定义页面的具体配置，可以参考后面内容

![](asserts/1e471dcd53b35a070bcac81874038efc.png)

服务属性：新打开一个窗口，展示服务的属性，属性主要是服务定义里面的信息结构化展示

![](asserts/c8c4c90b923e1c91ab43138fc3c51ad1.png)

启动详情：skynet3.0启动服务本质上是调用启动服务的命令进行服务启动，点击启动详情，即可以查看启动命令。并可以通过右上角的“复制命令”，对命令直接进行复制，然后到服务节点所在服务器上进行粘贴执行。

![](asserts/7519622bae06a08680ce9dc4ca5944b8.png)

通过右上角的详细数据，可以查看更详细的信息

![](asserts/c8e78a9af1a24ca0cb5db846667833f7.png)

服务日志：点击服务日志，进入到服务日志显示页面，日志的功能按键和上面介绍的作用一致

![](asserts/43e60c3fcca8bbeaa371a38c3beefb43.png)

标准输出：输出服务节点的标准输出

![](asserts/d680a34a822bf419fde14c78a2d173db.png)

健康监测：实时展示服务节点的健康监测日志信息，健康监测状态决定服务节点颜色（通过健康监测的服务节点颜色为蓝色）

![](asserts/78e45fe5a29fcb04416514658a2e16e2.png)

监控图表：实时展示服务器的基本信息，如CPU使用率、内存使用量、线程数、文件句柄数目。

![](asserts/eb0814b509b2665d4c55f700ffbe4a78.png)

右上角可以选取对应的时间范围进行查看和图表的刷新频率。


## 4.7 服务右键信息查看

除了通过点击方式进行信息查看之外，还可以在服务节点上右键，查看相应信息，目前支持的右键菜单为“查看-服务定义”、“查看-服务配置”、“查看-启动详情”、“查看-健康监测”、“查看-标准输出”、“查看-日志输出”、“查看-监控图表”、“启停-重启服务”、“启停-禁用服务”，除了启停的两个操作外，其它的操作和上面的功能一致。

![](asserts/e237f5368e3e386213e4b59fdb65b0c2.png)

“ 启停-重启服务”是对服务进行重启操作，点击后，通过在弹出的确认框中进行确认，即可对所选择服务进行重启

![](asserts/4de13c4253d10be7497bfb971cf716c3.png)

“启停-禁用服务”是对选择服务节点进行禁用操作，点击后，服务改为禁用状态，该按键变为“启停-启用服务”

![](asserts/af7253377c2816b2d492ad4052cf4bdf.png)


# 5. 节点管理


## 5.1 页面介绍

节点管理模块，主要是对注册服务器进行管理，通过点击左侧导航区的“节点管理”进入到该模块。节点管理页面整体分为筛选区、功能区以及展示区，下面对这些区域进行分别介绍。

![](asserts/1f6f6e40e9dd50c6f7e6b9bf7c57d0c3.png)


## 5.2 节点筛选

可以页面的筛选区，对页面的节点进行筛选。筛选维度包括状态、标签、IP地址等维度。下面分别对这几个筛选维度进行介绍：

状态：通过服务器节点状态对节点进行筛选展示，状态支持“在线”、“离线”以及“分发中”三种状态。选择相应的状态，下面会实时显示该状态的服务器节点。

![](asserts/caa30c12f6e1b9c0b956182555ddc591.png)

标签：通过选择标签选择具备该标签的服务器节点，选择后，下面展示区会展示具备该标签的服务器

![](asserts/8838636bed9b7673c1f42a5da615c1e6.png)

IP地址：通过输入IP地址，会筛选出和输入内容通配的服务器节点

![](asserts/f51633268e59c5c22d1aa57a97116548.png)


## 5.3 节点操作

节点操作主要包括控制台日志、添加、启动、停止、删除和更新版本等功能，下面对这些功能进行说明。


### 5.3.1 控制台日志

节点管理下的控制台日志，和服务操作下的控制台功能一致，具体介绍可以参考4.4.1一节


### 5.3.2 节点添加


#### 服务器节点

节点添加是skynet3.0平台的一个重点功能，作用是将服务器节点（在平台上又称为Agent）注册到Skynet平台进行管理。通过点击“添加”按钮，弹出服务器节点注册框

![](asserts/54e7b9e8461751d414a958e15d1b0b76.png)

在弹出框上，主要需要输入的是“IP地址”和“SSH密码”，SSH端口默认设为22（可更改），SSH用户默认为root（可更改，但是推荐使用root用户进行注册）

![](asserts/8bf0fe683cd219b1805ad64496d6a5e9.png)

填写完IP地址、SSH端口、SSH用户和SSH密码之后，可以通过左下角的“测试SSH连接”对填写内容进行测试，测试根据填写内容是否能够进行SSH连接

![](asserts/8f754e82b43bc08becb0ce58548a4b50.png)

备注和服务器标签，为选填项内容，这两项内容都是为了将服务器更好的进行分类，如按照能力分类（转写服务器、翻译服务器），或者按照服务器配置分类（GPU服务器、CPU服务器），或者按照应用场景分类（GCH服务器、一体机服务器），实际使用中，用户可以自己进行选填。

![](asserts/3497bd834613b8884ff5db4441716a38.png)

点击服务器标签右侧的“+ 选择标签”，即可以进入到选择标签页面，此页面可以新增标签或者选择已有的标签，标签支持多选

![](asserts/d78a3655b66e4212d4c743a5b5045cc0.png)

注册时可以选择是否进行自动分发，自动分发是将skynet以及repo下面的文件远程拷贝到新的服务器。选择自动分发之后，可以选择是否自动安装docker，因为skynet有些组件是用docker封装的，这些组件的启动依赖docker环境。如果该服务器已经安装了docker，则不会进行docker安装。

![](asserts/c47b28b4b837b657d795854aa0db03c1.png)


#### Kubernetes节点

新增Kubernetes节点功能，作用是将Kubernetes节点注册到Skynet平台进行管理

通过点击节点类型进行切换

![](asserts/dea3e3c1842ab58bc4b6bea3846ecea7.png)

其中必填项是KubeConfig(\~/.kube/config文件里的内容，包含了集群、用户、命名空间和认证机制信息)和镜像仓库地址，若镜像仓库有密码，需要输入用户名和密码，备注、标签、测试与服务器节点操作一样，同服务器节点

![](asserts/b3dc7d458ed6bd999c295f07dd58f1a5.png)


### 5.3.3 节点启动-停止-删除

节点启动/停止/删除等操作，是对注册服务器的状态进行更改的相关操作。首先框选需要操作的服务器节点、然后点击启动或者停止，可以对框选的服务器执行相应的操作。而且在操作的时候，可以选择是否对服务器节点上的服务节点进行操作。

![](asserts/72df021a1c46c628f3221234ffb99579.png)

此时会弹出启动成功或者失败的提示，但是需要说明的是，这个提醒说明的是操作的执行状态，并不代表服务器已经启动成功

![](asserts/8aa56a54f9c15fefb0b3c2b511484bf2.png)

更新版本是为了将最新的版本更新到注册的服务器节点，需要注意的是，在更新之前，需要将服务器停止，否则会弹出失败

![](asserts/01d5800a6f9ce449400c07da8bc45f9d.png)


### 5.3.4 节点信息

节点信息主要是展示服务器节点的IP、主机名、状态、硬件信息（CPU\|内存\|GPU）、操作系统、分类标签、备注、服务版本以及操作功能栏（启动、停止和更新版本，功能和5.3.3一致）

![](asserts/57d86f6743bbed27b1dd4b80dcffc745.png)

除了这些信息外，还可以通过点击蓝色的IP地址，进入到节点详情页面进行查看。下面是概览信息页面，主要是展示基本信息、版本信息、运行时信息以及SpringBoot Endpoints节点信息。


#### 服务器节点

![](asserts/79f71a9d9feb095be8c0814756f13914.png)

系统环境是一个很重要的功能，它展示了该服务器的一些重要信息，目前包括Agent日志、IO统计、进程内存TOP N、进程CPU TOP N、硬件信息、系统信息、服务器状态、网络环境、Java环境、Python环境、Docker环境、GPU环境以及AI引擎授权等各类信息，下面对这几种信息进行说明。

![](asserts/5a7181eebc3aa4672d534404c7cadd2a.png)

Agent日志：查看服务器节点的Agent日志文件，可以查看Agent日志、Skynet目录、Skynet版本信息、以及SkynetZeekeeper配置

![](asserts/88649b5a40f85020f7556e14ea6ab1eb.png)

Skynet目录tab下，可以查看Skynet安装目录的信息，下面是目录信息展示页面

![](asserts/aa00fb290c194cf682809e81d5fd2eea.png)

Skynet版本信息：可以查看该服务器节点的Skynet版本信息，主要是版本号、构建时间以及构建分支等信息

![](asserts/892be8282aea987dae16ba0d5108da9b.png)

SkynetZeekeeper配置：skynet-agent和skynet-manger版本信息，主要是zookeeper相关配置信息，如服务器IP地址、session过期时间、连接过期时间以及Authorization配置信息等

![](asserts/34214bf74e453ce4f4dff2924a2de832.png)

IO统计：通过我们编写的脚本（需要服务器支持iostat命令），统计CPU磁盘的使用情况

![](asserts/8f45af9679acf9af15969ae26a350f02.png)

进程内存TOP N：展示该服务器节点的内存TOP N使用情况，展示20个占用内存最多的进程，以及进程的执行命令，以便定位该进程

![](asserts/bb0a545c07e215c8466ee06034af1cb9.png)

进程CPU TOP N：展示该服务器节点的CPU TOP N使用情况，展示20个占用内存最多的进程，以及进程的执行命令，以便定位该进程

![](asserts/3b3046a16e2a91aea6ef0f58f02f08d1.png)

硬件信息：展示服务的硬件信息，包括CPU信息、块设备信息、分区表、硬盘阵列信息以及网卡信息，下图以CPU信息为例，其它信息就不一一说明

![](asserts/d9806d593ff145f9371104197263ab23.png)

系统信息：展示服务器的系统信息，包括操作系统、shell类型、环境变量、glibc版本、资源限制以及selinux信息，其中操作系统信息为重要信息，方便现场进行信息收集以及问题定位

![](asserts/98f051f0c892d675359cc759a7138ef3.png)

服务器状态：可以查看服务器的内存状态、磁盘状态以及服务器时间信息

![](asserts/84382d6af09a7b5687bbc3f4ee1f510a.png)

网络环境：可以查看服务器的端口占用、连接状态、IP配置、Host名称、Host配置、DNS配置以及防火墙状态

![](asserts/42d2b607264d3376e8d3902f1b0d2cc9.png)

Java环境：查看服务器的Java环境信息，包括JDK版本、Java进程、JDK部署目录信息

![](asserts/5e5d90965a4fa12aea42a7c352f316f7.png)

Python环境：查看服务器节点的Python信息，包括Python信息以及Python部署目录

![](asserts/edbd69b5771c62836b440353ca6f0261.png)

Docker环境：查看服务器的Docker版本、以及运行中和已经停止的Docker容器，并能查看Docker镜像以及Docker配置

![](asserts/82c3c6da8d3f5ee8c6edaaf3410ce690.png)

GPU环境：查看服务器的GPU信息，包括CUDA版本、GPU信息、GPU状态，其中GPU状态信息能够查看服务器的显卡类型以及显卡驱动版本

![](asserts/fade7dadd331f18e3b1ebee765c533a2.png)

AI引擎授权：可以查看该服务器上的授权信息，目前支持Skylock、Hasp以及Hasp_Authcode授权（云锁目前不支持命令查看）。需要说明的是，因为授权的组件较大，没有放在Skynet平台基线版本里面，如果需要的话，可以申请，然后放在{Skynet安装目录}/bin/utils目录下

![](asserts/7287c03181150accc9422d35f83cfd06.png)

服务日志显示的是ant-xagent@ant的服务日志，该日志显示xagent（服务器几点）的操作日志

![](asserts/4ab0c49815c109027df86e2596808c2f.png)

事件通知显示xagent的相关通知信息，主要是xagent上面服务节点之间的通信信息，如健康状态检查等信息

![](asserts/5eebdf1624c8fd1d232f9ee2a8d43b79.png)

分发日志显示服务器注册时的分发信息，包括docker安装信息以及部署包传输信息等

![](asserts/89431c4ab247aca6a6b100a00ec53d67.png)


#### Kubernetes节点

集群概述展示了配置好的集群的基本信息、版本信息、运行时信息、计算资源等信息

![](asserts/904dcec5dfb9ab49ee1edac84e869a4b.png)

分发日志显示服务器注册时的分发信息，包括docker安装信息以及部署包传输信息等

![](asserts/7fed68313c1efbcc60f21e39e28304ff.png)

主机：展示主机的各类基本信息，同时可以进行终端访问、封锁调度、排空节点、查看YAML等功能

![](asserts/eaedd48922e92241e4eccf17163eaa46.png)

工作负载下包含了一系列类别，如负载概览、容器组、部署、守护进程集等类别，下述详细介绍各个类别：

负载概览：展示所有节点的状态统计，如Pods、Deployments、DaemonSets等，同时可选择指定的命名空间，下方会展示一系列的事件，包含事件的类型，信息，发生的命名空间等

![](asserts/f0b482f2213a5fee9227c42ad3a37ef1.png)

容器组：每个Pods的基本信息，包含每个Pods中的容器运行状态，重启次数，以及操作等

![](asserts/0ffa4400e977e80ede066928cef78251.png)

可以通过YAML文件，点击右上角“新建”按钮创建

部署:每个Deployments的基本信息，包含名称、命名空间、期望、当前、就绪等操作

![](asserts/f059d8ecc37f075baeb761668658c9c5.png)

点击操作的下拉按钮，可以进行查看YAML、日志/终端、伸缩、重启、删除操作

![](asserts/e9e1853189821ecc9844c49ba305abad.png)

伸缩，可调整对应的副本数

![](asserts/11896d5f8e8c4bfb0fd0045485c99b34.png)

可以通过YAML文件，点击右上角“新建”按钮创建

![](asserts/6a263ff7ad95d7ac3363fa73e2d438d4.png)

守护进程集：其中包含五个状态Desired, Current, Ready, Up to Date, Available的情况

![](asserts/4d22ac9181215c73e61bb0935fa9dcbb.png)

操作包含查验YAML、日志/终端、重启、删除

![](asserts/082100b74f195bfe638765402f92b1e2.png)

可通过点击右上角“创建按钮”通过YAML文件创建![](asserts/6430005f96132d55eb53e4d388b5b4e0.png)

有状态副本集：同守护进程集

![](asserts/280facffae1f505d4e0f9ae128b14fb5.png)

操作:

![](asserts/9502f063e379bb9d16e95081cb1dd86b.png)

新增

![](asserts/895b578256d2629fc348e39f988fdf18.png)

定时任务：创建的定时任务的名称，命名空间，Cron表达式等，可通过右上角“创建按钮”新增定时任务

![](asserts/3c09629483eb88cbd41f6c0ae184e4fa.png)

![](asserts/20a8079a59b1946f0e70ad31831b54b2.png)

任务：任务的名称、命名空间、成功数、总数、开始时间、完成时间等信息，可通过右上角“创建按钮”新增任务

![](asserts/1a03e11677711e4c40744f49dcbc829d.png)

![](asserts/1e6b52d27045d3072614074c52ffc1ba.png)

配置字典：相关的配置字典信息，可通过右上角“创建按钮”新增新配置

![](asserts/dc1ba3422e29d3d5d057ddeac5329d45.png)

![](asserts/fd2a552a6542f8249fe60a13d8cfb970.png)

密文：密文的相关信息包含名称，是否不可修改，类型等，可通过右上角“创建按钮”新增密文

![](asserts/f005f5cdfd9442a9d7662852ce92c31f.png)

![](asserts/99f0cbf3f196ba8f03b462141ba651ec.png)

自动伸缩：通过配置自动伸缩实现根据应用程序的负载情况自动调整其数量。当应用程序的负载增加时，自动伸缩会自动添加新的实例来处理请求，以保持应用程序的高可用性。当负载减少时，自动伸缩会自动删除一些实例以节省成本。

![](asserts/1e4114d50b85112f013547d90f0f3bf1.png)

![](asserts/198108777e6cb1b4627a5e03b70dfab7.png)

服务：K8S集群上部署的各类服务，包含它们的名称、类型、集群IP、端口等信息

![](asserts/f7ffa30c08e71a808f49ff393c2023df.png)

![](asserts/de13220da3310cf99d1643143c2250ea.png)

端点：数据流的起点和终点，包含IP地址、端口等信息

![](asserts/a71a07395723ff975dd949f7d797d2d9.png)

路由：通过网络将信息正确传输到指定目的地的方式，包含IngressClass、 Hosts等信息

![](asserts/85a7f41139b86b44a89985c334577d11.png)

![](asserts/a64aeccf7f50259e14ac45ecd38a17c6.png)

命名空间：将一组名称与其他名称分隔开的方式，它可以避免不同程序中相同名称的变量或函数发生冲突

![](asserts/86589fca959c8968f1ee1a217698c11c.png)

![](asserts/78309c460ee73a31c5244abd52b41823.png)

自定义资源：包含Group, Version, Kind, Scope等信息

![](asserts/89f7df0bc8450877b59b7f5016fa9b5f.png)

 

# 6. 服务定义

服务定义功能，主要是对应用系统以及应用系统下的服务节点进行操作。在服务部署以及日常维护的过程中，经常需要与服务定义功能模块进行交互。这一节就对服务定义模块进行介绍，需要说明的是，从图聆获取的能力版本，大多都已经将服务定义配置好，只需要在页面进行导入即可。如果业务线想要将自身的服务托管到skynet3.0平台上，可以按照该使用手册进行添加。

 

## 6.1 页面介绍

首先对服务定义页面进行整体介绍。通过点击左侧的导航栏中的“服务定义”进入到服务定义页面，在服务定义显示区的上侧是集群功能区，包括集群属性配置、集群日志配置、导入以及刷新。左下方显示的是应用系统的列表，展示已经定义好的所用应用系统。右下方显示的是应用系统的功能区，包括应用系统的服务列表、资源仓库、系统属性以及系统日志。

![](asserts/fcf53392c684bbe3e827a8737c9c9a0b.png)
 

## 6.2 集群配置

在集群功能区，包含集群属性配置、集群日志配置、导入等集群级配置相关功能。通过点击属性配置弹出“集群-属性配置”框，左侧是属性内容，右侧是“编辑”、“刷新”、“工具”等按键的功能区

![](asserts/dc8355e72062777c8fbead687f5e9b78.png)

点击“刷新”后，左侧内容区即可进行编辑，并且右侧的“编辑”按钮变为“保存”按钮，可以点击“取消”取消本次编辑操作。需要重点说明的是，集群属性定义的值，在系统属性以及服务属性都可以通过可用系统变量占位符号或者自定义的属性引用，如 \${my.prop.key}，所以在集群级属性配置里面，尽量配置那些会进行统一更改的属性，如数据库配置等信息。

![](asserts/115bd424300fd5483216f6ff97546181.png)

点击“工具”，会弹出辅助工具弹出框，该弹出框主要是为了辅助配置项的填写，其具体功能介绍，可以参考第8节内容。

![](asserts/39d32939348e24eaf80e7f6e73830e9e.png)

点击“保存”后，会对更新情况进行弹出框提示

![](asserts/600b8eeb19e234a92c29985665d949b8.png)

日志配置是对集群的整个日志级别进行配置，因为不同服务节点的命名空间不同，所以集群日志配置默认的日志配置内容较少。右侧工具栏和上述属性配置一致，就不再进行介绍。

![](asserts/f0bdfd57feba465e34fb2355cb738205.png)

导入功能是对已有的配置进行导入，以便不同服务器之间的配置迁移，也方便快速部署。

 

## 6.3 应用系统

所有的服务节点都有应用系统，一个应用系统包含一个或者多个服务节点。在服务定义左侧展示的是应用系统列表，下面是应用系统界面说明

![](asserts/23ef699ed422002b6a6bc0e768d76011.png)

在筛选区输入搜索关键词，然后点击![](asserts/67c9793b9f1dd607b100a8d123434196.png)图标，即可根据输入内容，搜索出相匹配的应用系统，并在展示区进行展示

![](asserts/f7538cc406b56b52653b08f810b72dc1.png)

点击匹配的应用系统，可以在页面右侧看到该应用系统的所有服务节点，并展示所有节点的相关属性：服务名、编码、类型、协议、端口、分类、是否启用NG网关、是否启动监控。

![](asserts/1ec980f795fba799278930c322bea690.png)

 

## 6.4 服务节点操作



### 6.4.1 新建服务节点

首先选择将服务节点添加到哪个应用系统中，然后点击该应用系统右侧的“新建”按钮。

![](asserts/6a34674c620f6685768d401b8d2cf0f2.png)

在弹出的页面上，左边是服务节点的配置信息。右边是预定义的环境变量，在配置服务节点时，可以引用这些环境变量。下面是在定义服务节点是需要的工具。

![](asserts/1e471dcd53b35a070bcac81874038efc.png)

新建服务节点需要配置左侧六项信息：基本配置、关联文件、模板文件、功能选项、属性配置、日志配置。

1、基本配置：

在该页面配置服务节点的基础配置，如：服务名称、编码等等，用于区别该应用系统中的其他服务节点。“\*”为必填项。

![](asserts/485a119faf1f7ac6dacd37bdc38ebdc5.png)

2、关联文件：

点击左侧的“新增”按钮，出现需要填写的“文件名称”和“目标目录”，文件名称为要关联的文件名称，一般为服务包名称、引擎包名称、配置文件等等。目标目录为需要将前面定义的文件名称部署包拷贝到什么地方解压，一般填写\${SKYNET_PLUGIN_HOME}即可， \${SKYNET_PLUGIN_HOME}这个变量代表的是skynet/plugin/系统名称目录/ ,如果前面定义的文件名称部署包是一个配置文件，则需要填写该配置文件具体要放置的目录位置（绝对路径）。

如果有多个文件需要关联，则继续点击“新增”即可。

![](asserts/1d46568203ed57cd715c80174a541f23.png)

3、模板文件：

点击侧的“新增”按钮，出现需要填写的文件路径、文件编码、文件权限（默认读写）、归属用户、文件内容。用于存放一些配置文件，若无需要可以不进行配置。![](asserts/1b0b0446b92e0cd808bd1782679d4991.png)

模板文件内容会在做属性变量替换后写入如下配置的文件路径，如MySQL服务模板文件：

会在服务启动时删除“skyline“数据库,并创建一个新的“skyline“数据库

![](asserts/fc4fc5e9749e159fdfc443fce68e9323.png)

如下是数据库的配置文件，服务启动时会自动配置好相应属性。

![](asserts/b33e2d3798c1b5d0d36b85d8d68e2195.png)

模板文件是通过使用\${SKYNET_EXT_CONFIG_ROOT}占位符在启动时完成。
![C:\\Users\\<USER>\\AppData\\Roaming\\IFLY\\localfiles\\images\\2023\\7\\a07a7eb89324bc2a535d4fc3f50854bb.png](asserts/712da3af32d1116bc09933528bd0ca75.png)

4、功能选项：

如果引擎有首页需要展示，则打开“设定首页”的按钮，并这是首页的URL路径。

![](asserts/19b82977d89b033946c8de32b771a24e.png)

同理，选择是否进行“日志收集”，主要收集服务运行过程中产生的日志。

选择是否进行启用监控采集服务，并填写prometheus采集点配置。

![](asserts/5a15841987ca7d00d7958ee072fe580d.png)

选择是否启用nginx网关，并填写服务别名。

![](asserts/e4613b9f73934e71a8060196434aadb3.png)

其他配置同理

![](asserts/50d2e28bb619906112966d8e4adb9095.png)

5、属性配置

属性配置主要是为了配置服务节点的服务级属性，同时也提供系统级和集群级属性配置的入口（请参照上文介绍进行配置）可用系统变量占位符号或者自定义的属性引用，如 \${my.prop.key}，

![](asserts/2529e0d14a8733815926f2359ead97e8.png)

6、日志配置

同属性配置一样，日志配置是为了配置服务节点的服务级日志，同时也提供系统级和集群级日志配置的入口（请参照上文介绍进行配置）。

![](asserts/c4ea7a15904d500e1652f2d69806690c.png)

所有的配置项配置完成之后，需要进行保存。

![](asserts/a9ec229958885279df22ed31dd680608.png)

该工具栏还提供一些辅助工具。

![](asserts/c6afb37b634cc806719c32782668875c.png)

### 6.4.2 编辑-删除服务节点

可对已存在的服务节点进行编辑和删除操作，如下图所示。

![](asserts/2a9bcabbc50c841a959e0bd3953ed36c.png)


# 7. 集群监控

集群的监控信息，需要启动Skynet监控/仪表盘方可进行查看。除了可以查看集群的整体监控信息外，还可以查看不同主机的运行信息。



## 7.1 集群概述

用图表的方式展示集群的基本信息

![](asserts/4a8e6d69e1d4379ec1d761b0c07df29a.png)



## 7.2 信息摘要

详细展示了每台服务器的基本信息,如IP、CPU逻辑核数、系统平均负载[15min]/核数、内容容量等

![](asserts/0ff86d7248384ccf920dfede9bd7828c.png)

以及每台机器的磁盘读写、网卡信息、连接数等

![](asserts/1c6f5f3e9fe14d7b7b355090196dfe2b.png)
