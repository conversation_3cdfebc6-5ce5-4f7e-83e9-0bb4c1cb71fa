//package skynet;
//
//import java.util.Timer;
//import java.util.TimerTask;
//
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.client.RestTemplate;
//
//import skynet.boot.config.FastJsonHttpMessageConverter;
//import skynet.boot.health.ActionHealthData;
//import skynet.boot.logging.AntLogger;
//import skynet.boot.core.domain.AntNodeState;
//import skynet.boot.core.domain.AntWorkerDownObserver;
//
//public class AntWorkerHealth<PERSON>hecker implements AutoCloseable {
//
//	private static final Logger LOGGER = LoggerFactory.getLogger(AntWorkerHealthChecker.class);
//
//	public static final String SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS = "skynet.action.health.checker.interval.seconds";
//	public static final int SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS_DEFAULT = 60;
//
//	public static final String SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT = "skynet.action.health.checker.error.count";
//	public static final int SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT_DEFAULT = 5;
//
//	private AntNodeState targetAntNodeState;
//	private AntWorkerDownObserver antWorkerDownObserver;
//	private int testErrorCount = 0;
//	private int interval = SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS_DEFAULT;
//	private int errorCount = SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT_DEFAULT;
//	private Timer timer;
//
//	public AntWorkerHealthChecker(AntNodeState targetAntNodeState, AntWorkerDownObserver antWorkerDownObserver) {
//		if (antWorkerDownObserver == null) {
//			throw new IllegalArgumentException("antWorkerDownObserver is null");
//		}
//		this.targetAntNodeState = targetAntNodeState;
//		this.antWorkerDownObserver = antWorkerDownObserver;
//		this.interval = getValue(SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS, SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS_DEFAULT);
//		this.errorCount = getValue(SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT, SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT_DEFAULT);
//
//		log.infof("%s: %d", SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS, interval);
//		log.infof("%s: %d", SKYNET_ACTION_HEALTH_CHECKER_ERROR_COUNT, errorCount);
//	}
//
//	private static int getValue(String key, int defaultValue) {
//		String intervalStr = System.getProperty(key);
//		if (StringUtils.isNotBlank(intervalStr) && StringUtils.isNumeric(intervalStr)) {
//			return Integer.valueOf(intervalStr);
//		}
//		return defaultValue;
//	}
//
//	public void start() {
//
//		// 如果为0.就不启用 AntWorkerHealthChecker
//		if (this.interval <= 0) {
//			log.infof("[%s: %d]. not check antworker health.", SKYNET_ACTION_HEALTH_CHECKER_INTERVAL_SECONDS, interval);
//			return;
//		}
//
//		log.infof("started antworker [%s] health checker.", targetAntNodeState.actionTitle);
//		// 定时请求 AntWorker的health 状态，如果请求超时或服务状态为down，认为 AntWorker服务不可用，服务僵死，直接KILL，
//		String healthUri = String.format("http%s://127.0.0.1:%d/skynet/health", (targetAntNodeState.ssl ? "s" : ""), targetAntNodeState.port);
//		RestTemplate restTemplate = new RestTemplate(FastJsonHttpMessageConverter.getConverters().getConverters());
//
//		TimerTask timerTask = new TimerTask() {
//			@Override
//			public void run() {
//				try {
//					LOGGER.debugf("get antworker health:%s", healthUri);
//					ResponseEntity<ActionHealthData> healthStatus = restTemplate.getForEntity(healthUri, ActionHealthData.class);
//					LOGGER.debugf("get antworker health result:%s", healthStatus.getStatusCode());
//					if (healthStatus.getStatusCode() == HttpStatus.OK) {
//						LOGGER.debugf("healthStatus:%s", healthStatus);
//						try {
//							if (healthStatus.getBody().isUp()) {
//								testErrorCount = 0;
//							} else {
//								LOGGER.errorf("healthStatus:%s", healthStatus);
//								testErrorCount++;
//							}
//						} catch (NullPointerException e) {
//						}
//					} else {
//						LOGGER.errorf("healthStatus:%s", healthStatus);
//						testErrorCount = Integer.MAX_VALUE;
//					}
//				} catch (Exception e1) {
//					LOGGER.error(String.format("quest health [%s] error", healthUri) + e1.getMessage(), e1);
//					testErrorCount = Integer.MAX_VALUE;
//				}
//
//				if (testErrorCount >= errorCount) {
//					// 访问失败，认为服务不可用
//					log.infof("close the antworker [%s] the port:%d. [testErrorCount:%d > errorCount:%d] ", //
//							targetAntNodeState.actionTitle, targetAntNodeState.port, testErrorCount, errorCount);
//					antWorkerDownObserver.onDown();
//					timer.cancel();
//				}
//			}
//		};
//		this.timer = new Timer();
//		this.timer.schedule(timerTask, interval * 1000, interval * 1000);
//	}
//
//	@Override
//	public void close() throws Exception {
//		if (this.timer != null) {
//			this.timer.cancel();
//		}
//	}
//}
