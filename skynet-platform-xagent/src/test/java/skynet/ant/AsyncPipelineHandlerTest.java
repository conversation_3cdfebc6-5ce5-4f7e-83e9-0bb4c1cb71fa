package skynet.ant;

import skynet.boot.common.AsyncPipelineHandler;

public class AsyncPipelineHandlerTest {

    public static void main(String[] args) throws Exception {

        testfun();

        testMem();

        Thread.sleep(1000000);

    }

    static void testfun() throws Exception {
        MyPipelineHandler handler = new MyPipelineHandler();
        handler.init(10, "test pararm");

        for (int i = 0; i < 20; i++) {
            handler.onData("data\t" + i);
            System.err.println(" todo size\t" + handler.getTodoSize());
        }

        System.err.println(" on data end.========");
        Thread.sleep(50 * 1000);
        handler.close();

    }

    static void testMem() throws Exception {

        for (int index = 0; index < 10000; index++) {

            AsyncPipelineHandler<String, Object> handler = new AsyncPipelineHandler<String, Object>() {

                @Override
                public void onEvent(String event) throws Exception {
                    System.out.println("onEvent event:\t" + event);
                    Thread.sleep(100);

                }
            };

            handler.init(10, "test pararm");

            for (int i = 0; i < 20; i++) {
                handler.onData("data\t" + i);

            }

            System.err.println(" on data end.========");

            handler.close();

        }
    }
}

class MyPipelineHandler extends AsyncPipelineHandler<String, Object> {

    @Override
    protected void onInit(Object param) throws Exception {
        System.err.println("init param\t" + param);
    }

    @Override
    public void onEvent(String event) throws Exception {
        System.out.println("onEvent event:\t" + event);
        Thread.sleep(1000);
    }

    @Override
    protected void onClose() throws Exception {
        System.err.println("onClose");
    }
}
