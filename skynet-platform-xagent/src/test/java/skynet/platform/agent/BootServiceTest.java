package skynet.platform.agent;

import com.alibaba.fastjson2.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.platform.agent.core.BootService;
import skynet.platform.common.domain.BootProfile;
import skynet.platform.common.domain.BootStatus;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class BootServiceTest {
    //
    static {
        // Action服务名称
        System.setProperty("skynet.current.action", "ant-demo-v10@ant");
        System.setProperty("skynet.current.ip", "127.0.0.1");
        System.setProperty("server.port", "2201");
        // 必须设置 true
        System.setProperty("skynet.test.enabled", "true");
    }

    // 根据业务测试需求进行注解
    @Autowired
    private BootService bootService;

    /**
     * 具体的业务测试
     *
     * @throws Exception
     */
    @Test
    public void test() throws Exception {

        String json = "{'index':0,'ip':'***********','port':0,'pid':0,'name':'[基础服务]托管测试服务','subIndex':0,'aid':'ant-demo-v20@ant','param':'ant-demo-v20@ant','code':'ant-demo-v20','plugin':'ant','enable':true,'sport':7230,'up':'OFFLINE'}";
        BootProfile bootProfile = JSON.parseObject(json, BootProfile.class);
        BootStatus bootStatus = bootService.start(bootProfile);

        System.out.println(bootStatus.toString());

    }

}