package skynet.platform;

import skynet.boot.common.Guid;
import skynet.boot.common.pool.ObjectPool2;

import java.util.ArrayList;
import java.util.List;

public class ObjectPoolTest {

    public static void main(String[] args) throws Exception {

        int poolSize = 2;
        int loopCount = 10;
        final ObjectPool2<Worker> workerPool = new ObjectPool2<>(poolSize, index -> new Worker());

        List<Thread> threads = new ArrayList<>();

        // for (int j = 0; j < 10; j++) {
        // Worker worker = workerPool.borrowObject();
        // worker.run(j);
        // }

        for (int j = 0; j < loopCount; j++) {
            Thread t = new Thread(() -> {

                for (int i = 0; i < 10; i++) {

                    Worker worker = workerPool.borrowObject();

                    try {
                        if (worker != null) {
                            worker.run(i);
                        }

                        workerPool.returnObject(worker);
                    } catch (Exception e) {
                        System.err.println("error: \t" + e.getMessage());
                    }
                }

            });
            t.start();
            threads.add(t);
        }
        for (Thread thread : threads) {
            thread.join();
        }

        workerPool.close();

        System.out.println("------------end---------------");

    }
}

class Worker implements AutoCloseable {

    private String id = null;

    public Worker() {
        id = Guid.randomGuid();
    }

    public void run(int index) {
        System.out.println(String.format("worker run. id:\t%s \tindex:%d", id, index));
    }

    @Override
    public void close() throws Exception {
        System.out.println(String.format("close id:\t%s", id));

    }
}
