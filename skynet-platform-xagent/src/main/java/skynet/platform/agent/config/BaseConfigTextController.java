package skynet.platform.agent.config;

/**
 * <AUTHOR>
 * @date 2020/10/18 14:18
 */

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import skynet.boot.SkynetProperties;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuilder;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class BaseConfigTextController {
    private final BootEnvironmentBuilder bootEnvironmentBuilder;

    public BaseConfigTextController(BootEnvironmentBuilder bootEnvironmentBuilder) {
        this.bootEnvironmentBuilder = bootEnvironmentBuilder;
    }

    @RequestMapping(value = "/{action:.+}", method = RequestMethod.GET, produces = {MediaType.TEXT_PLAIN_VALUE})
    public String getProps(@PathVariable("action") String actionPoint, //
                           @RequestParam(value = "actionId", required = false) String actionId, //
                           @RequestParam(value = "debug", required = false, defaultValue = "false") boolean isDebug) throws Exception {

        log.debug("get.action.props. action={};debug={}", actionPoint, isDebug);
        return this.build(actionPoint, actionId, 0, isDebug).toPropLines();
    }

    @RequestMapping(value = "/{action:.+}/{key:.+}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<?> getPropsx(@PathVariable("action") String actionPoint, @PathVariable("key") String key) throws Exception {
        log.debug("get.action.prop. action={};key={};", actionPoint, key);

        Map<String, Object> map = this.build(actionPoint, actionPoint, 0, false);

        if (map.containsKey(key)) {
            Map<String, Object> tmpMap = new HashMap<>(1);
            tmpMap.put(key, map.get(key));
            return new ResponseEntity<Object>(tmpMap, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(String.format("No such prop[%s]", key), HttpStatus.NOT_FOUND);
        }
    }

    private BootEnvironment build(String actionPoint, String actionId, int port, boolean isDebug) throws Exception {

        SkynetProperties skynetProperties = new SkynetProperties(bootEnvironmentBuilder.getEnvironment());
        skynetProperties.setActionPoint(actionPoint);
        skynetProperties.setActionId(actionId);
        skynetProperties.setPort(port);
        skynetProperties.setDebugMode(isDebug);

        BootEnvironment bootEnvironment = bootEnvironmentBuilder.build(skynetProperties);
        return bootEnvironment.replacePlaceholder();
    }
}