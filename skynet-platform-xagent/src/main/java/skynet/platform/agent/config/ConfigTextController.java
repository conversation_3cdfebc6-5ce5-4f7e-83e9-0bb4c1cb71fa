package skynet.platform.agent.config;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.env.BootEnvironmentBuilder;

/**
 * 授权：base auth 模式，增加用户名和密码。
 * <p>
 * 场景：docker中的python 调用，
 *
 * <AUTHOR>
 */
@ExposeSwagger2
@RestController
@RequestMapping(value = "/skynet/config", method = RequestMethod.GET)
public class ConfigTextController extends BaseConfigTextController {

    public ConfigTextController(BootEnvironmentBuilder bootEnvironmentBuilder) {
        super(bootEnvironmentBuilder);
    }
}