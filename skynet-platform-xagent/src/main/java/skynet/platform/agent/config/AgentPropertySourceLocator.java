package skynet.platform.agent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;
import skynet.boot.SkynetProperties;
import skynet.platform.agent.server.AntServerApp;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.config.SkynetPropertySourceLocator;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.repository.config.IAntConfigService;

/**
 * 从配置中心获取 PropertySourceLocator
 *
 * <AUTHOR> [2018年8月22日 上午8:37:28]
 */
@Slf4j
public class AgentPropertySourceLocator extends SkynetPropertySourceLocator {

    @Override
    protected PropertySource<?> onLocate(Environment environment, SkynetProperties skynetProperties, IAntConfigService antConfigService) throws Exception {
        BootEnvironment bootEnvironment = AppBootEnvironment.getBootEnvironment(environment, antConfigService, skynetProperties);
        return new MapPropertySource(skynetProperties.getActionCode(), bootEnvironment.replacePlaceholder());
    }

    @Override
    protected boolean condition(Environment environment, SkynetProperties skynetProperties) {
        return AntServerApp.ACTION_POINT.equals(skynetProperties.getActionPoint());
    }
}