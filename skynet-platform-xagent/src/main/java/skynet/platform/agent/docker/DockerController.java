package skynet.platform.agent.docker;

import com.github.dockerjava.api.DockerClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * xxxx
 *
 * <AUTHOR> QQ：408365330
 * @date $date$
 */
@Slf4j
@RestController
@RequestMapping(value = "/skynet/agent/docker")
@AutoConfigureAfter(DockerConfig.class)
public class DockerController {

    @Autowired(required = false)
    private DockerClient dockerClient;

    @GetMapping("/{containerId}/info")
    public Object getNodestatus(@PathVariable("containerId") String containerId) {
        log.debug("get docker {} info", containerId);
        if (dockerClient == null) {
            return "docker is not  install";
        }
        return dockerClient.inspectContainerCmd(containerId).exec();
    }
}
