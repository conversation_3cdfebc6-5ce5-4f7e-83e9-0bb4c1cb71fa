package skynet.platform.agent.grafana.loader;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;

public class DatasourceLoader {

    @Getter
    private final String templatePath;

    private final Map<String, String> replacements;

    public DatasourceLoader(String templatePath) {
        this(templatePath, Collections.emptyMap());
    }

    public DatasourceLoader(String templatePath, Map<String, String> replacements) {
        this.templatePath = templatePath;
        this.replacements = replacements;
    }

    /**
     * 加载原始字符串
     *
     * @return
     * @throws IOException
     */
    private String loadRaw() throws IOException {
        ClassLoader classLoader = DatasourceLoader.class.getClassLoader();
        if (classLoader == null) {
            throw new IOException("The  DatasourceLoader.class.getClassLoader() is null.");
        }
        try (InputStream in = classLoader.getResourceAsStream(templatePath)) {
            assert in != null;
            return IOUtils.toString(in, StandardCharsets.UTF_8);
        }
    }

    public Datasource load() throws IOException {
        String afterReplacement = loadRaw();
        if (replacements != null && !replacements.isEmpty()) {
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                String value = (entry.getValue() == null) ? "" : entry.getValue();
                afterReplacement = afterReplacement.replace(entry.getKey(), value);
            }
        }
        JSONObject jsonObj = JSON.parseObject(afterReplacement);
        String name = jsonObj.getString("name");
        return new Datasource(name, afterReplacement, jsonObj);
    }

}
