package skynet.platform.agent.grafana.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Table extends Jsonable {

    private List<TableColumn> columns = new ArrayList<>(0);
    private List<Object> rows = new ArrayList<>(0);
    private String type = QueryTarget.TABLE;

    public void addColumn(TableColumn col) {
        this.columns.add(col);
    }

    public void clearColumn() {
        this.columns.clear();
    }

    public void addRow(Object[] row) {
        this.rows.add(row);
    }

    public void addRow(List<Object> row) {
        this.rows.add(row);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}