package skynet.platform.agent.grafana.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
public class QueryTarget extends Jsonable {

    public static final String TABLE = "table";

    private String target;
    private String type;
    private String refId;

    @Override
    public String toString() {
        return super.toString();
    }

}
