package skynet.platform.agent.grafana.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Setter
@Getter
public class ClusterSummary extends Jsonable {

    private int serverNodeNum;
    private int cpuCores;
    private double cpuUsePerc;
    private double avgLoad15m;
    private long memTotal;
    private long memUsed;
    private double memUsePerc;
    private int gpuNum;
    private long gpuMemTotal;
    private long gpuMemUsed;
    private double gpuMemUsePerc;
    private double gpuUsePerc;

}
