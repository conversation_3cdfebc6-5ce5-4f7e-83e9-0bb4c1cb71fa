package skynet.platform.agent.grafana.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;

public class ParsedTarget {

    @Getter
    private final String target;
    private JSONObject attributes;

    public String getAttribute(String key) {
        return (this.attributes == null) ? null : this.attributes.getString(key);
    }

    public Integer getIntAttribute(String key) {
        return (this.attributes == null) ? null : this.attributes.getInteger(key);
    }

    private ParsedTarget(String target) {
        this.target = target;
        this.attributes = new JSONObject();
    }

    public static ParsedTarget parse(String targetInQuery) {
        int colonIndex = targetInQuery.indexOf(':');
        if (colonIndex < 0) {
            return new ParsedTarget(targetInQuery);
        }
        ParsedTarget ret = new ParsedTarget(targetInQuery.substring(0, colonIndex));
        String attributesString = targetInQuery.substring(colonIndex + 1).replace("\\", "");
        ret.attributes = JSON.parseObject(attributesString);
        return ret;
    }
}
