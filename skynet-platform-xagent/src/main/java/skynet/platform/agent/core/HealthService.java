package skynet.platform.agent.core;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import skynet.platform.agent.core.config.AgentProperties;
import skynet.platform.agent.core.domain.HealthResult;
import skynet.platform.agent.core.domain.ObservableQueue;
import skynet.platform.agent.core.health.*;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.domain.HealthParam;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * 不健康就直接 STOP
 * </pre>
 *
 * <AUTHOR> [2018年7月7日 下午11:34:08]
 */
@Slf4j
@Service
public class HealthService {

    /**
     * key: Aid+pid, value: HealthCheckHandler
     */
    private final Map<String, HealthCheckHandler> handlers;
    private final AgentProperties agentProperties;
    private final BootService bootService;
    private final ScheduledService scheduledService;
    private final ApplicationContext springContext;

    public HealthService(MeterRegistry meterRegistry, AgentProperties agentProperties, BootService bootService, ScheduledService scheduledService, ApplicationContext springContext) {
        this.agentProperties = agentProperties;
        this.bootService = bootService;
        this.scheduledService = scheduledService;
        this.springContext = springContext;
        this.handlers = new ConcurrentHashMap<>();
        meterRegistry.gauge("skynet.agent.health.checker.size", handlers, Map::size);
    }


    @PostConstruct
    public void start() {
        // 启动定时器
        ScheduledExecutorService es = Executors.newScheduledThreadPool(1, runnable -> {
            Thread thread = new Thread(runnable);
            thread.setName("ant.health");
            return thread;
        });
        es.scheduleWithFixedDelay(() -> {
            try {
                if (scheduledService.isStarted()) {
                    refresh();
                }
            } catch (Exception e) {
                log.error("Refresh error", e);
            }
        }, agentProperties.getInitialDelay(), agentProperties.getDelay(), TimeUnit.SECONDS);
    }

    private synchronized void refresh() throws Exception {

        // 移除多余的健康检查器
        Collection<BootStatus> bootList = bootService.getAllBoot().values();

        // 寻找本地的所有进程，重建健康检查
        log.trace("RestoreHealthCheck bootList len={};handlers={}={}", bootList.size(), handlers.size(), handlers.keySet().toArray());
        for (BootStatus bootStatus : bootList) {

            if (bootStatus.getProfile().getPid() <= 0) {
                continue;
            }

            // 排除 启动中的服务
            String key = bootStatus.getUUID();
            log.trace("key={}", key);

            if (this.handlers.containsKey(key)) {
                // 检测服务注册中心上的 服务是否掉线
                bootService.checkZkOffline(bootStatus);
                continue;
            }

            // 所有的服务都有健康检测
            HealthListener healthListener = new HealthListener() {
                @Override
                public void onConnected(BootStatus bootStatus) throws Exception {
                }

                @Override
                public void onClosed(BootStatus bootStatus) throws Exception {
                    log.debug("[port={}] remove key={} begin..", bootStatus.getProfile().getPort(), bootStatus.getUUID());
                    HealthCheckHandler handler = handlers.remove(bootStatus.getUUID());
                    if (handler != null) {
                        handler.close();
                    }
                    log.debug("[port={}] remove key={} end.", bootStatus.getProfile().getPort(), bootStatus.getUUID());
                }
            };

            HealthCheckHandler healthCheckHandler = getHealthCheckHandler(bootStatus);
            healthCheckHandler.start(bootStatus, agentProperties.getOutCacheLineCount(), healthListener);

            log.info("[port={}]put key={}", bootStatus.getProfile().getPort(), key);
            this.handlers.put(key, healthCheckHandler);
        }

        log.trace("checkPid..");
        for (HealthCheckHandler handler : this.handlers.values()) {
            handler.checkPid();
        }
    }

    private HealthCheckHandler getHealthCheckHandler(BootStatus bootStatus) {
        HealthCheckHandler healthCheckHandler;

        // 所有的服务都有健康检测
        HealthParam healthParam = bootStatus.getAction().getBootParam().getHealthParam();

        if (healthParam != null && healthParam.isCheckEnabled()) {
            // 以服务的协议为主
            String protocol = bootStatus.getAction().getBootParam().getActionProtocol();
            protocol = StringUtils.isBlank(protocol) ? "" : protocol.trim();

            if ("HTTP".equalsIgnoreCase(protocol)) {
                healthCheckHandler = springContext.getBean(HealthCheckHandler4Http.class);
            } else {
                healthCheckHandler = springContext.getBean(HealthCheckHandler4Tcp.class);
            }
        } else {
            // 如果没有开启健康检测，采用缺省 依赖PID检测
            healthCheckHandler = springContext.getBean(HealthCheckHandler4Pid.class);
        }
        return healthCheckHandler;
    }

//    public List<HealthResult> getResultsAndRemove(String aid) {
//        List<HealthResult> list = new ArrayList<>();
//        for (HealthCheckHandler handler : handlers.values()) {
//            if (handler.getAid().equals(aid)) {
//                list.addAll(handler.getResults().getListAndRemove());
//            }
//        }
//        return list;
//    }

    public List<HealthResult> getResults(String aid) {
        List<HealthResult> list = new ArrayList<>();
        for (HealthCheckHandler handler : handlers.values()) {
            if (handler.getAid().equals(aid)) {
                list.addAll(handler.getResults().getList());
            }
        }
        return list;
    }

    public ObservableQueue<HealthResult> getQueue(String aid) {
        for (HealthCheckHandler handler : handlers.values()) {
            if (handler.getAid().equals(aid)) {
                return handler.getResults();
            }
        }
        return null;
    }

    public Map<String, String> getCheckHandlers() {
        Map<String, String> map = new TreeMap<>();
        for (Entry<String, HealthCheckHandler> item : handlers.entrySet()) {
            map.put(item.getKey(), String.valueOf(item.getValue()));
        }
        return map;
    }

}
