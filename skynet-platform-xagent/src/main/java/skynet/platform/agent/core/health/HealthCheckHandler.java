package skynet.platform.agent.core.health;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import skynet.platform.agent.core.BootService;
import skynet.platform.agent.core.core.EventService;
import skynet.platform.agent.core.domain.HealthResult;
import skynet.platform.agent.core.domain.ObservableQueue;
import skynet.platform.common.domain.BootProfile;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.domain.HealthParam;
import skynet.platform.common.utils.ProcessUtils;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年11月15日 下午4:47:04]
 */
@Slf4j
public abstract class HealthCheckHandler implements AutoCloseable {
    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("ant.health.t-%d").build();
    /**
     * -- GETTER --
     *
     * @return the results
     */
    @Getter
    ObservableQueue<HealthResult> results;
    BootProfile bootProfile;
    BootStatus bootStatus;
    @Autowired
    private EventService eventService;
    @Autowired
    private BootService bootService;
    private ScheduledExecutorService timer;

    private HealthListener myHealthListener;
    private volatile boolean isFirstCheck = true;

    protected abstract void onStart(HealthParam healthParam, HealthListener myHealthListener, EventService eventService) throws Exception;

    protected abstract void check() throws Exception;

    protected abstract void onClose() throws Exception;

    public void start(BootStatus bootStatus, int outCacheLineCount, HealthListener healthListener) throws Exception {

        this.bootStatus = bootStatus;
        this.results = new ObservableQueue<>(outCacheLineCount);
        this.bootProfile = bootStatus.getProfile();
        this.myHealthListener = new HealthListener() {
            @Override
            public void onConnected(BootStatus bootStatus) throws Exception {

                if (isFirstCheck) {
                    log.info("HealthLinker onConnected [{}:{}] Set [{}] UP begin..", bootProfile.getIp(), bootProfile.getPort(), bootStatus.getAid());
                    // 上线：将BootStatus 设置UP状态
                    bootService.setUp(bootProfile.getAid());

                    if (healthListener != null) {
                        healthListener.onConnected(bootStatus);
                    }

                    log.info("HealthLinker onConnected [{}:{}] Set [{}] UP end.", bootProfile.getIp(), bootProfile.getPort(), bootStatus.getAid());
                    isFirstCheck = false;
                }
            }

            @Override
            public void onClosed(BootStatus bootStatus) throws Exception {
                log.info("HealthLinker onClosed  [{}:{}] Stop [{}] begin...", bootProfile.getIp(), bootProfile.getPort(), bootStatus.getAid());
                // 掉线：清理当前BootStatus, 根据Pid，防止 计划服务 已经启动了
                bootService.setDown(bootProfile.getPid());

                try {
                    if (healthListener != null) {
                        healthListener.onClosed(bootStatus);
                    }
                } catch (Exception e) {
                    log.error("healthListener onClosed error " + e.getMessage(), e);
                }

                close();

                try {
                    HealthResult ret = new HealthResult();
                    ret.setAid(bootProfile.getAid());
                    ret.setPid(bootProfile.getPid());
                    ret.setMessage(String.format("[Port:%s]HealthLinker Closed.", bootProfile.getPort()));
                    ret.setFrom(this.getClass().getSimpleName());

                    // // 报告事件
                    eventService.putHealthCheckEvent(ret);
                    results.put(ret);
                } catch (Exception e) {
                    log.error("put HealthResult error " + e.getMessage(), e);
                }

                log.info("HealthLinker onClosed  [{}:{}] Stop [{}] end.", bootProfile.getIp(), bootProfile.getPort(), bootStatus.getAid());
            }
        };

        HealthParam healthParam = bootStatus.getAction().getBootParam().getHealthParam();
        // 如果没有配置 就采用缺省的健康检测参数，
        if (healthParam == null) {
            healthParam = new HealthParam();
        }

        onStart(healthParam, myHealthListener, eventService);

        ScheduledExecutorService timer = Executors.newScheduledThreadPool(1, THREAD_FACTORY);
        timer.scheduleWithFixedDelay(() -> {
            try {
                check();
            } catch (Exception e) {
                log.error("Check Error", e);
            }
        }, Math.max(healthParam.getInitialDelaySeconds(), 10), Math.max(healthParam.getIntervalSeconds(), 15), TimeUnit.SECONDS);
        this.timer = timer;
    }


    public void checkPid() {
        try {
            log.debug("CheckPid, pid={}", bootProfile.getPid());
            // 排除启动中进程
            if (bootProfile.getPid() > 0) {
                boolean isNeedClose = false;
                if (!ProcessUtils.exist(bootProfile.getPid())) {
                    isNeedClose = true;
                    log.warn("The action={} pid={} is not exist. clean the boot status.", bootProfile.getAid(), bootProfile.getPid());
                } else if (!bootService.isRunning(bootProfile.getAid())) {
                    isNeedClose = true;
                    log.warn("The action={} running operating environment invalid. clean the boot status.[PID={}]", bootProfile.getAid(), bootProfile.getPid());
                }
                if (isNeedClose) {
                    try {
                        myHealthListener.onClosed(bootStatus);

                    } catch (Exception e) {
                        log.error(String.format("check %s pid not exist. onClosed ERROR:%s", bootProfile.getAid(), e.getMessage()), e);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("checkPid {}, err={}", bootProfile.getAid(), e.getMessage());
        }
    }

    public String getAid() {
        return bootProfile.getAid();
    }

    @Override
    public final void close() throws Exception {
        // 清空输出
        try {
            this.results.clear();
        } catch (Exception ignored) {
        }

        if (timer != null) {
            timer.shutdownNow();
            timer = null;
        }
        onClose();
    }

}
