package skynet.platform.agent.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.codehaus.plexus.util.cli.CommandLineException;
import org.codehaus.plexus.util.cli.Commandline;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class NumaUtils {

    private static final String REGEX = "available: (\\d+) nodes.*";

    private static Integer nodeCount;

    public static int nodeCount() {
        synchronized (NumaUtils.class) {
            if (nodeCount == null) {
                nodeCount = __nodeCount();
            }
        }
        return nodeCount;
    }

    private static int __nodeCount() {
        int ret = -1;
        Commandline cmdline = new Commandline("numactl -H");
        try {
            Process p = cmdline.execute();
            List<String> lines = IOUtils.readLines(p.getInputStream());
            if (p.waitFor() == 0 && lines != null && !lines.isEmpty()) {
                String firstLine = lines.getFirst();
                log.trace("numactl -H = {}", firstLine);
                Pattern pattern = Pattern.compile(REGEX);
                Matcher m = pattern.matcher(firstLine);
                if (m.matches() && m.groupCount() == 1) {
                    String nodeCount = m.group(1);
                    log.trace("nodeCount = {}", nodeCount);
                    ret = Integer.parseInt(nodeCount.trim());
                }
            }
        } catch (CommandLineException e) {
            log.trace("", e);
        } catch (Exception e) {
            log.error("", e);
        }
        return ret;
    }

    public static void main(String[] args) {
        Pattern pattern = Pattern.compile(REGEX);
        Matcher m = pattern.matcher("available: 4 nodes");

        if (m.matches()) {
            System.out.printf("group count : %d\n", m.groupCount());
            String nodeCount = m.group(1);
            log.trace("nodeCount = {}", nodeCount);
        } else {
            System.out.println("not match");
        }
    }
}
