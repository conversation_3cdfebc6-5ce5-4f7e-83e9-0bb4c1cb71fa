package skynet.platform.agent.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.Date;

@Getter
@Setter
public class HealthResult extends Jsonable {

    private long pid;
    private String aid;
    @JSONField(format = "MM-dd HH:mm:ss")
    private Date time = new Date();
    private boolean ok;
    private String endpoint;
    private String message;
    private int code;
    private long responseTime;
    private String from;

    @Override
    public String toString() {
        return super.toString();
    }
}
