package skynet.platform.agent.core.update;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.SkynetProperties;
import skynet.boot.common.utils.MD5Util;
import skynet.platform.common.domain.UpdateParam;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 本地文件操作类
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年10月11日 下午5:45:23]
 */
@Slf4j
@Service
public class LocalFileUtils {

    private final SkynetProperties skynetProperties;

    public LocalFileUtils(SkynetProperties skynetProperties) {
        this.skynetProperties = skynetProperties;
    }

    public void setUpdateParam(String actionPoint, List<UpdateParam> thisUpdateParamList) throws IOException {
        File file = Paths.get(skynetProperties.getHome(), "tmp", String.format("%s.repofile", actionPoint)).toFile();
        FileUtils.write(file, JSON.toJSONString(thisUpdateParamList), StandardCharsets.UTF_8);
    }

    public List<UpdateParam> getUpdateParamList(String actionPoint) throws IOException {
        File file = Paths.get(skynetProperties.getHome(), "tmp", String.format("%s.repofile", actionPoint)).toFile();
        if (!file.exists()) {
            log.info("the file [{}] is not existe", file);
            return new ArrayList<>(0);
        }
        String json = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
        if (StringUtils.isBlank(json)) {
            log.info("the file [{}] is empty", file);
            return new ArrayList<>(0);
        }
        return JSON.parseObject(json, new TypeReference<List<UpdateParam>>() {
        });
    }

    /**
     * <pre>
     * 文件不存在，将返回null
     * </pre>
     *
     * @param targetDir
     * @param targetFileName
     * @return
     * @throws IOException
     */
    public UpdateFile getUpdateFile(File targetDir, String targetFileName) throws IOException {
        UpdateFile updateFile = new UpdateFile(targetDir, targetFileName);

        if (!updateFile.getTargetFile().exists()) {
            return null;
        }
        UpdateFileInfo updateFileInfo = new UpdateFileInfo(targetFileName);
        String fileMd5;
        if (updateFile.getMd5File().exists()) {
            fileMd5 = FileUtils.readFileToString(updateFile.getMd5File(), StandardCharsets.UTF_8);
        } else {
            // 如果不存在，先创建md5文件
            fileMd5 = MD5Util.getFileMd5String(updateFile.getTargetFile());
            FileUtils.write(updateFile.getMd5File(), fileMd5, StandardCharsets.UTF_8);
        }
        updateFileInfo.setMd5(fileMd5);// 本地文件MD5

        // if (updateFile.getTimestampFile().exists()) {
        // updateFileInfo.setLastModified(Files.readFileStringContent(updateFile.getTimestampFile()));// 本地文件MD5
        // }
        updateFile.setLocalUpdateFileInfo(updateFileInfo);
        return updateFile;
    }
}
