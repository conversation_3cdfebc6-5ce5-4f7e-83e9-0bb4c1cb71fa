package skynet.platform.agent.core.update;

import lombok.Getter;
import lombok.Setter;

import java.io.File;

@Getter
public class UpdateFile {
    /**
     * -- GETTER --
     *
     * @return the targetFile
     */
    private final File targetFile;
    /**
     * -- GETTER --
     *
     * @return the md5File
     */
    // private File timestampFile;
    private final File md5File;

    /**
     * -- SETTER --
     * <p>
     * <p>
     * -- GETTER --
     *
     * @param remoteUpdateFileInfo the remoteUpdateFileInfo to set
     * @return the remoteUpdateFileInfo
     */
    @Setter
    private UpdateFileInfo remoteUpdateFileInfo;
    /**
     * -- SETTER --
     * <p>
     * <p>
     * -- GETTER --
     *
     * @param localUpdateFileInfo the localUpdateFileInfo to set
     * @return the localUpdateFileInfo
     */
    @Setter
    private UpdateFileInfo localUpdateFileInfo;

    public UpdateFile(File targetDir, String targetFileName) {
        this.targetFile = new File(targetDir, targetFileName);
        // this.timestampFile = new File(targetDir, String.format("%s.%s", targetFileName, "lastModified"));
        this.md5File = new File(targetDir, String.format("%s.%s", targetFileName, "md5"));
    }

    // /**
    // * @return the timestampFile
    // */
    // public File getTimestampFile() {
    // return timestampFile;
    // }

}
