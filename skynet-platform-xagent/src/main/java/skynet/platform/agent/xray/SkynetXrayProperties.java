package skynet.platform.agent.xray;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import skynet.boot.common.domain.Jsonable;

/**
 * 系统性能计数器配置
 *
 * <AUTHOR> [Oct 11, 2017 11:40:06 AM]
 */
@Getter
@Component
@RefreshScope
public class SkynetXrayProperties extends Jsonable {

    @Value("${skynet.xray.gpu.query.string:index,name,serial,utilization.gpu,utilization.memory,memory.total,memory.free,memory.used,power.draw,power.limit,fan.speed,temperature.gpu,compute_mode,clocks.current.graphics,clocks.current.sm,clocks.current.memory,clocks.current.video,pstate,clocks_throttle_reasons.gpu_idle}")
    @JSONField(ordinal = 60)
    private String gpuQueryItemString;

    @Override
    public String toString() {
        return super.toString();
    }
}
