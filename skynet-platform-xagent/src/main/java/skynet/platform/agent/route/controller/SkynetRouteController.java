package skynet.platform.agent.route.controller;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.route.OnlineActionController;

/**
 * Web服务节点路由
 *
 * <pre>
 *     缺省禁用
 * </pre>
 *
 * <AUTHOR> [2018年4月8日 下午3:08:46]
 */
@ExposeSwagger2
@RestController
@RequestMapping("/skynet/route")
@ConditionalOnProperty(value = "skynet.endpoint.route.enabled")
public class SkynetRouteController extends OnlineActionController {

    public SkynetRouteController(OnlineActionManager onlineActionManager, IAntConfigService antConfigService) {
        super(onlineActionManager, antConfigService);
    }
}
