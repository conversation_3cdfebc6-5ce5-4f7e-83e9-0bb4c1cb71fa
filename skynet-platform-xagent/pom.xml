<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-platform</artifactId>
        <version>${skynet-platform.vision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>skynet-platform-xagent</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <jna.version>5.5.0</jna.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-platform-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-platform-feign</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java</artifactId>
            <version>${docker-java.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>docker-java-transport-jersey</artifactId>
                    <groupId>com.github.docker-java</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>docker-java-transport-netty</artifactId>
                    <groupId>com.github.docker-java</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-transport-httpclient5</artifactId>
            <version>${docker-java.version}</version>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>false</addClasspath>
                            <mainClass>skynet.platform.agent.Bootstrap</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>